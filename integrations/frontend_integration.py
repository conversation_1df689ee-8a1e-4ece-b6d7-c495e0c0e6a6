"""
Frontend integration for AI Voice Mate
Handles frontend WebSocket connections and converts legacy messages to protocol events
"""

import asyncio
import json
import time
import uuid
from typing import Dict, Any, Optional, List
import websockets
from websockets.exceptions import ConnectionClosedError

from .base_integration import BaseIntegration, IntegrationConfig, IntegrationStatus, CallMetadata
from tutor.executors.user import User
from tutor.modules.logger import logger
from tutor.modules.websocket import (
    EventType, ConnectedEvent, StartEvent, MediaEvent, StopEvent,
    MarkEvent, ClearEvent, DTMFEvent, StreamManager, AudioCodec
)

# Define StreamMetadata class for the integration
class StreamMetadata:
    def __init__(self, streamSid: str, accountSid: str, callSid: str,
                 from_number: str, to_number: str, mediaFormat: dict,
                 customParameters: dict = None):
        self.streamSid = streamSid
        self.accountSid = accountSid
        self.callSid = callSid
        self.from_number = from_number
        self.to_number = to_number
        self.mediaFormat = mediaFormat
        self.customParameters = customParameters or {}


class FrontendIntegration(BaseIntegration):
    """Integration for frontend WebSocket connections"""
    
    def __init__(self, config: IntegrationConfig, logger=None):
        super().__init__(config, logger)
        
        # Frontend-specific configuration
        self.websocket_server = None
        self.active_users: List[User] = []
        self.user_sessions: Dict[str, User] = {}  # session_id -> User
        self.session_streams: Dict[str, str] = {}  # session_id -> stream_id
        
        # Protocol event handlers for frontend-specific events
        self.frontend_event_handlers = {
            "user_register": self._handle_user_register,
            "call_start": self._handle_call_start,
            "call_end": self._handle_call_end
        }
    
    def validate_config(self) -> List[str]:
        """Validate frontend-specific configuration"""
        errors = super().validate_config()
        
        # Frontend integration doesn't require API keys
        # but needs endpoint configuration
        if not self.config.endpoint_url:
            errors.append("WebSocket endpoint URL is required for frontend integration")
        
        return errors
    
    async def connect(self) -> bool:
        """Connect frontend integration (start WebSocket server)"""
        try:
            self.status = IntegrationStatus.CONNECTING
            
            # Parse endpoint URL to get host and port
            if self.config.endpoint_url:
                # Extract host and port from URL like "ws://0.0.0.0:5010"
                url_parts = self.config.endpoint_url.replace("ws://", "").replace("wss://", "")
                if ":" in url_parts:
                    host, port = url_parts.split(":")
                    port = int(port)
                else:
                    host = url_parts
                    port = 5010
            else:
                host = "0.0.0.0"
                port = 5010
            
            # Start WebSocket server for frontend connections
            self.websocket_server = await websockets.serve(
                self._handle_websocket_connection,
                host,
                port
            )
            
            self.status = IntegrationStatus.CONNECTED
            
            if self.logger:
                self.logger.info(f"Frontend integration WebSocket server started on {host}:{port}")
            
            return True
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error starting frontend integration: {e}")
            if self.on_error:
                self.on_error(e)
            self.status = IntegrationStatus.ERROR
            return False
    
    async def disconnect(self) -> None:
        """Disconnect frontend integration"""
        try:
            # End all active calls
            for session_id in list(self.user_sessions.keys()):
                await self._cleanup_user_session(session_id)
            
            # Close WebSocket server
            if self.websocket_server:
                self.websocket_server.close()
                await self.websocket_server.wait_closed()
                self.websocket_server = None
            
            self.status = IntegrationStatus.DISCONNECTED
            
            if self.logger:
                self.logger.info("Frontend integration disconnected")
                
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error disconnecting frontend integration: {e}")
    
    async def start_call(self, to_number: str, from_number: str = None, **kwargs) -> str:
        """Start a new call (not applicable for frontend integration)"""
        # Frontend integration doesn't initiate calls, it receives them
        raise NotImplementedError("Frontend integration doesn't initiate calls")
    
    async def end_call(self, call_id: str) -> None:
        """End an active call"""
        try:
            # Find session by call_id
            session_id = None
            for sid, stream_id in self.session_streams.items():
                if stream_id == call_id:
                    session_id = sid
                    break
            
            if session_id:
                await self._cleanup_user_session(session_id)
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error ending call {call_id}: {e}")
    
    async def send_audio(self, call_id: str, audio_data: bytes) -> None:
        """Send audio data to frontend"""
        try:
            # Find user by call_id (stream_id)
            user = self._find_user_by_stream_id(call_id)
            if user and user.conn:
                # Send audio as protocol media event
                media_event = MediaEvent(
                    streamSid=call_id,
                    sequenceNumber=user.sequence_counter,
                    media={
                        "track": "outbound",
                        "chunk": str(user.chunk_counter),
                        "timestamp": str(int(time.time() * 1000)),
                        "payload": self.audio_codec.encode_audio_to_base64(audio_data)
                    }
                )
                
                await user.conn.send(media_event.to_json())
                user.sequence_counter += 1
                user.chunk_counter += 1
                
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error sending audio to frontend: {e}")
    
    async def send_dtmf(self, call_id: str, digits: str) -> None:
        """Send DTMF tones to frontend"""
        try:
            user = self._find_user_by_stream_id(call_id)
            if user and user.conn:
                for digit in digits:
                    dtmf_event = DTMFEvent(
                        streamSid=call_id,
                        sequenceNumber=user.sequence_counter,
                        dtmf={"digit": digit}
                    )
                    await user.conn.send(dtmf_event.to_json())
                    user.sequence_counter += 1
                    
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error sending DTMF to frontend: {e}")
    
    def _find_user_by_session(self, session_id: str) -> Optional[User]:
        """Find user by session ID"""
        return self.user_sessions.get(session_id)
    
    def _find_user_by_stream_id(self, stream_id: str) -> Optional[User]:
        """Find user by stream ID"""
        for user in self.active_users:
            if user.stream_id == stream_id:
                return user
        return None
    
    async def _handle_websocket_connection(self, websocket, path):
        """Handle incoming WebSocket connection from frontend"""
        try:
            if self.logger:
                self.logger.info(f"New frontend WebSocket connection from {websocket.remote_address}")
            
            async for message in websocket:
                try:
                    data = json.loads(message)
                    
                    # Check if this is a protocol event or frontend-specific event
                    if "event" in data:
                        event_type = data.get("event")

                        # Handle frontend-specific events
                        if event_type in self.frontend_event_handlers:
                            await self.frontend_event_handlers[event_type](websocket, data)
                        else:
                            # Standard protocol-compliant event
                            await self._handle_protocol_event(websocket, data)
                    else:
                        # Unknown message format
                        if self.logger:
                            self.logger.warning(f"Unknown message format: {data}")
                        await websocket.send(json.dumps({
                            "type": "error",
                            "message": "Unknown message format. Please use protocol-compliant events."
                        }))
                        
                except json.JSONDecodeError as e:
                    if self.logger:
                        self.logger.error(f"JSON decode error: {e}")
                    await websocket.send(json.dumps({"type": "error", "message": "Invalid JSON format"}))
                except Exception as e:
                    if self.logger:
                        self.logger.error(f"Error handling message: {e}")
                    await websocket.send(json.dumps({"type": "error", "message": "Error processing message"}))
                    
        except ConnectionClosedError:
            if self.logger:
                self.logger.info("Frontend WebSocket connection closed")
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error in frontend WebSocket connection: {e}")
    
    async def _handle_protocol_event(self, websocket, event_data: Dict[str, Any]) -> None:
        """Handle protocol-compliant events"""
        try:
            event_type = event_data.get("event")
            
            if event_type == EventType.CONNECTED.value:
                # Send connected response
                connected_event = ConnectedEvent()
                await websocket.send(connected_event.to_json())
                
            elif event_type == EventType.MEDIA.value:
                # Handle media event
                stream_sid = event_data.get("streamSid")
                user = self._find_user_by_stream_id(stream_sid)
                if user:
                    media = event_data.get("media", {})
                    payload_b64 = media.get("payload", "")
                    if payload_b64:
                        audio_data = self.audio_codec.decode_audio_from_base64(payload_b64)
                        user.audio_processor.add_protocol_audio(audio_data)
                        
                        # Update stats
                        chunk = int(media.get("chunk", 0))
                        timestamp = int(media.get("timestamp", 0))
                        user.update_media_stats(chunk, timestamp)
                        
            elif event_type == EventType.DTMF.value:
                # Handle DTMF event
                stream_sid = event_data.get("streamSid")
                user = self._find_user_by_stream_id(stream_sid)
                if user:
                    dtmf = event_data.get("dtmf", {})
                    digit = dtmf.get("digit", "")
                    user.handle_dtmf_input(digit)
                    
            elif event_type == EventType.MARK.value:
                # Handle mark event
                stream_sid = event_data.get("streamSid")
                user = self._find_user_by_stream_id(stream_sid)
                if user:
                    mark = event_data.get("mark", {})
                    mark_name = mark.get("name", "")
                    user.handle_mark_completion(mark_name)
                    
            elif event_type == EventType.CLEAR.value:
                # Handle clear event
                stream_sid = event_data.get("streamSid")
                user = self._find_user_by_stream_id(stream_sid)
                if user:
                    user.clear_audio_buffer()
                    
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error handling protocol event: {e}")

    async def _handle_user_register(self, websocket, data: Dict[str, Any]) -> None:
        """Handle user registration using protocol events"""
        try:
            session_id = data.get("sessionId")
            user_data = data.get("userData", {})

            if not session_id:
                await websocket.send(json.dumps({
                    "event": "error",
                    "message": "Session ID is required"
                }))
                return

            # Check if user already exists
            existing_user = self._find_user_by_session(session_id)
            if existing_user:
                # Remove existing user
                try:
                    self.active_users.remove(existing_user)
                    if existing_user.session in self.user_sessions:
                        del self.user_sessions[existing_user.session]
                except ValueError:
                    pass

            # Create new user
            new_user = User(
                session=session_id,
                name=user_data.get("name", "Unknown"),
                websocket=websocket,
                data=user_data,
                event_loop=asyncio.get_event_loop(),
                active_users=self.active_users
            )

            # Add to tracking
            self.active_users.append(new_user)
            self.user_sessions[session_id] = new_user

            # Send connected event as confirmation
            connected_event = ConnectedEvent()
            await websocket.send(connected_event.to_json())

            if self.logger:
                self.logger.info(f"User registered: {session_id} - {user_data.get('name')}")

        except Exception as e:
            if self.logger:
                self.logger.error(f"Error handling user registration: {e}")
            await websocket.send(json.dumps({
                "event": "error",
                "message": "Error registering user"
            }))

    async def _handle_call_start(self, websocket, data: Dict[str, Any]) -> None:
        """Handle call start using protocol events"""
        try:
            session_id = data.get("sessionId")
            call_config = data.get("callConfig", {})
            user = self._find_user_by_session(session_id)

            if not user:
                await websocket.send(json.dumps({
                    "event": "error",
                    "message": "User not found. Please register user first."
                }))
                return

            # Create call metadata
            call_metadata = CallMetadata(
                call_id=f"frontend_call_{session_id}_{int(time.time())}",
                from_number=call_config.get("from", "frontend"),
                to_number=call_config.get("to", "ai_assistant"),
                platform="frontend",
                start_time=time.time(),
                custom_data={
                    "session_id": session_id,
                    "call_config": call_config,
                    "custom_parameters": call_config.get("customParameters", {})
                }
            )

            # Track the call
            self.active_calls[call_metadata.call_id] = call_metadata

            # Create protocol stream
            stream_id = self.create_protocol_stream(call_metadata)
            user.set_stream_id(stream_id)
            self.session_streams[session_id] = stream_id

            # Create stream metadata for the start event
            stream_metadata = StreamMetadata(
                streamSid=stream_id,
                accountSid="frontend_account",
                callSid=call_metadata.call_id,
                from_number=call_metadata.from_number,
                to_number=call_metadata.to_number,
                mediaFormat={
                    "encoding": "mulaw",
                    "sampleRate": 8000,
                    "bitRate": 64000,
                    "bitDepth": 8
                },
                customParameters=call_metadata.custom_data.get("custom_parameters", {})
            )

            # Send start event to frontend
            start_event = StartEvent(
                streamSid=stream_id,
                sequenceNumber=user.sequence_counter,
                start=stream_metadata
            )
            await websocket.send(start_event.to_json())
            user.sequence_counter += 1

            # Start AI call with protocol support
            user.start_ai_call_with_protocol()

            # Notify callback
            if self.on_call_started:
                self.on_call_started(call_metadata)

            if self.logger:
                self.logger.info(f"AI call started for session {session_id}, stream {stream_id}")

        except Exception as e:
            if self.logger:
                self.logger.error(f"Error handling call start: {e}")
            await websocket.send(json.dumps({
                "event": "error",
                "message": "Error starting AI call"
            }))

    async def _handle_call_end(self, websocket, data: Dict[str, Any]) -> None:
        """Handle call end using protocol events"""
        try:
            session_id = data.get("sessionId")
            reason = data.get("reason", "User ended call")
            user = self._find_user_by_session(session_id)

            if user and user.stream_id:
                # Send stop event
                stop_event = StopEvent(
                    streamSid=user.stream_id,
                    sequenceNumber=user.sequence_counter,
                    stop={
                        "accountSid": "frontend_account",
                        "callSid": f"frontend_call_{session_id}",
                        "reason": reason
                    }
                )
                await websocket.send(stop_event.to_json())

                # Clean up session
                await self._cleanup_user_session(session_id)

                if self.logger:
                    self.logger.info(f"AI call ended for session {session_id}, reason: {reason}")
            else:
                await websocket.send(json.dumps({
                    "event": "error",
                    "message": "No active call found"
                }))

        except Exception as e:
            if self.logger:
                self.logger.error(f"Error handling call end: {e}")



    async def _cleanup_user_session(self, session_id: str) -> None:
        """Clean up user session and associated resources"""
        try:
            user = self._find_user_by_session(session_id)
            if user:
                # End call if active
                stream_id = self.session_streams.get(session_id)
                if stream_id:
                    call_metadata = None
                    for call_id, metadata in self.active_calls.items():
                        if self.call_streams.get(call_id) == stream_id:
                            call_metadata = metadata
                            break

                    if call_metadata:
                        await self.handle_call_ended(call_metadata.call_id, "Session cleanup")

                    # Clean up stream mapping
                    if session_id in self.session_streams:
                        del self.session_streams[session_id]

                # Remove user from tracking
                try:
                    self.active_users.remove(user)
                except ValueError:
                    pass

                if session_id in self.user_sessions:
                    del self.user_sessions[session_id]

                # Clean up user resources
                await user.cleanup()

                if self.logger:
                    self.logger.info(f"Cleaned up session: {session_id}")

        except Exception as e:
            if self.logger:
                self.logger.error(f"Error cleaning up session {session_id}: {e}")

    def get_platform_capabilities(self) -> Dict[str, bool]:
        """Get frontend platform capabilities"""
        return {
            "audio_streaming": True,
            "dtmf_support": True,
            "call_recording": True,
            "video_support": False,
            "conference_calls": False,
            "call_transfer": False,
            "call_hold": False,
            "text_input": True,
            "voice_input": True,
            "legacy_compatibility": True
        }
