"""
Frontend integration for AI Voice Mate
Handles frontend WebSocket connections and converts legacy messages to protocol events
"""

import asyncio
import json
import time
import uuid
from typing import Dict, Any, Optional, List
import websockets
from websockets.exceptions import ConnectionClosedError

from .base_integration import BaseIntegration, IntegrationConfig, IntegrationStatus, CallMetadata
from tutor.executors.user import User
from tutor.modules.logger import logger
from tutor.modules.websocket import (
    EventType, ConnectedEvent, StartEvent, MediaEvent, StopEvent,
    MarkEvent, ClearEvent, DTMFEvent, StreamManager, AudioCodec
)


class FrontendIntegration(BaseIntegration):
    """Integration for frontend WebSocket connections"""
    
    def __init__(self, config: IntegrationConfig, logger=None):
        super().__init__(config, logger)
        
        # Frontend-specific configuration
        self.websocket_server = None
        self.active_users: List[User] = []
        self.user_sessions: Dict[str, User] = {}  # session_id -> User
        self.session_streams: Dict[str, str] = {}  # session_id -> stream_id
        
        # Legacy message handlers
        self.legacy_handlers = {
            "store_user": self._handle_store_user,
            "start_ai_call": self._handle_start_ai_call,
            "end_ai_call": self._handle_end_ai_call,
            "text_input": self._handle_text_input,
            "voice_action_stop": self._handle_voice_action_stop,
            "ai_start_listening": self._handle_ai_start_listening,
            "audio_chunk": self._handle_audio_chunk,
            "request_active_users": self._handle_request_active_users,
            "start_accumulate": self._handle_accumulate,
            "resume_accumulate": self._handle_accumulate
        }
    
    def validate_config(self) -> List[str]:
        """Validate frontend-specific configuration"""
        errors = super().validate_config()
        
        # Frontend integration doesn't require API keys
        # but needs endpoint configuration
        if not self.config.endpoint_url:
            errors.append("WebSocket endpoint URL is required for frontend integration")
        
        return errors
    
    async def connect(self) -> bool:
        """Connect frontend integration (start WebSocket server)"""
        try:
            self.status = IntegrationStatus.CONNECTING
            
            # Parse endpoint URL to get host and port
            if self.config.endpoint_url:
                # Extract host and port from URL like "ws://0.0.0.0:5010"
                url_parts = self.config.endpoint_url.replace("ws://", "").replace("wss://", "")
                if ":" in url_parts:
                    host, port = url_parts.split(":")
                    port = int(port)
                else:
                    host = url_parts
                    port = 5010
            else:
                host = "0.0.0.0"
                port = 5010
            
            # Start WebSocket server for frontend connections
            self.websocket_server = await websockets.serve(
                self._handle_websocket_connection,
                host,
                port
            )
            
            self.status = IntegrationStatus.CONNECTED
            
            if self.logger:
                self.logger.info(f"Frontend integration WebSocket server started on {host}:{port}")
            
            return True
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error starting frontend integration: {e}")
            if self.on_error:
                self.on_error(e)
            self.status = IntegrationStatus.ERROR
            return False
    
    async def disconnect(self) -> None:
        """Disconnect frontend integration"""
        try:
            # End all active calls
            for session_id in list(self.user_sessions.keys()):
                await self._cleanup_user_session(session_id)
            
            # Close WebSocket server
            if self.websocket_server:
                self.websocket_server.close()
                await self.websocket_server.wait_closed()
                self.websocket_server = None
            
            self.status = IntegrationStatus.DISCONNECTED
            
            if self.logger:
                self.logger.info("Frontend integration disconnected")
                
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error disconnecting frontend integration: {e}")
    
    async def start_call(self, to_number: str, from_number: str = None, **kwargs) -> str:
        """Start a new call (not applicable for frontend integration)"""
        # Frontend integration doesn't initiate calls, it receives them
        raise NotImplementedError("Frontend integration doesn't initiate calls")
    
    async def end_call(self, call_id: str) -> None:
        """End an active call"""
        try:
            # Find session by call_id
            session_id = None
            for sid, stream_id in self.session_streams.items():
                if stream_id == call_id:
                    session_id = sid
                    break
            
            if session_id:
                await self._cleanup_user_session(session_id)
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error ending call {call_id}: {e}")
    
    async def send_audio(self, call_id: str, audio_data: bytes) -> None:
        """Send audio data to frontend"""
        try:
            # Find user by call_id (stream_id)
            user = self._find_user_by_stream_id(call_id)
            if user and user.conn:
                # Send audio as protocol media event
                media_event = MediaEvent(
                    streamSid=call_id,
                    sequenceNumber=user.sequence_counter,
                    media={
                        "track": "outbound",
                        "chunk": str(user.chunk_counter),
                        "timestamp": str(int(time.time() * 1000)),
                        "payload": self.audio_codec.encode_audio_to_base64(audio_data)
                    }
                )
                
                await user.conn.send(media_event.to_json())
                user.sequence_counter += 1
                user.chunk_counter += 1
                
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error sending audio to frontend: {e}")
    
    async def send_dtmf(self, call_id: str, digits: str) -> None:
        """Send DTMF tones to frontend"""
        try:
            user = self._find_user_by_stream_id(call_id)
            if user and user.conn:
                for digit in digits:
                    dtmf_event = DTMFEvent(
                        streamSid=call_id,
                        sequenceNumber=user.sequence_counter,
                        dtmf={"digit": digit}
                    )
                    await user.conn.send(dtmf_event.to_json())
                    user.sequence_counter += 1
                    
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error sending DTMF to frontend: {e}")
    
    def _find_user_by_session(self, session_id: str) -> Optional[User]:
        """Find user by session ID"""
        return self.user_sessions.get(session_id)
    
    def _find_user_by_stream_id(self, stream_id: str) -> Optional[User]:
        """Find user by stream ID"""
        for user in self.active_users:
            if user.stream_id == stream_id:
                return user
        return None
    
    async def _handle_websocket_connection(self, websocket, path):
        """Handle incoming WebSocket connection from frontend"""
        try:
            if self.logger:
                self.logger.info(f"New frontend WebSocket connection from {websocket.remote_address}")
            
            async for message in websocket:
                try:
                    data = json.loads(message)
                    
                    # Check if this is a protocol event or legacy message
                    if "event" in data:
                        # Protocol-compliant event - handle directly
                        await self._handle_protocol_event(websocket, data)
                    else:
                        # Legacy message format - convert to protocol
                        await self._handle_legacy_message(websocket, data)
                        
                except json.JSONDecodeError as e:
                    if self.logger:
                        self.logger.error(f"JSON decode error: {e}")
                    await websocket.send(json.dumps({"type": "error", "message": "Invalid JSON format"}))
                except Exception as e:
                    if self.logger:
                        self.logger.error(f"Error handling message: {e}")
                    await websocket.send(json.dumps({"type": "error", "message": "Error processing message"}))
                    
        except ConnectionClosedError:
            if self.logger:
                self.logger.info("Frontend WebSocket connection closed")
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error in frontend WebSocket connection: {e}")
    
    async def _handle_protocol_event(self, websocket, event_data: Dict[str, Any]) -> None:
        """Handle protocol-compliant events"""
        try:
            event_type = event_data.get("event")
            
            if event_type == EventType.CONNECTED.value:
                # Send connected response
                connected_event = ConnectedEvent()
                await websocket.send(connected_event.to_json())
                
            elif event_type == EventType.MEDIA.value:
                # Handle media event
                stream_sid = event_data.get("streamSid")
                user = self._find_user_by_stream_id(stream_sid)
                if user:
                    media = event_data.get("media", {})
                    payload_b64 = media.get("payload", "")
                    if payload_b64:
                        audio_data = self.audio_codec.decode_audio_from_base64(payload_b64)
                        user.audio_processor.add_protocol_audio(audio_data)
                        
                        # Update stats
                        chunk = int(media.get("chunk", 0))
                        timestamp = int(media.get("timestamp", 0))
                        user.update_media_stats(chunk, timestamp)
                        
            elif event_type == EventType.DTMF.value:
                # Handle DTMF event
                stream_sid = event_data.get("streamSid")
                user = self._find_user_by_stream_id(stream_sid)
                if user:
                    dtmf = event_data.get("dtmf", {})
                    digit = dtmf.get("digit", "")
                    user.handle_dtmf_input(digit)
                    
            elif event_type == EventType.MARK.value:
                # Handle mark event
                stream_sid = event_data.get("streamSid")
                user = self._find_user_by_stream_id(stream_sid)
                if user:
                    mark = event_data.get("mark", {})
                    mark_name = mark.get("name", "")
                    user.handle_mark_completion(mark_name)
                    
            elif event_type == EventType.CLEAR.value:
                # Handle clear event
                stream_sid = event_data.get("streamSid")
                user = self._find_user_by_stream_id(stream_sid)
                if user:
                    user.clear_audio_buffer()
                    
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error handling protocol event: {e}")

    async def _handle_legacy_message(self, websocket, data: Dict[str, Any]) -> None:
        """Handle legacy message format and convert to protocol events"""
        try:
            message_type = data.get("type")

            if message_type in self.legacy_handlers:
                await self.legacy_handlers[message_type](websocket, data)
            else:
                if self.logger:
                    self.logger.warning(f"Unknown legacy message type: {message_type}")
                await websocket.send(json.dumps({
                    "type": "error",
                    "message": f"Unknown message type: {message_type}"
                }))

        except Exception as e:
            if self.logger:
                self.logger.error(f"Error handling legacy message: {e}")

    async def _handle_store_user(self, websocket, data: Dict[str, Any]) -> None:
        """Handle legacy store_user message"""
        try:
            session_id = data.get("session")
            user_data = data.get("data", {})

            if not session_id:
                await websocket.send(json.dumps({
                    "type": "error",
                    "message": "Session ID is required"
                }))
                return

            # Check if user already exists
            existing_user = self._find_user_by_session(session_id)
            if existing_user:
                # Remove existing user
                try:
                    self.active_users.remove(existing_user)
                    if existing_user.session in self.user_sessions:
                        del self.user_sessions[existing_user.session]
                except ValueError:
                    pass

            # Create new user
            new_user = User(
                session=session_id,
                name=user_data.get("name", "Unknown"),
                websocket=websocket,
                data=user_data,
                event_loop=asyncio.get_event_loop(),
                active_users=self.active_users
            )

            # Add to tracking
            self.active_users.append(new_user)
            self.user_sessions[session_id] = new_user

            # Send success response
            await websocket.send(json.dumps({
                "type": "store_user",
                "data": "user added successfully"
            }))

            if self.logger:
                self.logger.info(f"User stored: {session_id} - {user_data.get('name')}")

        except Exception as e:
            if self.logger:
                self.logger.error(f"Error handling store_user: {e}")
            await websocket.send(json.dumps({
                "type": "error",
                "message": "Error storing user"
            }))

    async def _handle_start_ai_call(self, websocket, data: Dict[str, Any]) -> None:
        """Handle legacy start_ai_call message and convert to protocol stream"""
        try:
            session_id = data.get("session")
            user = self._find_user_by_session(session_id)

            if not user:
                await websocket.send(json.dumps({
                    "type": "error",
                    "message": "User not found. Please store user first."
                }))
                return

            # Create call metadata
            call_metadata = CallMetadata(
                call_id=f"frontend_call_{session_id}_{int(time.time())}",
                from_number=user.mobile or "frontend",
                to_number="ai_assistant",
                platform="frontend",
                start_time=time.time(),
                custom_data={"session_id": session_id, "user_data": data}
            )

            # Track the call
            self.active_calls[call_metadata.call_id] = call_metadata

            # Create protocol stream
            stream_id = self.create_protocol_stream(call_metadata)
            user.set_stream_id(stream_id)
            self.session_streams[session_id] = stream_id

            # Send start event to frontend
            metadata = self.stream_manager.get_stream_metadata(stream_id)
            if metadata:
                sequence_num = self.stream_manager.get_next_sequence_number(stream_id)
                start_event = StartEvent(sequence_num, metadata)
                await websocket.send(start_event.to_json())

                # Start AI call with protocol support
                user.start_ai_call_with_protocol()

                # Notify callback
                if self.on_call_started:
                    self.on_call_started(call_metadata)

                if self.logger:
                    self.logger.info(f"AI call started for session {session_id}, stream {stream_id}")
            else:
                await websocket.send(json.dumps({
                    "type": "error",
                    "message": "Failed to create stream"
                }))

        except Exception as e:
            if self.logger:
                self.logger.error(f"Error handling start_ai_call: {e}")
            await websocket.send(json.dumps({
                "type": "error",
                "message": "Error starting AI call"
            }))

    async def _handle_end_ai_call(self, websocket, data: Dict[str, Any]) -> None:
        """Handle legacy end_ai_call message"""
        try:
            session_id = data.get("session")
            user = self._find_user_by_session(session_id)

            if user and user.stream_id:
                # Send stop event
                stop_event = StopEvent(
                    streamSid=user.stream_id,
                    sequenceNumber=user.sequence_counter,
                    stop={"reason": "User ended call"}
                )
                await websocket.send(stop_event.to_json())

                # Clean up session
                await self._cleanup_user_session(session_id)

                if self.logger:
                    self.logger.info(f"AI call ended for session {session_id}")
            else:
                await websocket.send(json.dumps({
                    "type": "error",
                    "message": "No active call found"
                }))

        except Exception as e:
            if self.logger:
                self.logger.error(f"Error handling end_ai_call: {e}")

    async def _handle_text_input(self, websocket, data: Dict[str, Any]) -> None:
        """Handle legacy text_input message"""
        try:
            session_id = data.get("session")
            text_data = data.get("data", "")
            user = self._find_user_by_session(session_id)

            if user:
                await user.process_text(text_data)
            else:
                await websocket.send(json.dumps({
                    "type": "error",
                    "message": "User not found"
                }))

        except Exception as e:
            if self.logger:
                self.logger.error(f"Error handling text_input: {e}")

    async def _handle_voice_action_stop(self, websocket, data: Dict[str, Any]) -> None:
        """Handle legacy voice_action_stop message"""
        try:
            session_id = data.get("session")
            voice_data = data.get("data", "")
            user = self._find_user_by_session(session_id)

            if user:
                await user.process_voice(voice_data)
            else:
                await websocket.send(json.dumps({
                    "type": "error",
                    "message": "User not found"
                }))

        except Exception as e:
            if self.logger:
                self.logger.error(f"Error handling voice_action_stop: {e}")

    async def _handle_ai_start_listening(self, websocket, data: Dict[str, Any]) -> None:
        """Handle legacy ai_start_listening message"""
        try:
            session_id = data.get("session")
            user = self._find_user_by_session(session_id)

            if user:
                user.ai_start_listening = True
                if self.logger:
                    self.logger.info(f"AI start listening enabled for session {session_id}")
            else:
                await websocket.send(json.dumps({
                    "type": "error",
                    "message": "User not found"
                }))

        except Exception as e:
            if self.logger:
                self.logger.error(f"Error handling ai_start_listening: {e}")

    async def _handle_audio_chunk(self, websocket, data: Dict[str, Any]) -> None:
        """Handle legacy audio_chunk message and convert to protocol media event"""
        try:
            session_id = data.get("session")
            audio_data = data.get("data", [])
            user = self._find_user_by_session(session_id)

            if user and user.stream_id:
                # Convert audio data to bytes
                audio_bytes = bytes([x & 0xFF for x in audio_data])

                # Convert to protocol format
                try:
                    protocol_audio = self.audio_codec.convert_audio_to_protocol_format(
                        audio_bytes,
                        source_sample_rate=data.get("sampleRate", 44100),
                        source_channels=data.get("channels", 1),
                        source_sample_width=data.get("sampleWidth", 2)
                    )
                    user.audio_processor.add_protocol_audio(protocol_audio)
                except Exception as e:
                    if self.logger:
                        self.logger.error(f"Error converting audio to protocol format: {e}")
                    # Fallback to original processing
                    user.audio_processor.audio_queue.append(audio_bytes)
            else:
                if not user:
                    await websocket.send(json.dumps({
                        "type": "error",
                        "message": "User not found"
                    }))
                else:
                    await websocket.send(json.dumps({
                        "type": "error",
                        "message": "No active stream"
                    }))

        except Exception as e:
            if self.logger:
                self.logger.error(f"Error handling audio_chunk: {e}")

    async def _handle_request_active_users(self, websocket, data: Dict[str, Any]) -> None:
        """Handle legacy request_active_users message"""
        try:
            active_user_data = []
            for user in self.active_users:
                try:
                    active_user_data.append(user.to_dict())
                except Exception as e:
                    if self.logger:
                        self.logger.warning(f"Error serializing user data: {e}")

            await websocket.send(json.dumps({
                "type": "active_users",
                "data": active_user_data
            }))

        except Exception as e:
            if self.logger:
                self.logger.error(f"Error handling request_active_users: {e}")

    async def _handle_accumulate(self, websocket, data: Dict[str, Any]) -> None:
        """Handle legacy start_accumulate/resume_accumulate messages"""
        try:
            message_type = data.get("type")
            if self.logger:
                self.logger.info(f"{message_type} received")

            # These messages don't require specific handling in the new protocol
            # Just acknowledge receipt
            await websocket.send(json.dumps({
                "type": "acknowledge",
                "message": f"{message_type} acknowledged"
            }))

        except Exception as e:
            if self.logger:
                self.logger.error(f"Error handling accumulate message: {e}")

    async def _cleanup_user_session(self, session_id: str) -> None:
        """Clean up user session and associated resources"""
        try:
            user = self._find_user_by_session(session_id)
            if user:
                # End call if active
                stream_id = self.session_streams.get(session_id)
                if stream_id:
                    call_metadata = None
                    for call_id, metadata in self.active_calls.items():
                        if self.call_streams.get(call_id) == stream_id:
                            call_metadata = metadata
                            break

                    if call_metadata:
                        await self.handle_call_ended(call_metadata.call_id, "Session cleanup")

                    # Clean up stream mapping
                    if session_id in self.session_streams:
                        del self.session_streams[session_id]

                # Remove user from tracking
                try:
                    self.active_users.remove(user)
                except ValueError:
                    pass

                if session_id in self.user_sessions:
                    del self.user_sessions[session_id]

                # Clean up user resources
                await user.cleanup()

                if self.logger:
                    self.logger.info(f"Cleaned up session: {session_id}")

        except Exception as e:
            if self.logger:
                self.logger.error(f"Error cleaning up session {session_id}: {e}")

    def get_platform_capabilities(self) -> Dict[str, bool]:
        """Get frontend platform capabilities"""
        return {
            "audio_streaming": True,
            "dtmf_support": True,
            "call_recording": True,
            "video_support": False,
            "conference_calls": False,
            "call_transfer": False,
            "call_hold": False,
            "text_input": True,
            "voice_input": True,
            "legacy_compatibility": True
        }
