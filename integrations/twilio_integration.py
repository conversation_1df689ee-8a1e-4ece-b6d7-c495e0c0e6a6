"""
Twilio integration for AI Voice Mate
Supports Twilio Programmable Voice API and Media Streams
"""

import asyncio
import json
import base64
import time
from typing import Dict, Any, Optional
import aiohttp
import websockets
from urllib.parse import quote

from .base_integration import BaseIntegration, IntegrationConfig, IntegrationStatus, CallMetadata


class TwilioIntegration(BaseIntegration):
    """Integration with Twilio Programmable Voice and Media Streams"""
    
    def __init__(self, config: IntegrationConfig, logger=None):
        super().__init__(config, logger)
        
        # Twilio-specific configuration
        self.account_sid = config.api_key
        self.auth_token = config.api_secret
        self.base_url = "https://api.twilio.com/2010-04-01"
        self.websocket_url = None
        self.websocket_connection = None
        
        # Media stream configuration
        self.media_streams: Dict[str, Dict[str, Any]] = {}
        
    def validate_config(self) -> list:
        """Validate Twilio-specific configuration"""
        errors = super().validate_config()
        
        if not self.account_sid:
            errors.append("Twilio Account SID (api_key) is required")
        
        if not self.auth_token:
            errors.append("Twilio Auth Token (api_secret) is required")
        
        if not self.config.webhook_url:
            errors.append("Webhook URL is required for Twilio integration")
        
        return errors
    
    async def connect(self) -> bool:
        """Connect to Twilio services"""
        try:
            self.status = IntegrationStatus.CONNECTING
            
            # Test API connection
            async with aiohttp.ClientSession() as session:
                auth = aiohttp.BasicAuth(self.account_sid, self.auth_token)
                url = f"{self.base_url}/Accounts/{self.account_sid}.json"
                
                async with session.get(url, auth=auth) as response:
                    if response.status == 200:
                        account_info = await response.json()
                        if self.logger:
                            self.logger.info(f"Connected to Twilio account: {account_info.get('friendly_name')}")
                        
                        self.status = IntegrationStatus.CONNECTED
                        return True
                    else:
                        error_msg = f"Failed to connect to Twilio: {response.status}"
                        if self.logger:
                            self.logger.error(error_msg)
                        if self.on_error:
                            self.on_error(Exception(error_msg))
                        self.status = IntegrationStatus.ERROR
                        return False
                        
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error connecting to Twilio: {e}")
            if self.on_error:
                self.on_error(e)
            self.status = IntegrationStatus.ERROR
            return False
    
    async def disconnect(self) -> None:
        """Disconnect from Twilio services"""
        try:
            # Close any active WebSocket connections
            if self.websocket_connection:
                await self.websocket_connection.close()
                self.websocket_connection = None
            
            # End all active calls
            for call_id in list(self.active_calls.keys()):
                await self.end_call(call_id)
            
            self.status = IntegrationStatus.DISCONNECTED
            
            if self.logger:
                self.logger.info("Disconnected from Twilio")
                
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error disconnecting from Twilio: {e}")
    
    async def start_call(self, to_number: str, from_number: str = None, **kwargs) -> str:
        """Start a new call using Twilio Voice API"""
        try:
            if not from_number:
                from_number = self.config.custom_parameters.get("default_from_number")
                if not from_number:
                    raise ValueError("from_number is required for Twilio calls")
            
            # Prepare TwiML for the call
            twiml_url = self.config.webhook_url + "/twiml"
            
            # Create call via Twilio API
            async with aiohttp.ClientSession() as session:
                auth = aiohttp.BasicAuth(self.account_sid, self.auth_token)
                url = f"{self.base_url}/Accounts/{self.account_sid}/Calls.json"
                
                data = {
                    "To": to_number,
                    "From": from_number,
                    "Url": twiml_url,
                    "StatusCallback": self.config.webhook_url + "/status",
                    "StatusCallbackEvent": ["initiated", "ringing", "answered", "completed"],
                    "Record": "false"  # We'll handle recording ourselves
                }
                
                # Add custom parameters
                if kwargs:
                    data.update(kwargs)
                
                async with session.post(url, auth=auth, data=data) as response:
                    if response.status == 201:
                        call_data = await response.json()
                        call_id = call_data["sid"]
                        
                        # Create call metadata
                        call_metadata = CallMetadata(
                            call_id=call_id,
                            from_number=from_number,
                            to_number=to_number,
                            platform="twilio",
                            start_time=time.time(),
                            custom_data=call_data
                        )
                        
                        self.active_calls[call_id] = call_metadata
                        
                        if self.logger:
                            self.logger.info(f"Started Twilio call: {call_id}")
                        
                        return call_id
                    else:
                        error_data = await response.json()
                        error_msg = f"Failed to start call: {error_data.get('message', 'Unknown error')}"
                        raise Exception(error_msg)
                        
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error starting Twilio call: {e}")
            if self.on_error:
                self.on_error(e)
            raise
    
    async def end_call(self, call_id: str) -> None:
        """End a Twilio call"""
        try:
            # Update call status via API
            async with aiohttp.ClientSession() as session:
                auth = aiohttp.BasicAuth(self.account_sid, self.auth_token)
                url = f"{self.base_url}/Accounts/{self.account_sid}/Calls/{call_id}.json"
                
                data = {"Status": "completed"}
                
                async with session.post(url, auth=auth, data=data) as response:
                    if response.status == 200:
                        await self.handle_call_ended(call_id, "completed")
                        
                        if self.logger:
                            self.logger.info(f"Ended Twilio call: {call_id}")
                    else:
                        error_msg = f"Failed to end call {call_id}: {response.status}"
                        if self.logger:
                            self.logger.warning(error_msg)
                        
                        # Still try to clean up locally
                        await self.handle_call_ended(call_id, "error")
                        
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error ending Twilio call: {e}")
            # Clean up locally even if API call fails
            await self.handle_call_ended(call_id, "error")
    
    async def send_audio(self, call_id: str, audio_data: bytes) -> None:
        """Send audio data to Twilio Media Stream"""
        try:
            stream_info = self.media_streams.get(call_id)
            if not stream_info or not stream_info.get("websocket"):
                if self.logger:
                    self.logger.warning(f"No media stream available for call {call_id}")
                return
            
            websocket = stream_info["websocket"]
            
            # Convert protocol audio (mulaw) to base64
            audio_b64 = base64.b64encode(audio_data).decode('utf-8')
            
            # Create Twilio media message
            media_message = {
                "event": "media",
                "streamSid": stream_info["streamSid"],
                "media": {
                    "payload": audio_b64
                }
            }
            
            await websocket.send(json.dumps(media_message))
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error sending audio to Twilio: {e}")
            if self.on_error:
                self.on_error(e)
    
    async def send_dtmf(self, call_id: str, digits: str) -> None:
        """Send DTMF digits to Twilio call"""
        try:
            # Use Twilio API to send DTMF
            async with aiohttp.ClientSession() as session:
                auth = aiohttp.BasicAuth(self.account_sid, self.auth_token)
                url = f"{self.base_url}/Accounts/{self.account_sid}/Calls/{call_id}.json"
                
                # Use SendDigits parameter
                data = {"Method": "POST", "SendDigits": digits}
                
                async with session.post(url, auth=auth, data=data) as response:
                    if response.status == 200:
                        if self.logger:
                            self.logger.info(f"Sent DTMF digits '{digits}' to call {call_id}")
                    else:
                        error_msg = f"Failed to send DTMF: {response.status}"
                        if self.logger:
                            self.logger.error(error_msg)
                        
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error sending DTMF to Twilio: {e}")
            if self.on_error:
                self.on_error(e)
    
    async def handle_webhook(self, webhook_data: Dict[str, Any]) -> Dict[str, Any]:
        """Handle Twilio webhook events"""
        try:
            event_type = webhook_data.get("event", webhook_data.get("EventType"))
            call_sid = webhook_data.get("CallSid")

            if event_type == "call-started" or webhook_data.get("CallStatus") == "in-progress":
                await self.handle_incoming_call(webhook_data)

                # Return TwiML to start media stream
                return {
                    "content_type": "application/xml",
                    "body": self.generate_media_stream_twiml(call_sid)
                }

            elif event_type == "call-ended" or webhook_data.get("CallStatus") in ["completed", "failed", "busy", "no-answer"]:
                if call_sid:
                    await self.handle_call_ended(call_sid, webhook_data.get("CallStatus", "completed"))

            return {"status": "ok"}

        except Exception as e:
            if self.logger:
                self.logger.error(f"Error handling Twilio webhook: {e}")
            if self.on_error:
                self.on_error(e)
            return {"status": "error", "message": str(e)}\n    \n    async def handle_media_stream(self, websocket_connection, call_sid: str) -> None:\n        \"\"\"Handle Twilio Media Stream WebSocket connection\"\"\"\n        try:\n            self.media_streams[call_sid] = {\n                \"websocket\": websocket_connection,\n                \"streamSid\": None,\n                \"connected\": True\n            }\n            \n            async for message in websocket_connection:\n                try:\n                    data = json.loads(message)\n                    event = data.get(\"event\")\n                    \n                    if event == \"connected\":\n                        if self.logger:\n                            self.logger.info(f\"Media stream connected for call {call_sid}\")\n                    \n                    elif event == \"start\":\n                        stream_sid = data.get(\"streamSid\")\n                        self.media_streams[call_sid][\"streamSid\"] = stream_sid\n                        if self.logger:\n                            self.logger.info(f\"Media stream started: {stream_sid}\")\n                    \n                    elif event == \"media\":\n                        # Handle incoming audio\n                        media = data.get(\"media\", {})\n                        payload = media.get(\"payload\", \"\")\n                        \n                        if payload:\n                            # Decode base64 audio (already in mulaw format)\n                            audio_data = base64.b64decode(payload)\n                            await self.handle_incoming_audio(call_sid, audio_data, \"mulaw\")\n                    \n                    elif event == \"stop\":\n                        if self.logger:\n                            self.logger.info(f\"Media stream stopped for call {call_sid}\")\n                        break\n                        \n                except json.JSONDecodeError as e:\n                    if self.logger:\n                        self.logger.error(f\"Invalid JSON in media stream: {e}\")\n                        \n        except Exception as e:\n            if self.logger:\n                self.logger.error(f\"Error in media stream handling: {e}\")\n        finally:\n            # Clean up stream\n            if call_sid in self.media_streams:\n                del self.media_streams[call_sid]\n    \n    def generate_media_stream_twiml(self, call_sid: str) -> str:\n        \"\"\"Generate TwiML to start a media stream\"\"\"\n        websocket_url = self.config.webhook_url.replace(\"http\", \"ws\") + f\"/media-stream/{call_sid}\"\n        \n        return f\"\"\"<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<Response>\n    <Say>AI Voice Mate is now active. Please speak after the tone.</Say>\n    <Start>\n        <Stream url=\"{websocket_url}\" />\n    </Start>\n    <Pause length=\"3600\" />\n</Response>\"\"\"\n    \n    def get_platform_capabilities(self) -> Dict[str, bool]:\n        \"\"\"Get Twilio platform capabilities\"\"\"\n        return {\n            \"audio_streaming\": True,\n            \"dtmf_support\": True,\n            \"call_recording\": True,\n            \"video_support\": False,\n            \"conference_calls\": True,\n            \"call_transfer\": True,\n            \"call_hold\": True,\n            \"sms_support\": True,\n            \"media_streams\": True\n        }\n    \n    async def health_check(self) -> Dict[str, Any]:\n        \"\"\"Perform Twilio-specific health check\"\"\"\n        base_health = await super().health_check()\n        \n        # Add Twilio-specific health info\n        base_health.update({\n            \"account_sid\": self.account_sid,\n            \"media_streams\": len(self.media_streams),\n            \"api_accessible\": self.status in [IntegrationStatus.CONNECTED, IntegrationStatus.ACTIVE]\n        })\n        \n        return base_health