[{"/home/<USER>/PycharmProjects/ai-voice-mate-dev/ai_voice_mate/frontend/src/index.tsx": "1", "/home/<USER>/PycharmProjects/ai-voice-mate-dev/ai_voice_mate/frontend/src/App.tsx": "2", "/home/<USER>/PycharmProjects/ai-voice-mate-dev/ai_voice_mate/frontend/src/components/VoiceCallInterface.tsx": "3", "/home/<USER>/PycharmProjects/ai-voice-mate-dev/ai_voice_mate/frontend/src/services/audioService.ts": "4", "/home/<USER>/PycharmProjects/ai-voice-mate-dev/ai_voice_mate/frontend/src/services/websocketService.ts": "5"}, {"size": 251, "mtime": 1753598663629, "results": "6", "hashOfConfig": "7"}, {"size": 10251, "mtime": 1753598655551, "results": "8", "hashOfConfig": "7"}, {"size": 15814, "mtime": 1753604411697, "results": "9", "hashOfConfig": "7"}, {"size": 9768, "mtime": 1753602162968, "results": "10", "hashOfConfig": "7"}, {"size": 13746, "mtime": 1753604353592, "results": "11", "hashOfConfig": "7"}, {"filePath": "12", "messages": "13", "suppressedMessages": "14", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1bjvwel", {"filePath": "15", "messages": "16", "suppressedMessages": "17", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "18", "messages": "19", "suppressedMessages": "20", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "21", "messages": "22", "suppressedMessages": "23", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "/home/<USER>/PycharmProjects/ai-voice-mate-dev/ai_voice_mate/frontend/src/index.tsx", [], [], "/home/<USER>/PycharmProjects/ai-voice-mate-dev/ai_voice_mate/frontend/src/App.tsx", ["27", "28", "29"], [], "/home/<USER>/PycharmProjects/ai-voice-mate-dev/ai_voice_mate/frontend/src/components/VoiceCallInterface.tsx", ["30", "31", "32", "33", "34", "35", "36", "37"], [], "/home/<USER>/PycharmProjects/ai-voice-mate-dev/ai_voice_mate/frontend/src/services/audioService.ts", [], [], "/home/<USER>/PycharmProjects/ai-voice-mate-dev/ai_voice_mate/frontend/src/services/websocketService.ts", ["38", "39"], [], {"ruleId": "40", "severity": 1, "message": "41", "line": 23, "column": 27, "nodeType": "42", "messageId": "43", "endLine": 23, "endColumn": 31}, {"ruleId": "44", "severity": 1, "message": "45", "line": 98, "column": 29, "nodeType": "46", "messageId": "47", "endLine": 98, "endColumn": 30, "suggestions": "48"}, {"ruleId": "44", "severity": 1, "message": "49", "line": 98, "column": 31, "nodeType": "46", "messageId": "47", "endLine": 98, "endColumn": 32, "suggestions": "50"}, {"ruleId": "40", "severity": 1, "message": "51", "line": 21, "column": 3, "nodeType": "42", "messageId": "43", "endLine": 21, "endColumn": 16}, {"ruleId": "40", "severity": 1, "message": "52", "line": 22, "column": 3, "nodeType": "42", "messageId": "43", "endLine": 22, "endColumn": 11}, {"ruleId": "40", "severity": 1, "message": "53", "line": 23, "column": 3, "nodeType": "42", "messageId": "43", "endLine": 23, "endColumn": 12}, {"ruleId": "40", "severity": 1, "message": "54", "line": 24, "column": 3, "nodeType": "42", "messageId": "43", "endLine": 24, "endColumn": 11}, {"ruleId": "40", "severity": 1, "message": "55", "line": 74, "column": 10, "nodeType": "42", "messageId": "43", "endLine": 74, "endColumn": 23}, {"ruleId": "40", "severity": 1, "message": "56", "line": 74, "column": 25, "nodeType": "42", "messageId": "43", "endLine": 74, "endColumn": 41}, {"ruleId": "57", "severity": 1, "message": "58", "line": 185, "column": 40, "nodeType": "42", "endLine": 185, "endColumn": 47}, {"ruleId": "57", "severity": 1, "message": "59", "line": 191, "column": 6, "nodeType": "60", "endLine": 191, "endColumn": 16, "suggestions": "61"}, {"ruleId": "40", "severity": 1, "message": "62", "line": 6, "column": 10, "nodeType": "42", "messageId": "43", "endLine": 6, "endColumn": 12}, {"ruleId": "40", "severity": 1, "message": "63", "line": 6, "column": 14, "nodeType": "42", "messageId": "43", "endLine": 6, "endColumn": 20}, "@typescript-eslint/no-unused-vars", "'Info' is defined but never used.", "Identifier", "unusedVar", "no-useless-escape", "Unnecessary escape character: \\(.", "Literal", "unnecessaryEscape", ["64", "65"], "Unnecessary escape character: \\).", ["66", "67"], "'PhoneDisabled' is defined but never used.", "'VolumeUp' is defined but never used.", "'VolumeOff' is defined but never used.", "'Settings' is defined but never used.", "'transcription' is assigned a value but never used.", "'setTranscription' is assigned a value but never used.", "react-hooks/exhaustive-deps", "The ref value 'reconnectTimerRef.current' will likely have changed by the time this effect cleanup function runs. If this ref points to a node rendered by React, copy 'reconnectTimerRef.current' to a variable inside the effect, and use that variable in the cleanup function.", "React Hook useEffect has missing dependencies: 'endCall' and 'startCallTimer'. Either include them or remove the dependency array.", "ArrayExpression", ["68"], "'io' is defined but never used.", "'Socket' is defined but never used.", {"messageId": "69", "fix": "70", "desc": "71"}, {"messageId": "72", "fix": "73", "desc": "74"}, {"messageId": "69", "fix": "75", "desc": "71"}, {"messageId": "72", "fix": "76", "desc": "74"}, {"desc": "77", "fix": "78"}, "removeEscape", {"range": "79", "text": "80"}, "Remove the `\\`. This maintains the current functionality.", "escape<PERSON><PERSON><PERSON><PERSON>", {"range": "81", "text": "82"}, "Replace the `\\` with `\\\\` to include the actual backslash character.", {"range": "83", "text": "80"}, {"range": "84", "text": "82"}, "Update the dependencies array to be: [endCall, startCallTimer, streamId]", {"range": "85", "text": "86"}, [1976, 1977], "", [1976, 1976], "\\", [1978, 1979], [1978, 1978], [5672, 5682], "[endCall, startCallTimer, streamId]"]