{"ast": null, "code": "/**\n * WebSocket service for bi-directional audio streaming protocol\n * Implements the protocol events as specified in the integration document\n */\n\nclass WebSocketService {\n  constructor() {\n    this.socket = null;\n    this.callbacks = {};\n    this.sequenceNumber = 0;\n    this.streamSid = null;\n    this.isConnected = false;\n    this.reconnectAttempts = 0;\n    this.maxReconnectAttempts = 5;\n    this.reconnectDelay = 1000;\n    // Audio processing\n    this.audioContext = null;\n    this.audioQueue = [];\n    this.isPlayingAudio = false;\n    this.currentMarkName = null;\n    this.initializeAudioContext();\n  }\n  initializeAudioContext() {\n    try {\n      this.audioContext = new (window.AudioContext || window.webkitAudioContext)();\n    } catch (error) {\n      console.error('Failed to initialize audio context:', error);\n    }\n  }\n  setCallbacks(callbacks) {\n    this.callbacks = callbacks;\n  }\n  connect(url) {\n    return new Promise((resolve, reject) => {\n      try {\n        this.socket = new WebSocket(url);\n        this.socket.onopen = () => {\n          console.log('WebSocket connected');\n          this.isConnected = true;\n          this.reconnectAttempts = 0;\n\n          // Send connected event\n          this.sendConnectedEvent();\n          if (this.callbacks.onConnected) {\n            this.callbacks.onConnected();\n          }\n          resolve();\n        };\n        this.socket.onmessage = event => {\n          try {\n            const data = JSON.parse(event.data);\n            this.handleProtocolEvent(data);\n          } catch (error) {\n            console.error('Error parsing WebSocket message:', error);\n            if (this.callbacks.onError) {\n              this.callbacks.onError(new Error('Failed to parse message'));\n            }\n          }\n        };\n        this.socket.onclose = () => {\n          console.log('WebSocket disconnected');\n          this.isConnected = false;\n          if (this.callbacks.onDisconnected) {\n            this.callbacks.onDisconnected();\n          }\n\n          // Attempt to reconnect\n          this.attemptReconnect(url);\n        };\n        this.socket.onerror = error => {\n          console.error('WebSocket error:', error);\n          if (this.callbacks.onError) {\n            this.callbacks.onError(new Error('WebSocket connection error'));\n          }\n          reject(error);\n        };\n      } catch (error) {\n        reject(error);\n      }\n    });\n  }\n  attemptReconnect(url) {\n    if (this.reconnectAttempts < this.maxReconnectAttempts) {\n      this.reconnectAttempts++;\n      console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);\n      setTimeout(() => {\n        this.connect(url);\n      }, this.reconnectDelay * this.reconnectAttempts);\n    } else {\n      console.error('Max reconnection attempts reached');\n      if (this.callbacks.onError) {\n        this.callbacks.onError(new Error('Failed to reconnect after maximum attempts'));\n      }\n    }\n  }\n  handleProtocolEvent(event) {\n    console.log('Received protocol event:', event.event);\n    switch (event.event) {\n      case 'connected':\n        // Handle connected response\n        break;\n      case 'start':\n        this.streamSid = event.streamSid;\n        if (this.callbacks.onStartReceived) {\n          this.callbacks.onStartReceived(event);\n        }\n        break;\n      case 'media':\n        this.handleMediaEvent(event);\n        if (this.callbacks.onMediaReceived) {\n          this.callbacks.onMediaReceived(event);\n        }\n        break;\n      case 'mark':\n        this.handleMarkEvent(event);\n        if (this.callbacks.onMarkReceived) {\n          this.callbacks.onMarkReceived(event);\n        }\n        break;\n      case 'stop':\n        if (this.callbacks.onStopReceived) {\n          this.callbacks.onStopReceived(event);\n        }\n        break;\n      case 'clear':\n        this.clearAudioBuffer();\n        if (this.callbacks.onClearReceived) {\n          this.callbacks.onClearReceived(event);\n        }\n        break;\n      case 'dtmf':\n        if (this.callbacks.onDTMFReceived) {\n          this.callbacks.onDTMFReceived(event);\n        }\n        break;\n    }\n  }\n  async handleMediaEvent(event) {\n    try {\n      // Decode base64 audio payload\n      const audioData = this.base64ToArrayBuffer(event.media.payload);\n\n      // Convert mulaw to PCM and create AudioBuffer\n      const audioBuffer = await this.convertMulawToPCM(audioData);\n\n      // Queue for playback\n      this.audioQueue.push(audioBuffer);\n\n      // Start playback if not already playing\n      if (!this.isPlayingAudio) {\n        this.playNextAudio();\n      }\n    } catch (error) {\n      console.error('Error handling media event:', error);\n    }\n  }\n  handleMarkEvent(event) {\n    // Handle mark completion\n    if (this.callbacks.onAudioPlaybackComplete) {\n      this.callbacks.onAudioPlaybackComplete(event.mark.name);\n    }\n  }\n  base64ToArrayBuffer(base64) {\n    const binaryString = window.atob(base64);\n    const len = binaryString.length;\n    const bytes = new Uint8Array(len);\n    for (let i = 0; i < len; i++) {\n      bytes[i] = binaryString.charCodeAt(i);\n    }\n    return bytes.buffer;\n  }\n  async convertMulawToPCM(mulawData) {\n    if (!this.audioContext) {\n      throw new Error('Audio context not initialized');\n    }\n\n    // Convert mulaw to 16-bit PCM\n    const mulawArray = new Uint8Array(mulawData);\n    const pcmArray = new Int16Array(mulawArray.length);\n\n    // Mulaw to linear conversion table (simplified)\n    for (let i = 0; i < mulawArray.length; i++) {\n      pcmArray[i] = this.mulawToLinear(mulawArray[i]);\n    }\n\n    // Create AudioBuffer\n    const audioBuffer = this.audioContext.createBuffer(1, pcmArray.length, 8000);\n    const channelData = audioBuffer.getChannelData(0);\n\n    // Convert to float32 and normalize\n    for (let i = 0; i < pcmArray.length; i++) {\n      channelData[i] = pcmArray[i] / 32768.0;\n    }\n    return audioBuffer;\n  }\n  mulawToLinear(mulaw) {\n    // Simplified mulaw to linear conversion\n    mulaw = ~mulaw;\n    const sign = mulaw & 0x80;\n    const exponent = mulaw >> 4 & 0x07;\n    const mantissa = mulaw & 0x0F;\n    let sample = mantissa << exponent + 3;\n    if (exponent !== 0) {\n      sample += 0x84 << exponent;\n    }\n    return sign ? -sample : sample;\n  }\n  async playNextAudio() {\n    if (this.audioQueue.length === 0 || !this.audioContext) {\n      this.isPlayingAudio = false;\n      return;\n    }\n    this.isPlayingAudio = true;\n    const audioBuffer = this.audioQueue.shift();\n    const source = this.audioContext.createBufferSource();\n    source.buffer = audioBuffer;\n    source.connect(this.audioContext.destination);\n    source.onended = () => {\n      // Send mark completion if we have a current mark\n      if (this.currentMarkName) {\n        this.sendMarkEvent(this.currentMarkName);\n        this.currentMarkName = null;\n      }\n\n      // Play next audio\n      this.playNextAudio();\n    };\n    source.start();\n  }\n  clearAudioBuffer() {\n    this.audioQueue = [];\n\n    // Send completion for all pending marks\n    if (this.currentMarkName) {\n      this.sendMarkEvent(this.currentMarkName);\n      this.currentMarkName = null;\n    }\n  }\n\n  // Protocol event sending methods\n  sendConnectedEvent() {\n    const event = {\n      event: 'connected'\n    };\n    this.send(event);\n  }\n  sendMediaEvent(audioData, streamSid) {\n    if (!this.isConnected || !this.socket) {\n      console.warn('Cannot send media event: not connected');\n      return;\n    }\n    this.sequenceNumber++;\n    const event = {\n      event: 'media',\n      sequenceNumber: this.sequenceNumber.toString(),\n      media: {\n        chunk: '1',\n        // Simplified chunk numbering\n        timestamp: Date.now().toString(),\n        payload: this.arrayBufferToBase64(audioData)\n      },\n      streamSid\n    };\n    this.send(event);\n  }\n  sendMarkEvent(markName) {\n    if (!this.isConnected || !this.socket || !this.streamSid) {\n      console.warn('Cannot send mark event: not connected or no stream');\n      return;\n    }\n    this.sequenceNumber++;\n    const event = {\n      event: 'mark',\n      sequenceNumber: this.sequenceNumber.toString(),\n      streamSid: this.streamSid,\n      mark: {\n        name: markName\n      }\n    };\n    this.send(event);\n  }\n  sendDTMFEvent(digit) {\n    if (!this.isConnected || !this.socket || !this.streamSid) {\n      console.warn('Cannot send DTMF event: not connected or no stream');\n      return;\n    }\n    this.sequenceNumber++;\n    const event = {\n      event: 'dtmf',\n      streamSid: this.streamSid,\n      sequenceNumber: this.sequenceNumber.toString(),\n      dtmf: {\n        digit\n      }\n    };\n    this.send(event);\n  }\n  sendClearEvent() {\n    if (!this.isConnected || !this.socket || !this.streamSid) {\n      console.warn('Cannot send clear event: not connected or no stream');\n      return;\n    }\n    const event = {\n      event: 'clear',\n      streamSid: this.streamSid\n    };\n    this.send(event);\n  }\n  arrayBufferToBase64(buffer) {\n    const bytes = new Uint8Array(buffer);\n    let binary = '';\n    for (let i = 0; i < bytes.byteLength; i++) {\n      binary += String.fromCharCode(bytes[i]);\n    }\n    return window.btoa(binary);\n  }\n  send(event) {\n    if (this.socket && this.isConnected) {\n      this.socket.send(JSON.stringify(event));\n    }\n  }\n\n  // Protocol-based user registration and call initiation\n  sendUserRegistration(userData) {\n    // Generate a unique session ID for this user\n    const sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n\n    // Store user data in a custom event format that the backend can handle\n    const userEvent = {\n      event: 'user_register',\n      sessionId,\n      userData,\n      timestamp: Date.now()\n    };\n    this.send(userEvent);\n    return sessionId;\n  }\n  sendCallStart(sessionId, callConfig) {\n    const startCallEvent = {\n      event: 'call_start',\n      sessionId,\n      callConfig: {\n        from: (callConfig === null || callConfig === void 0 ? void 0 : callConfig.from) || 'frontend',\n        to: (callConfig === null || callConfig === void 0 ? void 0 : callConfig.to) || 'ai_assistant',\n        customParameters: (callConfig === null || callConfig === void 0 ? void 0 : callConfig.customParameters) || {}\n      },\n      timestamp: Date.now()\n    };\n    this.send(startCallEvent);\n  }\n  sendCallEnd(sessionId, reason = 'user_ended') {\n    const endCallEvent = {\n      event: 'call_end',\n      sessionId,\n      reason,\n      timestamp: Date.now()\n    };\n    this.send(endCallEvent);\n  }\n  disconnect() {\n    if (this.socket) {\n      this.socket.close();\n      this.socket = null;\n    }\n    this.isConnected = false;\n    this.streamSid = null;\n    this.sequenceNumber = 0;\n  }\n  getConnectionStatus() {\n    return this.isConnected;\n  }\n  getStreamId() {\n    return this.streamSid;\n  }\n}\nexport const websocketService = new WebSocketService();", "map": {"version": 3, "names": ["WebSocketService", "constructor", "socket", "callbacks", "sequenceNumber", "streamSid", "isConnected", "reconnectAttempts", "maxReconnectAttempts", "reconnectDelay", "audioContext", "audioQueue", "isPlayingAudio", "currentMarkName", "initializeAudioContext", "window", "AudioContext", "webkitAudioContext", "error", "console", "setCallbacks", "connect", "url", "Promise", "resolve", "reject", "WebSocket", "onopen", "log", "sendConnectedEvent", "onConnected", "onmessage", "event", "data", "JSON", "parse", "handleProtocolEvent", "onError", "Error", "onclose", "onDisconnected", "attemptReconnect", "onerror", "setTimeout", "onStartReceived", "handleMediaEvent", "onMediaReceived", "handleMarkEvent", "onMarkReceived", "onStopReceived", "clearAudioBuffer", "onClearReceived", "onDTMFReceived", "audioData", "base64ToArrayBuffer", "media", "payload", "audioBuffer", "convertMulawToPCM", "push", "playNextAudio", "onAudioPlaybackComplete", "mark", "name", "base64", "binaryString", "atob", "len", "length", "bytes", "Uint8Array", "i", "charCodeAt", "buffer", "mulawData", "mulawArray", "pcmArray", "Int16Array", "mulawToLinear", "createBuffer", "channelData", "getChannelData", "mulaw", "sign", "exponent", "mantissa", "sample", "shift", "source", "createBufferSource", "destination", "onended", "sendMarkEvent", "start", "send", "sendMediaEvent", "warn", "toString", "chunk", "timestamp", "Date", "now", "arrayBufferToBase64", "<PERSON><PERSON><PERSON>", "sendDTMFEvent", "digit", "dtmf", "sendClearEvent", "binary", "byteLength", "String", "fromCharCode", "btoa", "stringify", "sendUserRegistration", "userData", "sessionId", "Math", "random", "substr", "userEvent", "sendCallStart", "callConfig", "startCallEvent", "from", "to", "customParameters", "sendCallEnd", "reason", "endCallEvent", "disconnect", "close", "getConnectionStatus", "getStreamId", "websocketService"], "sources": ["/home/<USER>/PycharmProjects/ai-voice-mate-dev/ai_voice_mate/frontend/src/services/websocketService.ts"], "sourcesContent": ["/**\n * WebSocket service for bi-directional audio streaming protocol\n * Implements the protocol events as specified in the integration document\n */\n\nimport { io, Socket } from 'socket.io-client';\n\nexport interface StreamMetadata {\n  streamSid: string;\n  accountSid: string;\n  callSid: string;\n  from: string;\n  to: string;\n  mediaFormat: {\n    encoding: string;\n    sampleRate: number;\n    bitRate: number;\n    bitDepth: number;\n  };\n  customParameters: Record<string, any>;\n}\n\nexport interface MediaEvent {\n  event: 'media';\n  sequenceNumber: string;\n  media: {\n    chunk: string;\n    timestamp: string;\n    payload: string; // base64 encoded audio\n  };\n  streamSid: string;\n}\n\nexport interface MarkEvent {\n  event: 'mark';\n  sequenceNumber: string;\n  streamSid: string;\n  mark: {\n    name: string;\n  };\n}\n\nexport interface ConnectedEvent {\n  event: 'connected';\n}\n\nexport interface StartEvent {\n  event: 'start';\n  sequenceNumber: string;\n  start: StreamMetadata;\n  streamSid: string;\n}\n\nexport interface StopEvent {\n  event: 'stop';\n  sequenceNumber: string;\n  stop: {\n    accountSid: string;\n    callSid: string;\n    reason: string;\n  };\n  streamSid: string;\n}\n\nexport interface ClearEvent {\n  event: 'clear';\n  streamSid: string;\n}\n\nexport interface DTMFEvent {\n  event: 'dtmf';\n  streamSid: string;\n  sequenceNumber: string;\n  dtmf: {\n    digit: string;\n  };\n}\n\nexport type ProtocolEvent = \n  | ConnectedEvent \n  | StartEvent \n  | MediaEvent \n  | StopEvent \n  | MarkEvent \n  | ClearEvent \n  | DTMFEvent;\n\nexport interface AudioConfig {\n  sampleRate: number;\n  bitDepth: number;\n  channels: number;\n  encoding: string;\n}\n\nexport interface WebSocketServiceCallbacks {\n  onConnected?: () => void;\n  onStartReceived?: (event: StartEvent) => void;\n  onMediaReceived?: (event: MediaEvent) => void;\n  onMarkReceived?: (event: MarkEvent) => void;\n  onStopReceived?: (event: StopEvent) => void;\n  onClearReceived?: (event: ClearEvent) => void;\n  onDTMFReceived?: (event: DTMFEvent) => void;\n  onError?: (error: Error) => void;\n  onDisconnected?: () => void;\n  onAudioPlaybackComplete?: (markName: string) => void;\n}\n\nclass WebSocketService {\n  private socket: WebSocket | null = null;\n  private callbacks: WebSocketServiceCallbacks = {};\n  private sequenceNumber = 0;\n  private streamSid: string | null = null;\n  private isConnected = false;\n  private reconnectAttempts = 0;\n  private maxReconnectAttempts = 5;\n  private reconnectDelay = 1000;\n\n  // Audio processing\n  private audioContext: AudioContext | null = null;\n  private audioQueue: AudioBuffer[] = [];\n  private isPlayingAudio = false;\n  private currentMarkName: string | null = null;\n\n  constructor() {\n    this.initializeAudioContext();\n  }\n\n  private initializeAudioContext() {\n    try {\n      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();\n    } catch (error) {\n      console.error('Failed to initialize audio context:', error);\n    }\n  }\n\n  public setCallbacks(callbacks: WebSocketServiceCallbacks) {\n    this.callbacks = callbacks;\n  }\n\n  public connect(url: string): Promise<void> {\n    return new Promise((resolve, reject) => {\n      try {\n        this.socket = new WebSocket(url);\n\n        this.socket.onopen = () => {\n          console.log('WebSocket connected');\n          this.isConnected = true;\n          this.reconnectAttempts = 0;\n          \n          // Send connected event\n          this.sendConnectedEvent();\n          \n          if (this.callbacks.onConnected) {\n            this.callbacks.onConnected();\n          }\n          resolve();\n        };\n\n        this.socket.onmessage = (event) => {\n          try {\n            const data = JSON.parse(event.data);\n            this.handleProtocolEvent(data);\n          } catch (error) {\n            console.error('Error parsing WebSocket message:', error);\n            if (this.callbacks.onError) {\n              this.callbacks.onError(new Error('Failed to parse message'));\n            }\n          }\n        };\n\n        this.socket.onclose = () => {\n          console.log('WebSocket disconnected');\n          this.isConnected = false;\n          \n          if (this.callbacks.onDisconnected) {\n            this.callbacks.onDisconnected();\n          }\n          \n          // Attempt to reconnect\n          this.attemptReconnect(url);\n        };\n\n        this.socket.onerror = (error) => {\n          console.error('WebSocket error:', error);\n          if (this.callbacks.onError) {\n            this.callbacks.onError(new Error('WebSocket connection error'));\n          }\n          reject(error);\n        };\n\n      } catch (error) {\n        reject(error);\n      }\n    });\n  }\n\n  private attemptReconnect(url: string) {\n    if (this.reconnectAttempts < this.maxReconnectAttempts) {\n      this.reconnectAttempts++;\n      console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);\n      \n      setTimeout(() => {\n        this.connect(url);\n      }, this.reconnectDelay * this.reconnectAttempts);\n    } else {\n      console.error('Max reconnection attempts reached');\n      if (this.callbacks.onError) {\n        this.callbacks.onError(new Error('Failed to reconnect after maximum attempts'));\n      }\n    }\n  }\n\n  private handleProtocolEvent(event: ProtocolEvent) {\n    console.log('Received protocol event:', event.event);\n\n    switch (event.event) {\n      case 'connected':\n        // Handle connected response\n        break;\n        \n      case 'start':\n        this.streamSid = event.streamSid;\n        if (this.callbacks.onStartReceived) {\n          this.callbacks.onStartReceived(event);\n        }\n        break;\n        \n      case 'media':\n        this.handleMediaEvent(event);\n        if (this.callbacks.onMediaReceived) {\n          this.callbacks.onMediaReceived(event);\n        }\n        break;\n        \n      case 'mark':\n        this.handleMarkEvent(event);\n        if (this.callbacks.onMarkReceived) {\n          this.callbacks.onMarkReceived(event);\n        }\n        break;\n        \n      case 'stop':\n        if (this.callbacks.onStopReceived) {\n          this.callbacks.onStopReceived(event);\n        }\n        break;\n        \n      case 'clear':\n        this.clearAudioBuffer();\n        if (this.callbacks.onClearReceived) {\n          this.callbacks.onClearReceived(event);\n        }\n        break;\n        \n      case 'dtmf':\n        if (this.callbacks.onDTMFReceived) {\n          this.callbacks.onDTMFReceived(event);\n        }\n        break;\n    }\n  }\n\n  private async handleMediaEvent(event: MediaEvent) {\n    try {\n      // Decode base64 audio payload\n      const audioData = this.base64ToArrayBuffer(event.media.payload);\n      \n      // Convert mulaw to PCM and create AudioBuffer\n      const audioBuffer = await this.convertMulawToPCM(audioData);\n      \n      // Queue for playback\n      this.audioQueue.push(audioBuffer);\n      \n      // Start playback if not already playing\n      if (!this.isPlayingAudio) {\n        this.playNextAudio();\n      }\n      \n    } catch (error) {\n      console.error('Error handling media event:', error);\n    }\n  }\n\n  private handleMarkEvent(event: MarkEvent) {\n    // Handle mark completion\n    if (this.callbacks.onAudioPlaybackComplete) {\n      this.callbacks.onAudioPlaybackComplete(event.mark.name);\n    }\n  }\n\n  private base64ToArrayBuffer(base64: string): ArrayBuffer {\n    const binaryString = window.atob(base64);\n    const len = binaryString.length;\n    const bytes = new Uint8Array(len);\n    for (let i = 0; i < len; i++) {\n      bytes[i] = binaryString.charCodeAt(i);\n    }\n    return bytes.buffer;\n  }\n\n  private async convertMulawToPCM(mulawData: ArrayBuffer): Promise<AudioBuffer> {\n    if (!this.audioContext) {\n      throw new Error('Audio context not initialized');\n    }\n\n    // Convert mulaw to 16-bit PCM\n    const mulawArray = new Uint8Array(mulawData);\n    const pcmArray = new Int16Array(mulawArray.length);\n    \n    // Mulaw to linear conversion table (simplified)\n    for (let i = 0; i < mulawArray.length; i++) {\n      pcmArray[i] = this.mulawToLinear(mulawArray[i]);\n    }\n\n    // Create AudioBuffer\n    const audioBuffer = this.audioContext.createBuffer(1, pcmArray.length, 8000);\n    const channelData = audioBuffer.getChannelData(0);\n    \n    // Convert to float32 and normalize\n    for (let i = 0; i < pcmArray.length; i++) {\n      channelData[i] = pcmArray[i] / 32768.0;\n    }\n\n    return audioBuffer;\n  }\n\n  private mulawToLinear(mulaw: number): number {\n    // Simplified mulaw to linear conversion\n    mulaw = ~mulaw;\n    const sign = mulaw & 0x80;\n    const exponent = (mulaw >> 4) & 0x07;\n    const mantissa = mulaw & 0x0F;\n    \n    let sample = mantissa << (exponent + 3);\n    if (exponent !== 0) {\n      sample += 0x84 << exponent;\n    }\n    \n    return sign ? -sample : sample;\n  }\n\n  private async playNextAudio() {\n    if (this.audioQueue.length === 0 || !this.audioContext) {\n      this.isPlayingAudio = false;\n      return;\n    }\n\n    this.isPlayingAudio = true;\n    const audioBuffer = this.audioQueue.shift()!;\n    \n    const source = this.audioContext.createBufferSource();\n    source.buffer = audioBuffer;\n    source.connect(this.audioContext.destination);\n    \n    source.onended = () => {\n      // Send mark completion if we have a current mark\n      if (this.currentMarkName) {\n        this.sendMarkEvent(this.currentMarkName);\n        this.currentMarkName = null;\n      }\n      \n      // Play next audio\n      this.playNextAudio();\n    };\n    \n    source.start();\n  }\n\n  private clearAudioBuffer() {\n    this.audioQueue = [];\n    \n    // Send completion for all pending marks\n    if (this.currentMarkName) {\n      this.sendMarkEvent(this.currentMarkName);\n      this.currentMarkName = null;\n    }\n  }\n\n  // Protocol event sending methods\n  public sendConnectedEvent() {\n    const event: ConnectedEvent = {\n      event: 'connected'\n    };\n    this.send(event);\n  }\n\n  public sendMediaEvent(audioData: ArrayBuffer, streamSid: string) {\n    if (!this.isConnected || !this.socket) {\n      console.warn('Cannot send media event: not connected');\n      return;\n    }\n\n    this.sequenceNumber++;\n    \n    const event: MediaEvent = {\n      event: 'media',\n      sequenceNumber: this.sequenceNumber.toString(),\n      media: {\n        chunk: '1', // Simplified chunk numbering\n        timestamp: Date.now().toString(),\n        payload: this.arrayBufferToBase64(audioData)\n      },\n      streamSid\n    };\n    \n    this.send(event);\n  }\n\n  public sendMarkEvent(markName: string) {\n    if (!this.isConnected || !this.socket || !this.streamSid) {\n      console.warn('Cannot send mark event: not connected or no stream');\n      return;\n    }\n\n    this.sequenceNumber++;\n    \n    const event: MarkEvent = {\n      event: 'mark',\n      sequenceNumber: this.sequenceNumber.toString(),\n      streamSid: this.streamSid,\n      mark: {\n        name: markName\n      }\n    };\n    \n    this.send(event);\n  }\n\n  public sendDTMFEvent(digit: string) {\n    if (!this.isConnected || !this.socket || !this.streamSid) {\n      console.warn('Cannot send DTMF event: not connected or no stream');\n      return;\n    }\n\n    this.sequenceNumber++;\n    \n    const event: DTMFEvent = {\n      event: 'dtmf',\n      streamSid: this.streamSid,\n      sequenceNumber: this.sequenceNumber.toString(),\n      dtmf: {\n        digit\n      }\n    };\n    \n    this.send(event);\n  }\n\n  public sendClearEvent() {\n    if (!this.isConnected || !this.socket || !this.streamSid) {\n      console.warn('Cannot send clear event: not connected or no stream');\n      return;\n    }\n\n    const event: ClearEvent = {\n      event: 'clear',\n      streamSid: this.streamSid\n    };\n    \n    this.send(event);\n  }\n\n  private arrayBufferToBase64(buffer: ArrayBuffer): string {\n    const bytes = new Uint8Array(buffer);\n    let binary = '';\n    for (let i = 0; i < bytes.byteLength; i++) {\n      binary += String.fromCharCode(bytes[i]);\n    }\n    return window.btoa(binary);\n  }\n\n  private send(event: ProtocolEvent | any) {\n    if (this.socket && this.isConnected) {\n      this.socket.send(JSON.stringify(event));\n    }\n  }\n\n  // Protocol-based user registration and call initiation\n  public sendUserRegistration(userData: {\n    name: string;\n    mobile: string;\n    userId: string;\n    sessionType?: string;\n  }): string {\n    // Generate a unique session ID for this user\n    const sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n\n    // Store user data in a custom event format that the backend can handle\n    const userEvent = {\n      event: 'user_register',\n      sessionId,\n      userData,\n      timestamp: Date.now()\n    };\n\n    this.send(userEvent);\n    return sessionId;\n  }\n\n  public sendCallStart(sessionId: string, callConfig?: {\n    from?: string;\n    to?: string;\n    customParameters?: Record<string, any>;\n  }) {\n    const startCallEvent = {\n      event: 'call_start',\n      sessionId,\n      callConfig: {\n        from: callConfig?.from || 'frontend',\n        to: callConfig?.to || 'ai_assistant',\n        customParameters: callConfig?.customParameters || {}\n      },\n      timestamp: Date.now()\n    };\n\n    this.send(startCallEvent);\n  }\n\n  public sendCallEnd(sessionId: string, reason: string = 'user_ended') {\n    const endCallEvent = {\n      event: 'call_end',\n      sessionId,\n      reason,\n      timestamp: Date.now()\n    };\n\n    this.send(endCallEvent);\n  }\n\n  public disconnect() {\n    if (this.socket) {\n      this.socket.close();\n      this.socket = null;\n    }\n    this.isConnected = false;\n    this.streamSid = null;\n    this.sequenceNumber = 0;\n  }\n\n  public getConnectionStatus(): boolean {\n    return this.isConnected;\n  }\n\n  public getStreamId(): string | null {\n    return this.streamSid;\n  }\n}\n\nexport const websocketService = new WebSocketService();"], "mappings": "AAAA;AACA;AACA;AACA;;AAwGA,MAAMA,gBAAgB,CAAC;EAgBrBC,WAAWA,CAAA,EAAG;IAAA,KAfNC,MAAM,GAAqB,IAAI;IAAA,KAC/BC,SAAS,GAA8B,CAAC,CAAC;IAAA,KACzCC,cAAc,GAAG,CAAC;IAAA,KAClBC,SAAS,GAAkB,IAAI;IAAA,KAC/BC,WAAW,GAAG,KAAK;IAAA,KACnBC,iBAAiB,GAAG,CAAC;IAAA,KACrBC,oBAAoB,GAAG,CAAC;IAAA,KACxBC,cAAc,GAAG,IAAI;IAE7B;IAAA,KACQC,YAAY,GAAwB,IAAI;IAAA,KACxCC,UAAU,GAAkB,EAAE;IAAA,KAC9BC,cAAc,GAAG,KAAK;IAAA,KACtBC,eAAe,GAAkB,IAAI;IAG3C,IAAI,CAACC,sBAAsB,CAAC,CAAC;EAC/B;EAEQA,sBAAsBA,CAAA,EAAG;IAC/B,IAAI;MACF,IAAI,CAACJ,YAAY,GAAG,KAAKK,MAAM,CAACC,YAAY,IAAKD,MAAM,CAASE,kBAAkB,EAAE,CAAC;IACvF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;IAC7D;EACF;EAEOE,YAAYA,CAACjB,SAAoC,EAAE;IACxD,IAAI,CAACA,SAAS,GAAGA,SAAS;EAC5B;EAEOkB,OAAOA,CAACC,GAAW,EAAiB;IACzC,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;MACtC,IAAI;QACF,IAAI,CAACvB,MAAM,GAAG,IAAIwB,SAAS,CAACJ,GAAG,CAAC;QAEhC,IAAI,CAACpB,MAAM,CAACyB,MAAM,GAAG,MAAM;UACzBR,OAAO,CAACS,GAAG,CAAC,qBAAqB,CAAC;UAClC,IAAI,CAACtB,WAAW,GAAG,IAAI;UACvB,IAAI,CAACC,iBAAiB,GAAG,CAAC;;UAE1B;UACA,IAAI,CAACsB,kBAAkB,CAAC,CAAC;UAEzB,IAAI,IAAI,CAAC1B,SAAS,CAAC2B,WAAW,EAAE;YAC9B,IAAI,CAAC3B,SAAS,CAAC2B,WAAW,CAAC,CAAC;UAC9B;UACAN,OAAO,CAAC,CAAC;QACX,CAAC;QAED,IAAI,CAACtB,MAAM,CAAC6B,SAAS,GAAIC,KAAK,IAAK;UACjC,IAAI;YACF,MAAMC,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACH,KAAK,CAACC,IAAI,CAAC;YACnC,IAAI,CAACG,mBAAmB,CAACH,IAAI,CAAC;UAChC,CAAC,CAAC,OAAOf,KAAK,EAAE;YACdC,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;YACxD,IAAI,IAAI,CAACf,SAAS,CAACkC,OAAO,EAAE;cAC1B,IAAI,CAAClC,SAAS,CAACkC,OAAO,CAAC,IAAIC,KAAK,CAAC,yBAAyB,CAAC,CAAC;YAC9D;UACF;QACF,CAAC;QAED,IAAI,CAACpC,MAAM,CAACqC,OAAO,GAAG,MAAM;UAC1BpB,OAAO,CAACS,GAAG,CAAC,wBAAwB,CAAC;UACrC,IAAI,CAACtB,WAAW,GAAG,KAAK;UAExB,IAAI,IAAI,CAACH,SAAS,CAACqC,cAAc,EAAE;YACjC,IAAI,CAACrC,SAAS,CAACqC,cAAc,CAAC,CAAC;UACjC;;UAEA;UACA,IAAI,CAACC,gBAAgB,CAACnB,GAAG,CAAC;QAC5B,CAAC;QAED,IAAI,CAACpB,MAAM,CAACwC,OAAO,GAAIxB,KAAK,IAAK;UAC/BC,OAAO,CAACD,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;UACxC,IAAI,IAAI,CAACf,SAAS,CAACkC,OAAO,EAAE;YAC1B,IAAI,CAAClC,SAAS,CAACkC,OAAO,CAAC,IAAIC,KAAK,CAAC,4BAA4B,CAAC,CAAC;UACjE;UACAb,MAAM,CAACP,KAAK,CAAC;QACf,CAAC;MAEH,CAAC,CAAC,OAAOA,KAAK,EAAE;QACdO,MAAM,CAACP,KAAK,CAAC;MACf;IACF,CAAC,CAAC;EACJ;EAEQuB,gBAAgBA,CAACnB,GAAW,EAAE;IACpC,IAAI,IAAI,CAACf,iBAAiB,GAAG,IAAI,CAACC,oBAAoB,EAAE;MACtD,IAAI,CAACD,iBAAiB,EAAE;MACxBY,OAAO,CAACS,GAAG,CAAC,4BAA4B,IAAI,CAACrB,iBAAiB,IAAI,IAAI,CAACC,oBAAoB,GAAG,CAAC;MAE/FmC,UAAU,CAAC,MAAM;QACf,IAAI,CAACtB,OAAO,CAACC,GAAG,CAAC;MACnB,CAAC,EAAE,IAAI,CAACb,cAAc,GAAG,IAAI,CAACF,iBAAiB,CAAC;IAClD,CAAC,MAAM;MACLY,OAAO,CAACD,KAAK,CAAC,mCAAmC,CAAC;MAClD,IAAI,IAAI,CAACf,SAAS,CAACkC,OAAO,EAAE;QAC1B,IAAI,CAAClC,SAAS,CAACkC,OAAO,CAAC,IAAIC,KAAK,CAAC,4CAA4C,CAAC,CAAC;MACjF;IACF;EACF;EAEQF,mBAAmBA,CAACJ,KAAoB,EAAE;IAChDb,OAAO,CAACS,GAAG,CAAC,0BAA0B,EAAEI,KAAK,CAACA,KAAK,CAAC;IAEpD,QAAQA,KAAK,CAACA,KAAK;MACjB,KAAK,WAAW;QACd;QACA;MAEF,KAAK,OAAO;QACV,IAAI,CAAC3B,SAAS,GAAG2B,KAAK,CAAC3B,SAAS;QAChC,IAAI,IAAI,CAACF,SAAS,CAACyC,eAAe,EAAE;UAClC,IAAI,CAACzC,SAAS,CAACyC,eAAe,CAACZ,KAAK,CAAC;QACvC;QACA;MAEF,KAAK,OAAO;QACV,IAAI,CAACa,gBAAgB,CAACb,KAAK,CAAC;QAC5B,IAAI,IAAI,CAAC7B,SAAS,CAAC2C,eAAe,EAAE;UAClC,IAAI,CAAC3C,SAAS,CAAC2C,eAAe,CAACd,KAAK,CAAC;QACvC;QACA;MAEF,KAAK,MAAM;QACT,IAAI,CAACe,eAAe,CAACf,KAAK,CAAC;QAC3B,IAAI,IAAI,CAAC7B,SAAS,CAAC6C,cAAc,EAAE;UACjC,IAAI,CAAC7C,SAAS,CAAC6C,cAAc,CAAChB,KAAK,CAAC;QACtC;QACA;MAEF,KAAK,MAAM;QACT,IAAI,IAAI,CAAC7B,SAAS,CAAC8C,cAAc,EAAE;UACjC,IAAI,CAAC9C,SAAS,CAAC8C,cAAc,CAACjB,KAAK,CAAC;QACtC;QACA;MAEF,KAAK,OAAO;QACV,IAAI,CAACkB,gBAAgB,CAAC,CAAC;QACvB,IAAI,IAAI,CAAC/C,SAAS,CAACgD,eAAe,EAAE;UAClC,IAAI,CAAChD,SAAS,CAACgD,eAAe,CAACnB,KAAK,CAAC;QACvC;QACA;MAEF,KAAK,MAAM;QACT,IAAI,IAAI,CAAC7B,SAAS,CAACiD,cAAc,EAAE;UACjC,IAAI,CAACjD,SAAS,CAACiD,cAAc,CAACpB,KAAK,CAAC;QACtC;QACA;IACJ;EACF;EAEA,MAAca,gBAAgBA,CAACb,KAAiB,EAAE;IAChD,IAAI;MACF;MACA,MAAMqB,SAAS,GAAG,IAAI,CAACC,mBAAmB,CAACtB,KAAK,CAACuB,KAAK,CAACC,OAAO,CAAC;;MAE/D;MACA,MAAMC,WAAW,GAAG,MAAM,IAAI,CAACC,iBAAiB,CAACL,SAAS,CAAC;;MAE3D;MACA,IAAI,CAAC1C,UAAU,CAACgD,IAAI,CAACF,WAAW,CAAC;;MAEjC;MACA,IAAI,CAAC,IAAI,CAAC7C,cAAc,EAAE;QACxB,IAAI,CAACgD,aAAa,CAAC,CAAC;MACtB;IAEF,CAAC,CAAC,OAAO1C,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;IACrD;EACF;EAEQ6B,eAAeA,CAACf,KAAgB,EAAE;IACxC;IACA,IAAI,IAAI,CAAC7B,SAAS,CAAC0D,uBAAuB,EAAE;MAC1C,IAAI,CAAC1D,SAAS,CAAC0D,uBAAuB,CAAC7B,KAAK,CAAC8B,IAAI,CAACC,IAAI,CAAC;IACzD;EACF;EAEQT,mBAAmBA,CAACU,MAAc,EAAe;IACvD,MAAMC,YAAY,GAAGlD,MAAM,CAACmD,IAAI,CAACF,MAAM,CAAC;IACxC,MAAMG,GAAG,GAAGF,YAAY,CAACG,MAAM;IAC/B,MAAMC,KAAK,GAAG,IAAIC,UAAU,CAACH,GAAG,CAAC;IACjC,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,GAAG,EAAEI,CAAC,EAAE,EAAE;MAC5BF,KAAK,CAACE,CAAC,CAAC,GAAGN,YAAY,CAACO,UAAU,CAACD,CAAC,CAAC;IACvC;IACA,OAAOF,KAAK,CAACI,MAAM;EACrB;EAEA,MAAcf,iBAAiBA,CAACgB,SAAsB,EAAwB;IAC5E,IAAI,CAAC,IAAI,CAAChE,YAAY,EAAE;MACtB,MAAM,IAAI4B,KAAK,CAAC,+BAA+B,CAAC;IAClD;;IAEA;IACA,MAAMqC,UAAU,GAAG,IAAIL,UAAU,CAACI,SAAS,CAAC;IAC5C,MAAME,QAAQ,GAAG,IAAIC,UAAU,CAACF,UAAU,CAACP,MAAM,CAAC;;IAElD;IACA,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGI,UAAU,CAACP,MAAM,EAAEG,CAAC,EAAE,EAAE;MAC1CK,QAAQ,CAACL,CAAC,CAAC,GAAG,IAAI,CAACO,aAAa,CAACH,UAAU,CAACJ,CAAC,CAAC,CAAC;IACjD;;IAEA;IACA,MAAMd,WAAW,GAAG,IAAI,CAAC/C,YAAY,CAACqE,YAAY,CAAC,CAAC,EAAEH,QAAQ,CAACR,MAAM,EAAE,IAAI,CAAC;IAC5E,MAAMY,WAAW,GAAGvB,WAAW,CAACwB,cAAc,CAAC,CAAC,CAAC;;IAEjD;IACA,KAAK,IAAIV,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGK,QAAQ,CAACR,MAAM,EAAEG,CAAC,EAAE,EAAE;MACxCS,WAAW,CAACT,CAAC,CAAC,GAAGK,QAAQ,CAACL,CAAC,CAAC,GAAG,OAAO;IACxC;IAEA,OAAOd,WAAW;EACpB;EAEQqB,aAAaA,CAACI,KAAa,EAAU;IAC3C;IACAA,KAAK,GAAG,CAACA,KAAK;IACd,MAAMC,IAAI,GAAGD,KAAK,GAAG,IAAI;IACzB,MAAME,QAAQ,GAAIF,KAAK,IAAI,CAAC,GAAI,IAAI;IACpC,MAAMG,QAAQ,GAAGH,KAAK,GAAG,IAAI;IAE7B,IAAII,MAAM,GAAGD,QAAQ,IAAKD,QAAQ,GAAG,CAAE;IACvC,IAAIA,QAAQ,KAAK,CAAC,EAAE;MAClBE,MAAM,IAAI,IAAI,IAAIF,QAAQ;IAC5B;IAEA,OAAOD,IAAI,GAAG,CAACG,MAAM,GAAGA,MAAM;EAChC;EAEA,MAAc1B,aAAaA,CAAA,EAAG;IAC5B,IAAI,IAAI,CAACjD,UAAU,CAACyD,MAAM,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC1D,YAAY,EAAE;MACtD,IAAI,CAACE,cAAc,GAAG,KAAK;MAC3B;IACF;IAEA,IAAI,CAACA,cAAc,GAAG,IAAI;IAC1B,MAAM6C,WAAW,GAAG,IAAI,CAAC9C,UAAU,CAAC4E,KAAK,CAAC,CAAE;IAE5C,MAAMC,MAAM,GAAG,IAAI,CAAC9E,YAAY,CAAC+E,kBAAkB,CAAC,CAAC;IACrDD,MAAM,CAACf,MAAM,GAAGhB,WAAW;IAC3B+B,MAAM,CAACnE,OAAO,CAAC,IAAI,CAACX,YAAY,CAACgF,WAAW,CAAC;IAE7CF,MAAM,CAACG,OAAO,GAAG,MAAM;MACrB;MACA,IAAI,IAAI,CAAC9E,eAAe,EAAE;QACxB,IAAI,CAAC+E,aAAa,CAAC,IAAI,CAAC/E,eAAe,CAAC;QACxC,IAAI,CAACA,eAAe,GAAG,IAAI;MAC7B;;MAEA;MACA,IAAI,CAAC+C,aAAa,CAAC,CAAC;IACtB,CAAC;IAED4B,MAAM,CAACK,KAAK,CAAC,CAAC;EAChB;EAEQ3C,gBAAgBA,CAAA,EAAG;IACzB,IAAI,CAACvC,UAAU,GAAG,EAAE;;IAEpB;IACA,IAAI,IAAI,CAACE,eAAe,EAAE;MACxB,IAAI,CAAC+E,aAAa,CAAC,IAAI,CAAC/E,eAAe,CAAC;MACxC,IAAI,CAACA,eAAe,GAAG,IAAI;IAC7B;EACF;;EAEA;EACOgB,kBAAkBA,CAAA,EAAG;IAC1B,MAAMG,KAAqB,GAAG;MAC5BA,KAAK,EAAE;IACT,CAAC;IACD,IAAI,CAAC8D,IAAI,CAAC9D,KAAK,CAAC;EAClB;EAEO+D,cAAcA,CAAC1C,SAAsB,EAAEhD,SAAiB,EAAE;IAC/D,IAAI,CAAC,IAAI,CAACC,WAAW,IAAI,CAAC,IAAI,CAACJ,MAAM,EAAE;MACrCiB,OAAO,CAAC6E,IAAI,CAAC,wCAAwC,CAAC;MACtD;IACF;IAEA,IAAI,CAAC5F,cAAc,EAAE;IAErB,MAAM4B,KAAiB,GAAG;MACxBA,KAAK,EAAE,OAAO;MACd5B,cAAc,EAAE,IAAI,CAACA,cAAc,CAAC6F,QAAQ,CAAC,CAAC;MAC9C1C,KAAK,EAAE;QACL2C,KAAK,EAAE,GAAG;QAAE;QACZC,SAAS,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,CAACJ,QAAQ,CAAC,CAAC;QAChCzC,OAAO,EAAE,IAAI,CAAC8C,mBAAmB,CAACjD,SAAS;MAC7C,CAAC;MACDhD;IACF,CAAC;IAED,IAAI,CAACyF,IAAI,CAAC9D,KAAK,CAAC;EAClB;EAEO4D,aAAaA,CAACW,QAAgB,EAAE;IACrC,IAAI,CAAC,IAAI,CAACjG,WAAW,IAAI,CAAC,IAAI,CAACJ,MAAM,IAAI,CAAC,IAAI,CAACG,SAAS,EAAE;MACxDc,OAAO,CAAC6E,IAAI,CAAC,oDAAoD,CAAC;MAClE;IACF;IAEA,IAAI,CAAC5F,cAAc,EAAE;IAErB,MAAM4B,KAAgB,GAAG;MACvBA,KAAK,EAAE,MAAM;MACb5B,cAAc,EAAE,IAAI,CAACA,cAAc,CAAC6F,QAAQ,CAAC,CAAC;MAC9C5F,SAAS,EAAE,IAAI,CAACA,SAAS;MACzByD,IAAI,EAAE;QACJC,IAAI,EAAEwC;MACR;IACF,CAAC;IAED,IAAI,CAACT,IAAI,CAAC9D,KAAK,CAAC;EAClB;EAEOwE,aAAaA,CAACC,KAAa,EAAE;IAClC,IAAI,CAAC,IAAI,CAACnG,WAAW,IAAI,CAAC,IAAI,CAACJ,MAAM,IAAI,CAAC,IAAI,CAACG,SAAS,EAAE;MACxDc,OAAO,CAAC6E,IAAI,CAAC,oDAAoD,CAAC;MAClE;IACF;IAEA,IAAI,CAAC5F,cAAc,EAAE;IAErB,MAAM4B,KAAgB,GAAG;MACvBA,KAAK,EAAE,MAAM;MACb3B,SAAS,EAAE,IAAI,CAACA,SAAS;MACzBD,cAAc,EAAE,IAAI,CAACA,cAAc,CAAC6F,QAAQ,CAAC,CAAC;MAC9CS,IAAI,EAAE;QACJD;MACF;IACF,CAAC;IAED,IAAI,CAACX,IAAI,CAAC9D,KAAK,CAAC;EAClB;EAEO2E,cAAcA,CAAA,EAAG;IACtB,IAAI,CAAC,IAAI,CAACrG,WAAW,IAAI,CAAC,IAAI,CAACJ,MAAM,IAAI,CAAC,IAAI,CAACG,SAAS,EAAE;MACxDc,OAAO,CAAC6E,IAAI,CAAC,qDAAqD,CAAC;MACnE;IACF;IAEA,MAAMhE,KAAiB,GAAG;MACxBA,KAAK,EAAE,OAAO;MACd3B,SAAS,EAAE,IAAI,CAACA;IAClB,CAAC;IAED,IAAI,CAACyF,IAAI,CAAC9D,KAAK,CAAC;EAClB;EAEQsE,mBAAmBA,CAAC7B,MAAmB,EAAU;IACvD,MAAMJ,KAAK,GAAG,IAAIC,UAAU,CAACG,MAAM,CAAC;IACpC,IAAImC,MAAM,GAAG,EAAE;IACf,KAAK,IAAIrC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,KAAK,CAACwC,UAAU,EAAEtC,CAAC,EAAE,EAAE;MACzCqC,MAAM,IAAIE,MAAM,CAACC,YAAY,CAAC1C,KAAK,CAACE,CAAC,CAAC,CAAC;IACzC;IACA,OAAOxD,MAAM,CAACiG,IAAI,CAACJ,MAAM,CAAC;EAC5B;EAEQd,IAAIA,CAAC9D,KAA0B,EAAE;IACvC,IAAI,IAAI,CAAC9B,MAAM,IAAI,IAAI,CAACI,WAAW,EAAE;MACnC,IAAI,CAACJ,MAAM,CAAC4F,IAAI,CAAC5D,IAAI,CAAC+E,SAAS,CAACjF,KAAK,CAAC,CAAC;IACzC;EACF;;EAEA;EACOkF,oBAAoBA,CAACC,QAK3B,EAAU;IACT;IACA,MAAMC,SAAS,GAAG,WAAWhB,IAAI,CAACC,GAAG,CAAC,CAAC,IAAIgB,IAAI,CAACC,MAAM,CAAC,CAAC,CAACrB,QAAQ,CAAC,EAAE,CAAC,CAACsB,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;;IAEpF;IACA,MAAMC,SAAS,GAAG;MAChBxF,KAAK,EAAE,eAAe;MACtBoF,SAAS;MACTD,QAAQ;MACRhB,SAAS,EAAEC,IAAI,CAACC,GAAG,CAAC;IACtB,CAAC;IAED,IAAI,CAACP,IAAI,CAAC0B,SAAS,CAAC;IACpB,OAAOJ,SAAS;EAClB;EAEOK,aAAaA,CAACL,SAAiB,EAAEM,UAIvC,EAAE;IACD,MAAMC,cAAc,GAAG;MACrB3F,KAAK,EAAE,YAAY;MACnBoF,SAAS;MACTM,UAAU,EAAE;QACVE,IAAI,EAAE,CAAAF,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEE,IAAI,KAAI,UAAU;QACpCC,EAAE,EAAE,CAAAH,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEG,EAAE,KAAI,cAAc;QACpCC,gBAAgB,EAAE,CAAAJ,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEI,gBAAgB,KAAI,CAAC;MACrD,CAAC;MACD3B,SAAS,EAAEC,IAAI,CAACC,GAAG,CAAC;IACtB,CAAC;IAED,IAAI,CAACP,IAAI,CAAC6B,cAAc,CAAC;EAC3B;EAEOI,WAAWA,CAACX,SAAiB,EAAEY,MAAc,GAAG,YAAY,EAAE;IACnE,MAAMC,YAAY,GAAG;MACnBjG,KAAK,EAAE,UAAU;MACjBoF,SAAS;MACTY,MAAM;MACN7B,SAAS,EAAEC,IAAI,CAACC,GAAG,CAAC;IACtB,CAAC;IAED,IAAI,CAACP,IAAI,CAACmC,YAAY,CAAC;EACzB;EAEOC,UAAUA,CAAA,EAAG;IAClB,IAAI,IAAI,CAAChI,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACiI,KAAK,CAAC,CAAC;MACnB,IAAI,CAACjI,MAAM,GAAG,IAAI;IACpB;IACA,IAAI,CAACI,WAAW,GAAG,KAAK;IACxB,IAAI,CAACD,SAAS,GAAG,IAAI;IACrB,IAAI,CAACD,cAAc,GAAG,CAAC;EACzB;EAEOgI,mBAAmBA,CAAA,EAAY;IACpC,OAAO,IAAI,CAAC9H,WAAW;EACzB;EAEO+H,WAAWA,CAAA,EAAkB;IAClC,OAAO,IAAI,CAAChI,SAAS;EACvB;AACF;AAEA,OAAO,MAAMiI,gBAAgB,GAAG,IAAItI,gBAAgB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}