{"ast": null, "code": "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getDialogUtilityClass(slot) {\n  return generateUtilityClass('MuiDialog', slot);\n}\nconst dialogClasses = generateUtilityClasses('MuiDialog', ['root', 'scrollPaper', 'scrollBody', 'container', 'paper', 'paperScrollPaper', 'paperScrollBody', 'paperWidthFalse', 'paperWidthXs', 'paperWidthSm', 'paperWidthMd', 'paperWidthLg', 'paperWidthXl', 'paperFullWidth', 'paperFullScreen']);\nexport default dialogClasses;", "map": {"version": 3, "names": ["generateUtilityClasses", "generateUtilityClass", "getDialogUtilityClass", "slot", "dialogClasses"], "sources": ["/home/<USER>/PycharmProjects/ai-voice-mate-dev/ai_voice_mate/frontend/node_modules/@mui/material/Dialog/dialogClasses.js"], "sourcesContent": ["import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getDialogUtilityClass(slot) {\n  return generateUtilityClass('MuiDialog', slot);\n}\nconst dialogClasses = generateUtilityClasses('MuiDialog', ['root', 'scrollPaper', 'scrollBody', 'container', 'paper', 'paperScrollPaper', 'paperScrollBody', 'paperWidthFalse', 'paperWidthXs', 'paperWidthSm', 'paperWidthMd', 'paperWidthLg', 'paperWidthXl', 'paperFullWidth', 'paperFullScreen']);\nexport default dialogClasses;"], "mappings": "AAAA,OAAOA,sBAAsB,MAAM,mCAAmC;AACtE,OAAOC,oBAAoB,MAAM,iCAAiC;AAClE,OAAO,SAASC,qBAAqBA,CAACC,IAAI,EAAE;EAC1C,OAAOF,oBAAoB,CAAC,WAAW,EAAEE,IAAI,CAAC;AAChD;AACA,MAAMC,aAAa,GAAGJ,sBAAsB,CAAC,WAAW,EAAE,CAAC,MAAM,EAAE,aAAa,EAAE,YAAY,EAAE,WAAW,EAAE,OAAO,EAAE,kBAAkB,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,gBAAgB,EAAE,iBAAiB,CAAC,CAAC;AACrS,eAAeI,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}