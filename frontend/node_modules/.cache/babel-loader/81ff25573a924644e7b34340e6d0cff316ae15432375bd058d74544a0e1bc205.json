{"ast": null, "code": "import getWindow from \"./getWindow.js\";\nexport default function getComputedStyle(element) {\n  return getWindow(element).getComputedStyle(element);\n}", "map": {"version": 3, "names": ["getWindow", "getComputedStyle", "element"], "sources": ["/home/<USER>/PycharmProjects/ai-voice-mate-dev/ai_voice_mate/frontend/node_modules/@popperjs/core/lib/dom-utils/getComputedStyle.js"], "sourcesContent": ["import getWindow from \"./getWindow.js\";\nexport default function getComputedStyle(element) {\n  return getWindow(element).getComputedStyle(element);\n}"], "mappings": "AAAA,OAAOA,SAAS,MAAM,gBAAgB;AACtC,eAAe,SAASC,gBAAgBA,CAACC,OAAO,EAAE;EAChD,OAAOF,SAAS,CAACE,OAAO,CAAC,CAACD,gBAAgB,CAACC,OAAO,CAAC;AACrD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}