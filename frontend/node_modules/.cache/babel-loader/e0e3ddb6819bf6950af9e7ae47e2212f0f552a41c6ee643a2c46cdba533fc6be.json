{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nconst EMPTY = [];\n\n/**\n * A React.useEffect equivalent that runs once, when the component is mounted.\n */\nexport default function useOnMount(fn) {\n  /* eslint-disable react-hooks/exhaustive-deps */\n  React.useEffect(fn, EMPTY);\n  /* eslint-enable react-hooks/exhaustive-deps */\n}", "map": {"version": 3, "names": ["React", "EMPTY", "useOnMount", "fn", "useEffect"], "sources": ["/home/<USER>/PycharmProjects/ai-voice-mate-dev/ai_voice_mate/frontend/node_modules/@mui/utils/esm/useOnMount/useOnMount.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nconst EMPTY = [];\n\n/**\n * A React.useEffect equivalent that runs once, when the component is mounted.\n */\nexport default function useOnMount(fn) {\n  /* eslint-disable react-hooks/exhaustive-deps */\n  React.useEffect(fn, EMPTY);\n  /* eslint-enable react-hooks/exhaustive-deps */\n}"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,MAAMC,KAAK,GAAG,EAAE;;AAEhB;AACA;AACA;AACA,eAAe,SAASC,UAAUA,CAACC,EAAE,EAAE;EACrC;EACAH,KAAK,CAACI,SAAS,CAACD,EAAE,EAAEF,KAAK,CAAC;EAC1B;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}