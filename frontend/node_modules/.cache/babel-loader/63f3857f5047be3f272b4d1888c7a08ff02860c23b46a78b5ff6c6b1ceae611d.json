{"ast": null, "code": "export { default } from './useMediaQuery';\nexport * from './useMediaQuery';", "map": {"version": 3, "names": ["default"], "sources": ["/home/<USER>/PycharmProjects/ai-voice-mate-dev/ai_voice_mate/frontend/node_modules/@mui/system/esm/useMediaQuery/index.js"], "sourcesContent": ["export { default } from './useMediaQuery';\nexport * from './useMediaQuery';"], "mappings": "AAAA,SAASA,OAAO,QAAQ,iBAAiB;AACzC,cAAc,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}