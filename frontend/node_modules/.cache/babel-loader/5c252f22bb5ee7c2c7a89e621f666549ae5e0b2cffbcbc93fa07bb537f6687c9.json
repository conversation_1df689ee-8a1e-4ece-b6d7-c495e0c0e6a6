{"ast": null, "code": "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"active\", \"children\", \"className\", \"component\", \"completed\", \"disabled\", \"expanded\", \"index\", \"last\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport integerPropType from '@mui/utils/integerPropType';\nimport composeClasses from '@mui/utils/composeClasses';\nimport StepperContext from '../Stepper/StepperContext';\nimport StepContext from './StepContext';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport styled from '../styles/styled';\nimport { getStepUtilityClass } from './stepClasses';\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    orientation,\n    alternativeLabel,\n    completed\n  } = ownerState;\n  const slots = {\n    root: ['root', orientation, alternativeLabel && 'alternativeLabel', completed && 'completed']\n  };\n  return composeClasses(slots, getStepUtilityClass, classes);\n};\nconst StepRoot = styled('div', {\n  name: 'MuiStep',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, styles[ownerState.orientation], ownerState.alternativeLabel && styles.alternativeLabel, ownerState.completed && styles.completed];\n  }\n})(_ref => {\n  let {\n    ownerState\n  } = _ref;\n  return _extends({}, ownerState.orientation === 'horizontal' && {\n    paddingLeft: 8,\n    paddingRight: 8\n  }, ownerState.alternativeLabel && {\n    flex: 1,\n    position: 'relative'\n  });\n});\nconst Step = /*#__PURE__*/React.forwardRef(function Step(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiStep'\n  });\n  const {\n      active: activeProp,\n      children,\n      className,\n      component = 'div',\n      completed: completedProp,\n      disabled: disabledProp,\n      expanded = false,\n      index,\n      last\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    activeStep,\n    connector,\n    alternativeLabel,\n    orientation,\n    nonLinear\n  } = React.useContext(StepperContext);\n  let [active = false, completed = false, disabled = false] = [activeProp, completedProp, disabledProp];\n  if (activeStep === index) {\n    active = activeProp !== undefined ? activeProp : true;\n  } else if (!nonLinear && activeStep > index) {\n    completed = completedProp !== undefined ? completedProp : true;\n  } else if (!nonLinear && activeStep < index) {\n    disabled = disabledProp !== undefined ? disabledProp : true;\n  }\n  const contextValue = React.useMemo(() => ({\n    index,\n    last,\n    expanded,\n    icon: index + 1,\n    active,\n    completed,\n    disabled\n  }), [index, last, expanded, active, completed, disabled]);\n  const ownerState = _extends({}, props, {\n    active,\n    orientation,\n    alternativeLabel,\n    completed,\n    disabled,\n    expanded,\n    component\n  });\n  const classes = useUtilityClasses(ownerState);\n  const newChildren = /*#__PURE__*/_jsxs(StepRoot, _extends({\n    as: component,\n    className: clsx(classes.root, className),\n    ref: ref,\n    ownerState: ownerState\n  }, other, {\n    children: [connector && alternativeLabel && index !== 0 ? connector : null, children]\n  }));\n  return /*#__PURE__*/_jsx(StepContext.Provider, {\n    value: contextValue,\n    children: connector && !alternativeLabel && index !== 0 ? /*#__PURE__*/_jsxs(React.Fragment, {\n      children: [connector, newChildren]\n    }) : newChildren\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? Step.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * Sets the step as active. Is passed to child components.\n   */\n  active: PropTypes.bool,\n  /**\n   * Should be `Step` sub-components such as `StepLabel`, `StepContent`.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * Mark the step as completed. Is passed to child components.\n   */\n  completed: PropTypes.bool,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * If `true`, the step is disabled, will also disable the button if\n   * `StepButton` is a child of `Step`. Is passed to child components.\n   */\n  disabled: PropTypes.bool,\n  /**\n   * Expand the step.\n   * @default false\n   */\n  expanded: PropTypes.bool,\n  /**\n   * The position of the step.\n   * The prop defaults to the value inherited from the parent Stepper component.\n   */\n  index: integerPropType,\n  /**\n   * If `true`, the Step is displayed as rendered last.\n   * The prop defaults to the value inherited from the parent Stepper component.\n   */\n  last: PropTypes.bool,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default Step;", "map": {"version": 3, "names": ["_objectWithoutPropertiesLoose", "_extends", "_excluded", "React", "PropTypes", "clsx", "integerPropType", "composeClasses", "StepperContext", "StepContext", "useDefaultProps", "styled", "getStepUtilityClass", "jsxs", "_jsxs", "jsx", "_jsx", "useUtilityClasses", "ownerState", "classes", "orientation", "alternativeLabel", "completed", "slots", "root", "StepRoot", "name", "slot", "overridesResolver", "props", "styles", "_ref", "paddingLeft", "paddingRight", "flex", "position", "Step", "forwardRef", "inProps", "ref", "active", "activeProp", "children", "className", "component", "completedProp", "disabled", "disabledProp", "expanded", "index", "last", "other", "activeStep", "connector", "nonLinear", "useContext", "undefined", "contextValue", "useMemo", "icon", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "as", "Provider", "value", "Fragment", "process", "env", "NODE_ENV", "propTypes", "bool", "node", "object", "string", "elementType", "sx", "oneOfType", "arrayOf", "func"], "sources": ["/home/<USER>/PycharmProjects/ai-voice-mate-dev/ai_voice_mate/frontend/node_modules/@mui/material/Step/Step.js"], "sourcesContent": ["'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"active\", \"children\", \"className\", \"component\", \"completed\", \"disabled\", \"expanded\", \"index\", \"last\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport integerPropType from '@mui/utils/integerPropType';\nimport composeClasses from '@mui/utils/composeClasses';\nimport StepperContext from '../Stepper/StepperContext';\nimport StepContext from './StepContext';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport styled from '../styles/styled';\nimport { getStepUtilityClass } from './stepClasses';\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    orientation,\n    alternativeLabel,\n    completed\n  } = ownerState;\n  const slots = {\n    root: ['root', orientation, alternativeLabel && 'alternativeLabel', completed && 'completed']\n  };\n  return composeClasses(slots, getStepUtilityClass, classes);\n};\nconst StepRoot = styled('div', {\n  name: 'MuiStep',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, styles[ownerState.orientation], ownerState.alternativeLabel && styles.alternativeLabel, ownerState.completed && styles.completed];\n  }\n})(({\n  ownerState\n}) => _extends({}, ownerState.orientation === 'horizontal' && {\n  paddingLeft: 8,\n  paddingRight: 8\n}, ownerState.alternativeLabel && {\n  flex: 1,\n  position: 'relative'\n}));\nconst Step = /*#__PURE__*/React.forwardRef(function Step(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiStep'\n  });\n  const {\n      active: activeProp,\n      children,\n      className,\n      component = 'div',\n      completed: completedProp,\n      disabled: disabledProp,\n      expanded = false,\n      index,\n      last\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    activeStep,\n    connector,\n    alternativeLabel,\n    orientation,\n    nonLinear\n  } = React.useContext(StepperContext);\n  let [active = false, completed = false, disabled = false] = [activeProp, completedProp, disabledProp];\n  if (activeStep === index) {\n    active = activeProp !== undefined ? activeProp : true;\n  } else if (!nonLinear && activeStep > index) {\n    completed = completedProp !== undefined ? completedProp : true;\n  } else if (!nonLinear && activeStep < index) {\n    disabled = disabledProp !== undefined ? disabledProp : true;\n  }\n  const contextValue = React.useMemo(() => ({\n    index,\n    last,\n    expanded,\n    icon: index + 1,\n    active,\n    completed,\n    disabled\n  }), [index, last, expanded, active, completed, disabled]);\n  const ownerState = _extends({}, props, {\n    active,\n    orientation,\n    alternativeLabel,\n    completed,\n    disabled,\n    expanded,\n    component\n  });\n  const classes = useUtilityClasses(ownerState);\n  const newChildren = /*#__PURE__*/_jsxs(StepRoot, _extends({\n    as: component,\n    className: clsx(classes.root, className),\n    ref: ref,\n    ownerState: ownerState\n  }, other, {\n    children: [connector && alternativeLabel && index !== 0 ? connector : null, children]\n  }));\n  return /*#__PURE__*/_jsx(StepContext.Provider, {\n    value: contextValue,\n    children: connector && !alternativeLabel && index !== 0 ? /*#__PURE__*/_jsxs(React.Fragment, {\n      children: [connector, newChildren]\n    }) : newChildren\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? Step.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * Sets the step as active. Is passed to child components.\n   */\n  active: PropTypes.bool,\n  /**\n   * Should be `Step` sub-components such as `StepLabel`, `StepContent`.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * Mark the step as completed. Is passed to child components.\n   */\n  completed: PropTypes.bool,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * If `true`, the step is disabled, will also disable the button if\n   * `StepButton` is a child of `Step`. Is passed to child components.\n   */\n  disabled: PropTypes.bool,\n  /**\n   * Expand the step.\n   * @default false\n   */\n  expanded: PropTypes.bool,\n  /**\n   * The position of the step.\n   * The prop defaults to the value inherited from the parent Stepper component.\n   */\n  index: integerPropType,\n  /**\n   * If `true`, the Step is displayed as rendered last.\n   * The prop defaults to the value inherited from the parent Stepper component.\n   */\n  last: PropTypes.bool,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default Step;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,MAAMC,SAAS,GAAG,CAAC,QAAQ,EAAE,UAAU,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,UAAU,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,CAAC;AACxH,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,eAAe,MAAM,4BAA4B;AACxD,OAAOC,cAAc,MAAM,2BAA2B;AACtD,OAAOC,cAAc,MAAM,2BAA2B;AACtD,OAAOC,WAAW,MAAM,eAAe;AACvC,SAASC,eAAe,QAAQ,yBAAyB;AACzD,OAAOC,MAAM,MAAM,kBAAkB;AACrC,SAASC,mBAAmB,QAAQ,eAAe;AACnD,SAASC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AACjD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC,OAAO;IACPC,WAAW;IACXC,gBAAgB;IAChBC;EACF,CAAC,GAAGJ,UAAU;EACd,MAAMK,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,EAAEJ,WAAW,EAAEC,gBAAgB,IAAI,kBAAkB,EAAEC,SAAS,IAAI,WAAW;EAC9F,CAAC;EACD,OAAOf,cAAc,CAACgB,KAAK,EAAEX,mBAAmB,EAAEO,OAAO,CAAC;AAC5D,CAAC;AACD,MAAMM,QAAQ,GAAGd,MAAM,CAAC,KAAK,EAAE;EAC7Be,IAAI,EAAE,SAAS;EACfC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJZ;IACF,CAAC,GAAGW,KAAK;IACT,OAAO,CAACC,MAAM,CAACN,IAAI,EAAEM,MAAM,CAACZ,UAAU,CAACE,WAAW,CAAC,EAAEF,UAAU,CAACG,gBAAgB,IAAIS,MAAM,CAACT,gBAAgB,EAAEH,UAAU,CAACI,SAAS,IAAIQ,MAAM,CAACR,SAAS,CAAC;EACxJ;AACF,CAAC,CAAC,CAACS,IAAA;EAAA,IAAC;IACFb;EACF,CAAC,GAAAa,IAAA;EAAA,OAAK9B,QAAQ,CAAC,CAAC,CAAC,EAAEiB,UAAU,CAACE,WAAW,KAAK,YAAY,IAAI;IAC5DY,WAAW,EAAE,CAAC;IACdC,YAAY,EAAE;EAChB,CAAC,EAAEf,UAAU,CAACG,gBAAgB,IAAI;IAChCa,IAAI,EAAE,CAAC;IACPC,QAAQ,EAAE;EACZ,CAAC,CAAC;AAAA,EAAC;AACH,MAAMC,IAAI,GAAG,aAAajC,KAAK,CAACkC,UAAU,CAAC,SAASD,IAAIA,CAACE,OAAO,EAAEC,GAAG,EAAE;EACrE,MAAMV,KAAK,GAAGnB,eAAe,CAAC;IAC5BmB,KAAK,EAAES,OAAO;IACdZ,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;MACFc,MAAM,EAAEC,UAAU;MAClBC,QAAQ;MACRC,SAAS;MACTC,SAAS,GAAG,KAAK;MACjBtB,SAAS,EAAEuB,aAAa;MACxBC,QAAQ,EAAEC,YAAY;MACtBC,QAAQ,GAAG,KAAK;MAChBC,KAAK;MACLC;IACF,CAAC,GAAGrB,KAAK;IACTsB,KAAK,GAAGnD,6BAA6B,CAAC6B,KAAK,EAAE3B,SAAS,CAAC;EACzD,MAAM;IACJkD,UAAU;IACVC,SAAS;IACThC,gBAAgB;IAChBD,WAAW;IACXkC;EACF,CAAC,GAAGnD,KAAK,CAACoD,UAAU,CAAC/C,cAAc,CAAC;EACpC,IAAI,CAACgC,MAAM,GAAG,KAAK,EAAElB,SAAS,GAAG,KAAK,EAAEwB,QAAQ,GAAG,KAAK,CAAC,GAAG,CAACL,UAAU,EAAEI,aAAa,EAAEE,YAAY,CAAC;EACrG,IAAIK,UAAU,KAAKH,KAAK,EAAE;IACxBT,MAAM,GAAGC,UAAU,KAAKe,SAAS,GAAGf,UAAU,GAAG,IAAI;EACvD,CAAC,MAAM,IAAI,CAACa,SAAS,IAAIF,UAAU,GAAGH,KAAK,EAAE;IAC3C3B,SAAS,GAAGuB,aAAa,KAAKW,SAAS,GAAGX,aAAa,GAAG,IAAI;EAChE,CAAC,MAAM,IAAI,CAACS,SAAS,IAAIF,UAAU,GAAGH,KAAK,EAAE;IAC3CH,QAAQ,GAAGC,YAAY,KAAKS,SAAS,GAAGT,YAAY,GAAG,IAAI;EAC7D;EACA,MAAMU,YAAY,GAAGtD,KAAK,CAACuD,OAAO,CAAC,OAAO;IACxCT,KAAK;IACLC,IAAI;IACJF,QAAQ;IACRW,IAAI,EAAEV,KAAK,GAAG,CAAC;IACfT,MAAM;IACNlB,SAAS;IACTwB;EACF,CAAC,CAAC,EAAE,CAACG,KAAK,EAAEC,IAAI,EAAEF,QAAQ,EAAER,MAAM,EAAElB,SAAS,EAAEwB,QAAQ,CAAC,CAAC;EACzD,MAAM5B,UAAU,GAAGjB,QAAQ,CAAC,CAAC,CAAC,EAAE4B,KAAK,EAAE;IACrCW,MAAM;IACNpB,WAAW;IACXC,gBAAgB;IAChBC,SAAS;IACTwB,QAAQ;IACRE,QAAQ;IACRJ;EACF,CAAC,CAAC;EACF,MAAMzB,OAAO,GAAGF,iBAAiB,CAACC,UAAU,CAAC;EAC7C,MAAM0C,WAAW,GAAG,aAAa9C,KAAK,CAACW,QAAQ,EAAExB,QAAQ,CAAC;IACxD4D,EAAE,EAAEjB,SAAS;IACbD,SAAS,EAAEtC,IAAI,CAACc,OAAO,CAACK,IAAI,EAAEmB,SAAS,CAAC;IACxCJ,GAAG,EAAEA,GAAG;IACRrB,UAAU,EAAEA;EACd,CAAC,EAAEiC,KAAK,EAAE;IACRT,QAAQ,EAAE,CAACW,SAAS,IAAIhC,gBAAgB,IAAI4B,KAAK,KAAK,CAAC,GAAGI,SAAS,GAAG,IAAI,EAAEX,QAAQ;EACtF,CAAC,CAAC,CAAC;EACH,OAAO,aAAa1B,IAAI,CAACP,WAAW,CAACqD,QAAQ,EAAE;IAC7CC,KAAK,EAAEN,YAAY;IACnBf,QAAQ,EAAEW,SAAS,IAAI,CAAChC,gBAAgB,IAAI4B,KAAK,KAAK,CAAC,GAAG,aAAanC,KAAK,CAACX,KAAK,CAAC6D,QAAQ,EAAE;MAC3FtB,QAAQ,EAAE,CAACW,SAAS,EAAEO,WAAW;IACnC,CAAC,CAAC,GAAGA;EACP,CAAC,CAAC;AACJ,CAAC,CAAC;AACFK,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG/B,IAAI,CAACgC,SAAS,CAAC,yBAAyB;EAC9E;EACA;EACA;EACA;EACA;AACF;AACA;EACE5B,MAAM,EAAEpC,SAAS,CAACiE,IAAI;EACtB;AACF;AACA;EACE3B,QAAQ,EAAEtC,SAAS,CAACkE,IAAI;EACxB;AACF;AACA;EACEnD,OAAO,EAAEf,SAAS,CAACmE,MAAM;EACzB;AACF;AACA;EACE5B,SAAS,EAAEvC,SAAS,CAACoE,MAAM;EAC3B;AACF;AACA;EACElD,SAAS,EAAElB,SAAS,CAACiE,IAAI;EACzB;AACF;AACA;AACA;EACEzB,SAAS,EAAExC,SAAS,CAACqE,WAAW;EAChC;AACF;AACA;AACA;EACE3B,QAAQ,EAAE1C,SAAS,CAACiE,IAAI;EACxB;AACF;AACA;AACA;EACErB,QAAQ,EAAE5C,SAAS,CAACiE,IAAI;EACxB;AACF;AACA;AACA;EACEpB,KAAK,EAAE3C,eAAe;EACtB;AACF;AACA;AACA;EACE4C,IAAI,EAAE9C,SAAS,CAACiE,IAAI;EACpB;AACF;AACA;EACEK,EAAE,EAAEtE,SAAS,CAACuE,SAAS,CAAC,CAACvE,SAAS,CAACwE,OAAO,CAACxE,SAAS,CAACuE,SAAS,CAAC,CAACvE,SAAS,CAACyE,IAAI,EAAEzE,SAAS,CAACmE,MAAM,EAAEnE,SAAS,CAACiE,IAAI,CAAC,CAAC,CAAC,EAAEjE,SAAS,CAACyE,IAAI,EAAEzE,SAAS,CAACmE,MAAM,CAAC;AACxJ,CAAC,GAAG,KAAK,CAAC;AACV,eAAenC,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}