{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"onEntering\"],\n  _excluded2 = [\"action\", \"anchorEl\", \"anchorOrigin\", \"anchorPosition\", \"anchorReference\", \"children\", \"className\", \"container\", \"elevation\", \"marginThreshold\", \"open\", \"PaperProps\", \"slots\", \"slotProps\", \"transformOrigin\", \"TransitionComponent\", \"transitionDuration\", \"TransitionProps\", \"disableScrollLock\"],\n  _excluded3 = [\"slotProps\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport HTMLElementType from '@mui/utils/HTMLElementType';\nimport refType from '@mui/utils/refType';\nimport elementTypeAcceptingRef from '@mui/utils/elementTypeAcceptingRef';\nimport integerPropType from '@mui/utils/integerPropType';\nimport chainPropTypes from '@mui/utils/chainPropTypes';\nimport useSlotProps from '@mui/utils/useSlotProps';\nimport isHostComponent from '@mui/utils/isHostComponent';\nimport styled from '../styles/styled';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport debounce from '../utils/debounce';\nimport ownerDocument from '../utils/ownerDocument';\nimport ownerWindow from '../utils/ownerWindow';\nimport useForkRef from '../utils/useForkRef';\nimport Grow from '../Grow';\nimport Modal from '../Modal';\nimport PaperBase from '../Paper';\nimport { getPopoverUtilityClass } from './popoverClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport function getOffsetTop(rect, vertical) {\n  let offset = 0;\n  if (typeof vertical === 'number') {\n    offset = vertical;\n  } else if (vertical === 'center') {\n    offset = rect.height / 2;\n  } else if (vertical === 'bottom') {\n    offset = rect.height;\n  }\n  return offset;\n}\nexport function getOffsetLeft(rect, horizontal) {\n  let offset = 0;\n  if (typeof horizontal === 'number') {\n    offset = horizontal;\n  } else if (horizontal === 'center') {\n    offset = rect.width / 2;\n  } else if (horizontal === 'right') {\n    offset = rect.width;\n  }\n  return offset;\n}\nfunction getTransformOriginValue(transformOrigin) {\n  return [transformOrigin.horizontal, transformOrigin.vertical].map(n => typeof n === 'number' ? `${n}px` : n).join(' ');\n}\nfunction resolveAnchorEl(anchorEl) {\n  return typeof anchorEl === 'function' ? anchorEl() : anchorEl;\n}\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root'],\n    paper: ['paper']\n  };\n  return composeClasses(slots, getPopoverUtilityClass, classes);\n};\nexport const PopoverRoot = styled(Modal, {\n  name: 'MuiPopover',\n  slot: 'Root',\n  overridesResolver: (props, styles) => styles.root\n})({});\nexport const PopoverPaper = styled(PaperBase, {\n  name: 'MuiPopover',\n  slot: 'Paper',\n  overridesResolver: (props, styles) => styles.paper\n})({\n  position: 'absolute',\n  overflowY: 'auto',\n  overflowX: 'hidden',\n  // So we see the popover when it's empty.\n  // It's most likely on issue on userland.\n  minWidth: 16,\n  minHeight: 16,\n  maxWidth: 'calc(100% - 32px)',\n  maxHeight: 'calc(100% - 32px)',\n  // We disable the focus ring for mouse, touch and keyboard users.\n  outline: 0\n});\nconst Popover = /*#__PURE__*/React.forwardRef(function Popover(inProps, ref) {\n  var _slotProps$paper, _slots$root, _slots$paper;\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiPopover'\n  });\n  const {\n      action,\n      anchorEl,\n      anchorOrigin = {\n        vertical: 'top',\n        horizontal: 'left'\n      },\n      anchorPosition,\n      anchorReference = 'anchorEl',\n      children,\n      className,\n      container: containerProp,\n      elevation = 8,\n      marginThreshold = 16,\n      open,\n      PaperProps: PaperPropsProp = {},\n      slots,\n      slotProps,\n      transformOrigin = {\n        vertical: 'top',\n        horizontal: 'left'\n      },\n      TransitionComponent = Grow,\n      transitionDuration: transitionDurationProp = 'auto',\n      TransitionProps: {\n        onEntering\n      } = {},\n      disableScrollLock = false\n    } = props,\n    TransitionProps = _objectWithoutPropertiesLoose(props.TransitionProps, _excluded),\n    other = _objectWithoutPropertiesLoose(props, _excluded2);\n  const externalPaperSlotProps = (_slotProps$paper = slotProps == null ? void 0 : slotProps.paper) != null ? _slotProps$paper : PaperPropsProp;\n  const paperRef = React.useRef();\n  const handlePaperRef = useForkRef(paperRef, externalPaperSlotProps.ref);\n  const ownerState = _extends({}, props, {\n    anchorOrigin,\n    anchorReference,\n    elevation,\n    marginThreshold,\n    externalPaperSlotProps,\n    transformOrigin,\n    TransitionComponent,\n    transitionDuration: transitionDurationProp,\n    TransitionProps\n  });\n  const classes = useUtilityClasses(ownerState);\n\n  // Returns the top/left offset of the position\n  // to attach to on the anchor element (or body if none is provided)\n  const getAnchorOffset = React.useCallback(() => {\n    if (anchorReference === 'anchorPosition') {\n      if (process.env.NODE_ENV !== 'production') {\n        if (!anchorPosition) {\n          console.error('MUI: You need to provide a `anchorPosition` prop when using ' + '<Popover anchorReference=\"anchorPosition\" />.');\n        }\n      }\n      return anchorPosition;\n    }\n    const resolvedAnchorEl = resolveAnchorEl(anchorEl);\n\n    // If an anchor element wasn't provided, just use the parent body element of this Popover\n    const anchorElement = resolvedAnchorEl && resolvedAnchorEl.nodeType === 1 ? resolvedAnchorEl : ownerDocument(paperRef.current).body;\n    const anchorRect = anchorElement.getBoundingClientRect();\n    if (process.env.NODE_ENV !== 'production') {\n      const box = anchorElement.getBoundingClientRect();\n      if (process.env.NODE_ENV !== 'test' && box.top === 0 && box.left === 0 && box.right === 0 && box.bottom === 0) {\n        console.warn(['MUI: The `anchorEl` prop provided to the component is invalid.', 'The anchor element should be part of the document layout.', \"Make sure the element is present in the document or that it's not display none.\"].join('\\n'));\n      }\n    }\n    return {\n      top: anchorRect.top + getOffsetTop(anchorRect, anchorOrigin.vertical),\n      left: anchorRect.left + getOffsetLeft(anchorRect, anchorOrigin.horizontal)\n    };\n  }, [anchorEl, anchorOrigin.horizontal, anchorOrigin.vertical, anchorPosition, anchorReference]);\n\n  // Returns the base transform origin using the element\n  const getTransformOrigin = React.useCallback(elemRect => {\n    return {\n      vertical: getOffsetTop(elemRect, transformOrigin.vertical),\n      horizontal: getOffsetLeft(elemRect, transformOrigin.horizontal)\n    };\n  }, [transformOrigin.horizontal, transformOrigin.vertical]);\n  const getPositioningStyle = React.useCallback(element => {\n    const elemRect = {\n      width: element.offsetWidth,\n      height: element.offsetHeight\n    };\n\n    // Get the transform origin point on the element itself\n    const elemTransformOrigin = getTransformOrigin(elemRect);\n    if (anchorReference === 'none') {\n      return {\n        top: null,\n        left: null,\n        transformOrigin: getTransformOriginValue(elemTransformOrigin)\n      };\n    }\n\n    // Get the offset of the anchoring element\n    const anchorOffset = getAnchorOffset();\n\n    // Calculate element positioning\n    let top = anchorOffset.top - elemTransformOrigin.vertical;\n    let left = anchorOffset.left - elemTransformOrigin.horizontal;\n    const bottom = top + elemRect.height;\n    const right = left + elemRect.width;\n\n    // Use the parent window of the anchorEl if provided\n    const containerWindow = ownerWindow(resolveAnchorEl(anchorEl));\n\n    // Window thresholds taking required margin into account\n    const heightThreshold = containerWindow.innerHeight - marginThreshold;\n    const widthThreshold = containerWindow.innerWidth - marginThreshold;\n\n    // Check if the vertical axis needs shifting\n    if (marginThreshold !== null && top < marginThreshold) {\n      const diff = top - marginThreshold;\n      top -= diff;\n      elemTransformOrigin.vertical += diff;\n    } else if (marginThreshold !== null && bottom > heightThreshold) {\n      const diff = bottom - heightThreshold;\n      top -= diff;\n      elemTransformOrigin.vertical += diff;\n    }\n    if (process.env.NODE_ENV !== 'production') {\n      if (elemRect.height > heightThreshold && elemRect.height && heightThreshold) {\n        console.error(['MUI: The popover component is too tall.', `Some part of it can not be seen on the screen (${elemRect.height - heightThreshold}px).`, 'Please consider adding a `max-height` to improve the user-experience.'].join('\\n'));\n      }\n    }\n\n    // Check if the horizontal axis needs shifting\n    if (marginThreshold !== null && left < marginThreshold) {\n      const diff = left - marginThreshold;\n      left -= diff;\n      elemTransformOrigin.horizontal += diff;\n    } else if (right > widthThreshold) {\n      const diff = right - widthThreshold;\n      left -= diff;\n      elemTransformOrigin.horizontal += diff;\n    }\n    return {\n      top: `${Math.round(top)}px`,\n      left: `${Math.round(left)}px`,\n      transformOrigin: getTransformOriginValue(elemTransformOrigin)\n    };\n  }, [anchorEl, anchorReference, getAnchorOffset, getTransformOrigin, marginThreshold]);\n  const [isPositioned, setIsPositioned] = React.useState(open);\n  const setPositioningStyles = React.useCallback(() => {\n    const element = paperRef.current;\n    if (!element) {\n      return;\n    }\n    const positioning = getPositioningStyle(element);\n    if (positioning.top !== null) {\n      element.style.top = positioning.top;\n    }\n    if (positioning.left !== null) {\n      element.style.left = positioning.left;\n    }\n    element.style.transformOrigin = positioning.transformOrigin;\n    setIsPositioned(true);\n  }, [getPositioningStyle]);\n  React.useEffect(() => {\n    if (disableScrollLock) {\n      window.addEventListener('scroll', setPositioningStyles);\n    }\n    return () => window.removeEventListener('scroll', setPositioningStyles);\n  }, [anchorEl, disableScrollLock, setPositioningStyles]);\n  const handleEntering = (element, isAppearing) => {\n    if (onEntering) {\n      onEntering(element, isAppearing);\n    }\n    setPositioningStyles();\n  };\n  const handleExited = () => {\n    setIsPositioned(false);\n  };\n  React.useEffect(() => {\n    if (open) {\n      setPositioningStyles();\n    }\n  });\n  React.useImperativeHandle(action, () => open ? {\n    updatePosition: () => {\n      setPositioningStyles();\n    }\n  } : null, [open, setPositioningStyles]);\n  React.useEffect(() => {\n    if (!open) {\n      return undefined;\n    }\n    const handleResize = debounce(() => {\n      setPositioningStyles();\n    });\n    const containerWindow = ownerWindow(anchorEl);\n    containerWindow.addEventListener('resize', handleResize);\n    return () => {\n      handleResize.clear();\n      containerWindow.removeEventListener('resize', handleResize);\n    };\n  }, [anchorEl, open, setPositioningStyles]);\n  let transitionDuration = transitionDurationProp;\n  if (transitionDurationProp === 'auto' && !TransitionComponent.muiSupportAuto) {\n    transitionDuration = undefined;\n  }\n\n  // If the container prop is provided, use that\n  // If the anchorEl prop is provided, use its parent body element as the container\n  // If neither are provided let the Modal take care of choosing the container\n  const container = containerProp || (anchorEl ? ownerDocument(resolveAnchorEl(anchorEl)).body : undefined);\n  const RootSlot = (_slots$root = slots == null ? void 0 : slots.root) != null ? _slots$root : PopoverRoot;\n  const PaperSlot = (_slots$paper = slots == null ? void 0 : slots.paper) != null ? _slots$paper : PopoverPaper;\n  const paperProps = useSlotProps({\n    elementType: PaperSlot,\n    externalSlotProps: _extends({}, externalPaperSlotProps, {\n      style: isPositioned ? externalPaperSlotProps.style : _extends({}, externalPaperSlotProps.style, {\n        opacity: 0\n      })\n    }),\n    additionalProps: {\n      elevation,\n      ref: handlePaperRef\n    },\n    ownerState,\n    className: clsx(classes.paper, externalPaperSlotProps == null ? void 0 : externalPaperSlotProps.className)\n  });\n  const _useSlotProps = useSlotProps({\n      elementType: RootSlot,\n      externalSlotProps: (slotProps == null ? void 0 : slotProps.root) || {},\n      externalForwardedProps: other,\n      additionalProps: {\n        ref,\n        slotProps: {\n          backdrop: {\n            invisible: true\n          }\n        },\n        container,\n        open\n      },\n      ownerState,\n      className: clsx(classes.root, className)\n    }),\n    {\n      slotProps: rootSlotPropsProp\n    } = _useSlotProps,\n    rootProps = _objectWithoutPropertiesLoose(_useSlotProps, _excluded3);\n  return /*#__PURE__*/_jsx(RootSlot, _extends({}, rootProps, !isHostComponent(RootSlot) && {\n    slotProps: rootSlotPropsProp,\n    disableScrollLock\n  }, {\n    children: /*#__PURE__*/_jsx(TransitionComponent, _extends({\n      appear: true,\n      in: open,\n      onEntering: handleEntering,\n      onExited: handleExited,\n      timeout: transitionDuration\n    }, TransitionProps, {\n      children: /*#__PURE__*/_jsx(PaperSlot, _extends({}, paperProps, {\n        children: children\n      }))\n    }))\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? Popover.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * A ref for imperative actions.\n   * It currently only supports updatePosition() action.\n   */\n  action: refType,\n  /**\n   * An HTML element, [PopoverVirtualElement](/material-ui/react-popover/#virtual-element),\n   * or a function that returns either.\n   * It's used to set the position of the popover.\n   */\n  anchorEl: chainPropTypes(PropTypes.oneOfType([HTMLElementType, PropTypes.func]), props => {\n    if (props.open && (!props.anchorReference || props.anchorReference === 'anchorEl')) {\n      const resolvedAnchorEl = resolveAnchorEl(props.anchorEl);\n      if (resolvedAnchorEl && resolvedAnchorEl.nodeType === 1) {\n        const box = resolvedAnchorEl.getBoundingClientRect();\n        if (process.env.NODE_ENV !== 'test' && box.top === 0 && box.left === 0 && box.right === 0 && box.bottom === 0) {\n          return new Error(['MUI: The `anchorEl` prop provided to the component is invalid.', 'The anchor element should be part of the document layout.', \"Make sure the element is present in the document or that it's not display none.\"].join('\\n'));\n        }\n      } else {\n        return new Error(['MUI: The `anchorEl` prop provided to the component is invalid.', `It should be an Element or PopoverVirtualElement instance but it's \\`${resolvedAnchorEl}\\` instead.`].join('\\n'));\n      }\n    }\n    return null;\n  }),\n  /**\n   * This is the point on the anchor where the popover's\n   * `anchorEl` will attach to. This is not used when the\n   * anchorReference is 'anchorPosition'.\n   *\n   * Options:\n   * vertical: [top, center, bottom];\n   * horizontal: [left, center, right].\n   * @default {\n   *   vertical: 'top',\n   *   horizontal: 'left',\n   * }\n   */\n  anchorOrigin: PropTypes.shape({\n    horizontal: PropTypes.oneOfType([PropTypes.oneOf(['center', 'left', 'right']), PropTypes.number]).isRequired,\n    vertical: PropTypes.oneOfType([PropTypes.oneOf(['bottom', 'center', 'top']), PropTypes.number]).isRequired\n  }),\n  /**\n   * This is the position that may be used to set the position of the popover.\n   * The coordinates are relative to the application's client area.\n   */\n  anchorPosition: PropTypes.shape({\n    left: PropTypes.number.isRequired,\n    top: PropTypes.number.isRequired\n  }),\n  /**\n   * This determines which anchor prop to refer to when setting\n   * the position of the popover.\n   * @default 'anchorEl'\n   */\n  anchorReference: PropTypes.oneOf(['anchorEl', 'anchorPosition', 'none']),\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * An HTML element, component instance, or function that returns either.\n   * The `container` will passed to the Modal component.\n   *\n   * By default, it uses the body of the anchorEl's top-level document object,\n   * so it's simply `document.body` most of the time.\n   */\n  container: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([HTMLElementType, PropTypes.func]),\n  /**\n   * Disable the scroll lock behavior.\n   * @default false\n   */\n  disableScrollLock: PropTypes.bool,\n  /**\n   * The elevation of the popover.\n   * @default 8\n   */\n  elevation: integerPropType,\n  /**\n   * Specifies how close to the edge of the window the popover can appear.\n   * If null, the popover will not be constrained by the window.\n   * @default 16\n   */\n  marginThreshold: PropTypes.number,\n  /**\n   * Callback fired when the component requests to be closed.\n   * The `reason` parameter can optionally be used to control the response to `onClose`.\n   */\n  onClose: PropTypes.func,\n  /**\n   * If `true`, the component is shown.\n   */\n  open: PropTypes.bool.isRequired,\n  /**\n   * Props applied to the [`Paper`](/material-ui/api/paper/) element.\n   *\n   * This prop is an alias for `slotProps.paper` and will be overriden by it if both are used.\n   * @deprecated Use `slotProps.paper` instead.\n   *\n   * @default {}\n   */\n  PaperProps: PropTypes /* @typescript-to-proptypes-ignore */.shape({\n    component: elementTypeAcceptingRef\n  }),\n  /**\n   * The extra props for the slot components.\n   * You can override the existing props or add new ones.\n   *\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    paper: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   *\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    paper: PropTypes.elementType,\n    root: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * This is the point on the popover which\n   * will attach to the anchor's origin.\n   *\n   * Options:\n   * vertical: [top, center, bottom, x(px)];\n   * horizontal: [left, center, right, x(px)].\n   * @default {\n   *   vertical: 'top',\n   *   horizontal: 'left',\n   * }\n   */\n  transformOrigin: PropTypes.shape({\n    horizontal: PropTypes.oneOfType([PropTypes.oneOf(['center', 'left', 'right']), PropTypes.number]).isRequired,\n    vertical: PropTypes.oneOfType([PropTypes.oneOf(['bottom', 'center', 'top']), PropTypes.number]).isRequired\n  }),\n  /**\n   * The component used for the transition.\n   * [Follow this guide](/material-ui/transitions/#transitioncomponent-prop) to learn more about the requirements for this component.\n   * @default Grow\n   */\n  TransitionComponent: PropTypes.elementType,\n  /**\n   * Set to 'auto' to automatically calculate transition time based on height.\n   * @default 'auto'\n   */\n  transitionDuration: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.number, PropTypes.shape({\n    appear: PropTypes.number,\n    enter: PropTypes.number,\n    exit: PropTypes.number\n  })]),\n  /**\n   * Props applied to the transition element.\n   * By default, the element is based on this [`Transition`](https://reactcommunity.org/react-transition-group/transition/) component.\n   * @default {}\n   */\n  TransitionProps: PropTypes.object\n} : void 0;\nexport default Popover;", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "_excluded2", "_excluded3", "React", "PropTypes", "clsx", "composeClasses", "HTMLElementType", "refType", "elementTypeAcceptingRef", "integerPropType", "chainPropTypes", "useSlotProps", "isHostComponent", "styled", "useDefaultProps", "debounce", "ownerDocument", "ownerWindow", "useForkRef", "Grow", "Modal", "PaperBase", "getPopoverUtilityClass", "jsx", "_jsx", "getOffsetTop", "rect", "vertical", "offset", "height", "getOffsetLeft", "horizontal", "width", "getTransformOriginValue", "transform<PERSON><PERSON>in", "map", "n", "join", "resolveAnchorEl", "anchorEl", "useUtilityClasses", "ownerState", "classes", "slots", "root", "paper", "PopoverRoot", "name", "slot", "overridesResolver", "props", "styles", "PopoverPaper", "position", "overflowY", "overflowX", "min<PERSON><PERSON><PERSON>", "minHeight", "max<PERSON><PERSON><PERSON>", "maxHeight", "outline", "Popover", "forwardRef", "inProps", "ref", "_slotProps$paper", "_slots$root", "_slots$paper", "action", "anchor<PERSON><PERSON><PERSON>", "anchorPosition", "anchorReference", "children", "className", "container", "containerProp", "elevation", "marginT<PERSON><PERSON>old", "open", "PaperProps", "PaperPropsProp", "slotProps", "TransitionComponent", "transitionDuration", "transitionDurationProp", "TransitionProps", "onEntering", "disableScrollLock", "other", "externalPaperSlotProps", "paperRef", "useRef", "handlePaperRef", "getAnchorOffset", "useCallback", "process", "env", "NODE_ENV", "console", "error", "resolvedAnchorEl", "anchorElement", "nodeType", "current", "body", "anchorRect", "getBoundingClientRect", "box", "top", "left", "right", "bottom", "warn", "getTransformOrigin", "elemRect", "getPositioningStyle", "element", "offsetWidth", "offsetHeight", "elemTransformOrigin", "anchorOffset", "containerWindow", "heightThreshold", "innerHeight", "widthThreshold", "innerWidth", "diff", "Math", "round", "isPositioned", "setIsPositioned", "useState", "setPositioningStyles", "positioning", "style", "useEffect", "window", "addEventListener", "removeEventListener", "handleEntering", "isAppearing", "handleExited", "useImperativeHandle", "updatePosition", "undefined", "handleResize", "clear", "muiSupportAuto", "RootSlot", "PaperSlot", "paperProps", "elementType", "externalSlotProps", "opacity", "additionalProps", "_useSlotProps", "externalForwardedProps", "backdrop", "invisible", "rootSlotPropsProp", "rootProps", "appear", "in", "onExited", "timeout", "propTypes", "oneOfType", "func", "Error", "shape", "oneOf", "number", "isRequired", "node", "object", "string", "bool", "onClose", "component", "sx", "arrayOf", "enter", "exit"], "sources": ["/home/<USER>/PycharmProjects/ai-voice-mate-dev/ai_voice_mate/frontend/node_modules/@mui/material/Popover/Popover.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"onEntering\"],\n  _excluded2 = [\"action\", \"anchorEl\", \"anchorOrigin\", \"anchorPosition\", \"anchorReference\", \"children\", \"className\", \"container\", \"elevation\", \"marginThreshold\", \"open\", \"PaperProps\", \"slots\", \"slotProps\", \"transformOrigin\", \"TransitionComponent\", \"transitionDuration\", \"TransitionProps\", \"disableScrollLock\"],\n  _excluded3 = [\"slotProps\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport HTMLElementType from '@mui/utils/HTMLElementType';\nimport refType from '@mui/utils/refType';\nimport elementTypeAcceptingRef from '@mui/utils/elementTypeAcceptingRef';\nimport integerPropType from '@mui/utils/integerPropType';\nimport chainPropTypes from '@mui/utils/chainPropTypes';\nimport useSlotProps from '@mui/utils/useSlotProps';\nimport isHostComponent from '@mui/utils/isHostComponent';\nimport styled from '../styles/styled';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport debounce from '../utils/debounce';\nimport ownerDocument from '../utils/ownerDocument';\nimport ownerWindow from '../utils/ownerWindow';\nimport useForkRef from '../utils/useForkRef';\nimport Grow from '../Grow';\nimport Modal from '../Modal';\nimport PaperBase from '../Paper';\nimport { getPopoverUtilityClass } from './popoverClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport function getOffsetTop(rect, vertical) {\n  let offset = 0;\n  if (typeof vertical === 'number') {\n    offset = vertical;\n  } else if (vertical === 'center') {\n    offset = rect.height / 2;\n  } else if (vertical === 'bottom') {\n    offset = rect.height;\n  }\n  return offset;\n}\nexport function getOffsetLeft(rect, horizontal) {\n  let offset = 0;\n  if (typeof horizontal === 'number') {\n    offset = horizontal;\n  } else if (horizontal === 'center') {\n    offset = rect.width / 2;\n  } else if (horizontal === 'right') {\n    offset = rect.width;\n  }\n  return offset;\n}\nfunction getTransformOriginValue(transformOrigin) {\n  return [transformOrigin.horizontal, transformOrigin.vertical].map(n => typeof n === 'number' ? `${n}px` : n).join(' ');\n}\nfunction resolveAnchorEl(anchorEl) {\n  return typeof anchorEl === 'function' ? anchorEl() : anchorEl;\n}\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root'],\n    paper: ['paper']\n  };\n  return composeClasses(slots, getPopoverUtilityClass, classes);\n};\nexport const PopoverRoot = styled(Modal, {\n  name: 'MuiPopover',\n  slot: 'Root',\n  overridesResolver: (props, styles) => styles.root\n})({});\nexport const PopoverPaper = styled(PaperBase, {\n  name: 'MuiPopover',\n  slot: 'Paper',\n  overridesResolver: (props, styles) => styles.paper\n})({\n  position: 'absolute',\n  overflowY: 'auto',\n  overflowX: 'hidden',\n  // So we see the popover when it's empty.\n  // It's most likely on issue on userland.\n  minWidth: 16,\n  minHeight: 16,\n  maxWidth: 'calc(100% - 32px)',\n  maxHeight: 'calc(100% - 32px)',\n  // We disable the focus ring for mouse, touch and keyboard users.\n  outline: 0\n});\nconst Popover = /*#__PURE__*/React.forwardRef(function Popover(inProps, ref) {\n  var _slotProps$paper, _slots$root, _slots$paper;\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiPopover'\n  });\n  const {\n      action,\n      anchorEl,\n      anchorOrigin = {\n        vertical: 'top',\n        horizontal: 'left'\n      },\n      anchorPosition,\n      anchorReference = 'anchorEl',\n      children,\n      className,\n      container: containerProp,\n      elevation = 8,\n      marginThreshold = 16,\n      open,\n      PaperProps: PaperPropsProp = {},\n      slots,\n      slotProps,\n      transformOrigin = {\n        vertical: 'top',\n        horizontal: 'left'\n      },\n      TransitionComponent = Grow,\n      transitionDuration: transitionDurationProp = 'auto',\n      TransitionProps: {\n        onEntering\n      } = {},\n      disableScrollLock = false\n    } = props,\n    TransitionProps = _objectWithoutPropertiesLoose(props.TransitionProps, _excluded),\n    other = _objectWithoutPropertiesLoose(props, _excluded2);\n  const externalPaperSlotProps = (_slotProps$paper = slotProps == null ? void 0 : slotProps.paper) != null ? _slotProps$paper : PaperPropsProp;\n  const paperRef = React.useRef();\n  const handlePaperRef = useForkRef(paperRef, externalPaperSlotProps.ref);\n  const ownerState = _extends({}, props, {\n    anchorOrigin,\n    anchorReference,\n    elevation,\n    marginThreshold,\n    externalPaperSlotProps,\n    transformOrigin,\n    TransitionComponent,\n    transitionDuration: transitionDurationProp,\n    TransitionProps\n  });\n  const classes = useUtilityClasses(ownerState);\n\n  // Returns the top/left offset of the position\n  // to attach to on the anchor element (or body if none is provided)\n  const getAnchorOffset = React.useCallback(() => {\n    if (anchorReference === 'anchorPosition') {\n      if (process.env.NODE_ENV !== 'production') {\n        if (!anchorPosition) {\n          console.error('MUI: You need to provide a `anchorPosition` prop when using ' + '<Popover anchorReference=\"anchorPosition\" />.');\n        }\n      }\n      return anchorPosition;\n    }\n    const resolvedAnchorEl = resolveAnchorEl(anchorEl);\n\n    // If an anchor element wasn't provided, just use the parent body element of this Popover\n    const anchorElement = resolvedAnchorEl && resolvedAnchorEl.nodeType === 1 ? resolvedAnchorEl : ownerDocument(paperRef.current).body;\n    const anchorRect = anchorElement.getBoundingClientRect();\n    if (process.env.NODE_ENV !== 'production') {\n      const box = anchorElement.getBoundingClientRect();\n      if (process.env.NODE_ENV !== 'test' && box.top === 0 && box.left === 0 && box.right === 0 && box.bottom === 0) {\n        console.warn(['MUI: The `anchorEl` prop provided to the component is invalid.', 'The anchor element should be part of the document layout.', \"Make sure the element is present in the document or that it's not display none.\"].join('\\n'));\n      }\n    }\n    return {\n      top: anchorRect.top + getOffsetTop(anchorRect, anchorOrigin.vertical),\n      left: anchorRect.left + getOffsetLeft(anchorRect, anchorOrigin.horizontal)\n    };\n  }, [anchorEl, anchorOrigin.horizontal, anchorOrigin.vertical, anchorPosition, anchorReference]);\n\n  // Returns the base transform origin using the element\n  const getTransformOrigin = React.useCallback(elemRect => {\n    return {\n      vertical: getOffsetTop(elemRect, transformOrigin.vertical),\n      horizontal: getOffsetLeft(elemRect, transformOrigin.horizontal)\n    };\n  }, [transformOrigin.horizontal, transformOrigin.vertical]);\n  const getPositioningStyle = React.useCallback(element => {\n    const elemRect = {\n      width: element.offsetWidth,\n      height: element.offsetHeight\n    };\n\n    // Get the transform origin point on the element itself\n    const elemTransformOrigin = getTransformOrigin(elemRect);\n    if (anchorReference === 'none') {\n      return {\n        top: null,\n        left: null,\n        transformOrigin: getTransformOriginValue(elemTransformOrigin)\n      };\n    }\n\n    // Get the offset of the anchoring element\n    const anchorOffset = getAnchorOffset();\n\n    // Calculate element positioning\n    let top = anchorOffset.top - elemTransformOrigin.vertical;\n    let left = anchorOffset.left - elemTransformOrigin.horizontal;\n    const bottom = top + elemRect.height;\n    const right = left + elemRect.width;\n\n    // Use the parent window of the anchorEl if provided\n    const containerWindow = ownerWindow(resolveAnchorEl(anchorEl));\n\n    // Window thresholds taking required margin into account\n    const heightThreshold = containerWindow.innerHeight - marginThreshold;\n    const widthThreshold = containerWindow.innerWidth - marginThreshold;\n\n    // Check if the vertical axis needs shifting\n    if (marginThreshold !== null && top < marginThreshold) {\n      const diff = top - marginThreshold;\n      top -= diff;\n      elemTransformOrigin.vertical += diff;\n    } else if (marginThreshold !== null && bottom > heightThreshold) {\n      const diff = bottom - heightThreshold;\n      top -= diff;\n      elemTransformOrigin.vertical += diff;\n    }\n    if (process.env.NODE_ENV !== 'production') {\n      if (elemRect.height > heightThreshold && elemRect.height && heightThreshold) {\n        console.error(['MUI: The popover component is too tall.', `Some part of it can not be seen on the screen (${elemRect.height - heightThreshold}px).`, 'Please consider adding a `max-height` to improve the user-experience.'].join('\\n'));\n      }\n    }\n\n    // Check if the horizontal axis needs shifting\n    if (marginThreshold !== null && left < marginThreshold) {\n      const diff = left - marginThreshold;\n      left -= diff;\n      elemTransformOrigin.horizontal += diff;\n    } else if (right > widthThreshold) {\n      const diff = right - widthThreshold;\n      left -= diff;\n      elemTransformOrigin.horizontal += diff;\n    }\n    return {\n      top: `${Math.round(top)}px`,\n      left: `${Math.round(left)}px`,\n      transformOrigin: getTransformOriginValue(elemTransformOrigin)\n    };\n  }, [anchorEl, anchorReference, getAnchorOffset, getTransformOrigin, marginThreshold]);\n  const [isPositioned, setIsPositioned] = React.useState(open);\n  const setPositioningStyles = React.useCallback(() => {\n    const element = paperRef.current;\n    if (!element) {\n      return;\n    }\n    const positioning = getPositioningStyle(element);\n    if (positioning.top !== null) {\n      element.style.top = positioning.top;\n    }\n    if (positioning.left !== null) {\n      element.style.left = positioning.left;\n    }\n    element.style.transformOrigin = positioning.transformOrigin;\n    setIsPositioned(true);\n  }, [getPositioningStyle]);\n  React.useEffect(() => {\n    if (disableScrollLock) {\n      window.addEventListener('scroll', setPositioningStyles);\n    }\n    return () => window.removeEventListener('scroll', setPositioningStyles);\n  }, [anchorEl, disableScrollLock, setPositioningStyles]);\n  const handleEntering = (element, isAppearing) => {\n    if (onEntering) {\n      onEntering(element, isAppearing);\n    }\n    setPositioningStyles();\n  };\n  const handleExited = () => {\n    setIsPositioned(false);\n  };\n  React.useEffect(() => {\n    if (open) {\n      setPositioningStyles();\n    }\n  });\n  React.useImperativeHandle(action, () => open ? {\n    updatePosition: () => {\n      setPositioningStyles();\n    }\n  } : null, [open, setPositioningStyles]);\n  React.useEffect(() => {\n    if (!open) {\n      return undefined;\n    }\n    const handleResize = debounce(() => {\n      setPositioningStyles();\n    });\n    const containerWindow = ownerWindow(anchorEl);\n    containerWindow.addEventListener('resize', handleResize);\n    return () => {\n      handleResize.clear();\n      containerWindow.removeEventListener('resize', handleResize);\n    };\n  }, [anchorEl, open, setPositioningStyles]);\n  let transitionDuration = transitionDurationProp;\n  if (transitionDurationProp === 'auto' && !TransitionComponent.muiSupportAuto) {\n    transitionDuration = undefined;\n  }\n\n  // If the container prop is provided, use that\n  // If the anchorEl prop is provided, use its parent body element as the container\n  // If neither are provided let the Modal take care of choosing the container\n  const container = containerProp || (anchorEl ? ownerDocument(resolveAnchorEl(anchorEl)).body : undefined);\n  const RootSlot = (_slots$root = slots == null ? void 0 : slots.root) != null ? _slots$root : PopoverRoot;\n  const PaperSlot = (_slots$paper = slots == null ? void 0 : slots.paper) != null ? _slots$paper : PopoverPaper;\n  const paperProps = useSlotProps({\n    elementType: PaperSlot,\n    externalSlotProps: _extends({}, externalPaperSlotProps, {\n      style: isPositioned ? externalPaperSlotProps.style : _extends({}, externalPaperSlotProps.style, {\n        opacity: 0\n      })\n    }),\n    additionalProps: {\n      elevation,\n      ref: handlePaperRef\n    },\n    ownerState,\n    className: clsx(classes.paper, externalPaperSlotProps == null ? void 0 : externalPaperSlotProps.className)\n  });\n  const _useSlotProps = useSlotProps({\n      elementType: RootSlot,\n      externalSlotProps: (slotProps == null ? void 0 : slotProps.root) || {},\n      externalForwardedProps: other,\n      additionalProps: {\n        ref,\n        slotProps: {\n          backdrop: {\n            invisible: true\n          }\n        },\n        container,\n        open\n      },\n      ownerState,\n      className: clsx(classes.root, className)\n    }),\n    {\n      slotProps: rootSlotPropsProp\n    } = _useSlotProps,\n    rootProps = _objectWithoutPropertiesLoose(_useSlotProps, _excluded3);\n  return /*#__PURE__*/_jsx(RootSlot, _extends({}, rootProps, !isHostComponent(RootSlot) && {\n    slotProps: rootSlotPropsProp,\n    disableScrollLock\n  }, {\n    children: /*#__PURE__*/_jsx(TransitionComponent, _extends({\n      appear: true,\n      in: open,\n      onEntering: handleEntering,\n      onExited: handleExited,\n      timeout: transitionDuration\n    }, TransitionProps, {\n      children: /*#__PURE__*/_jsx(PaperSlot, _extends({}, paperProps, {\n        children: children\n      }))\n    }))\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? Popover.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * A ref for imperative actions.\n   * It currently only supports updatePosition() action.\n   */\n  action: refType,\n  /**\n   * An HTML element, [PopoverVirtualElement](/material-ui/react-popover/#virtual-element),\n   * or a function that returns either.\n   * It's used to set the position of the popover.\n   */\n  anchorEl: chainPropTypes(PropTypes.oneOfType([HTMLElementType, PropTypes.func]), props => {\n    if (props.open && (!props.anchorReference || props.anchorReference === 'anchorEl')) {\n      const resolvedAnchorEl = resolveAnchorEl(props.anchorEl);\n      if (resolvedAnchorEl && resolvedAnchorEl.nodeType === 1) {\n        const box = resolvedAnchorEl.getBoundingClientRect();\n        if (process.env.NODE_ENV !== 'test' && box.top === 0 && box.left === 0 && box.right === 0 && box.bottom === 0) {\n          return new Error(['MUI: The `anchorEl` prop provided to the component is invalid.', 'The anchor element should be part of the document layout.', \"Make sure the element is present in the document or that it's not display none.\"].join('\\n'));\n        }\n      } else {\n        return new Error(['MUI: The `anchorEl` prop provided to the component is invalid.', `It should be an Element or PopoverVirtualElement instance but it's \\`${resolvedAnchorEl}\\` instead.`].join('\\n'));\n      }\n    }\n    return null;\n  }),\n  /**\n   * This is the point on the anchor where the popover's\n   * `anchorEl` will attach to. This is not used when the\n   * anchorReference is 'anchorPosition'.\n   *\n   * Options:\n   * vertical: [top, center, bottom];\n   * horizontal: [left, center, right].\n   * @default {\n   *   vertical: 'top',\n   *   horizontal: 'left',\n   * }\n   */\n  anchorOrigin: PropTypes.shape({\n    horizontal: PropTypes.oneOfType([PropTypes.oneOf(['center', 'left', 'right']), PropTypes.number]).isRequired,\n    vertical: PropTypes.oneOfType([PropTypes.oneOf(['bottom', 'center', 'top']), PropTypes.number]).isRequired\n  }),\n  /**\n   * This is the position that may be used to set the position of the popover.\n   * The coordinates are relative to the application's client area.\n   */\n  anchorPosition: PropTypes.shape({\n    left: PropTypes.number.isRequired,\n    top: PropTypes.number.isRequired\n  }),\n  /**\n   * This determines which anchor prop to refer to when setting\n   * the position of the popover.\n   * @default 'anchorEl'\n   */\n  anchorReference: PropTypes.oneOf(['anchorEl', 'anchorPosition', 'none']),\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * An HTML element, component instance, or function that returns either.\n   * The `container` will passed to the Modal component.\n   *\n   * By default, it uses the body of the anchorEl's top-level document object,\n   * so it's simply `document.body` most of the time.\n   */\n  container: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([HTMLElementType, PropTypes.func]),\n  /**\n   * Disable the scroll lock behavior.\n   * @default false\n   */\n  disableScrollLock: PropTypes.bool,\n  /**\n   * The elevation of the popover.\n   * @default 8\n   */\n  elevation: integerPropType,\n  /**\n   * Specifies how close to the edge of the window the popover can appear.\n   * If null, the popover will not be constrained by the window.\n   * @default 16\n   */\n  marginThreshold: PropTypes.number,\n  /**\n   * Callback fired when the component requests to be closed.\n   * The `reason` parameter can optionally be used to control the response to `onClose`.\n   */\n  onClose: PropTypes.func,\n  /**\n   * If `true`, the component is shown.\n   */\n  open: PropTypes.bool.isRequired,\n  /**\n   * Props applied to the [`Paper`](/material-ui/api/paper/) element.\n   *\n   * This prop is an alias for `slotProps.paper` and will be overriden by it if both are used.\n   * @deprecated Use `slotProps.paper` instead.\n   *\n   * @default {}\n   */\n  PaperProps: PropTypes /* @typescript-to-proptypes-ignore */.shape({\n    component: elementTypeAcceptingRef\n  }),\n  /**\n   * The extra props for the slot components.\n   * You can override the existing props or add new ones.\n   *\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    paper: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   *\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    paper: PropTypes.elementType,\n    root: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * This is the point on the popover which\n   * will attach to the anchor's origin.\n   *\n   * Options:\n   * vertical: [top, center, bottom, x(px)];\n   * horizontal: [left, center, right, x(px)].\n   * @default {\n   *   vertical: 'top',\n   *   horizontal: 'left',\n   * }\n   */\n  transformOrigin: PropTypes.shape({\n    horizontal: PropTypes.oneOfType([PropTypes.oneOf(['center', 'left', 'right']), PropTypes.number]).isRequired,\n    vertical: PropTypes.oneOfType([PropTypes.oneOf(['bottom', 'center', 'top']), PropTypes.number]).isRequired\n  }),\n  /**\n   * The component used for the transition.\n   * [Follow this guide](/material-ui/transitions/#transitioncomponent-prop) to learn more about the requirements for this component.\n   * @default Grow\n   */\n  TransitionComponent: PropTypes.elementType,\n  /**\n   * Set to 'auto' to automatically calculate transition time based on height.\n   * @default 'auto'\n   */\n  transitionDuration: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.number, PropTypes.shape({\n    appear: PropTypes.number,\n    enter: PropTypes.number,\n    exit: PropTypes.number\n  })]),\n  /**\n   * Props applied to the transition element.\n   * By default, the element is based on this [`Transition`](https://reactcommunity.org/react-transition-group/transition/) component.\n   * @default {}\n   */\n  TransitionProps: PropTypes.object\n} : void 0;\nexport default Popover;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,YAAY,CAAC;EAC9BC,UAAU,GAAG,CAAC,QAAQ,EAAE,UAAU,EAAE,cAAc,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,UAAU,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,iBAAiB,EAAE,MAAM,EAAE,YAAY,EAAE,OAAO,EAAE,WAAW,EAAE,iBAAiB,EAAE,qBAAqB,EAAE,oBAAoB,EAAE,iBAAiB,EAAE,mBAAmB,CAAC;EAClTC,UAAU,GAAG,CAAC,WAAW,CAAC;AAC5B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,cAAc,MAAM,2BAA2B;AACtD,OAAOC,eAAe,MAAM,4BAA4B;AACxD,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAOC,uBAAuB,MAAM,oCAAoC;AACxE,OAAOC,eAAe,MAAM,4BAA4B;AACxD,OAAOC,cAAc,MAAM,2BAA2B;AACtD,OAAOC,YAAY,MAAM,yBAAyB;AAClD,OAAOC,eAAe,MAAM,4BAA4B;AACxD,OAAOC,MAAM,MAAM,kBAAkB;AACrC,SAASC,eAAe,QAAQ,yBAAyB;AACzD,OAAOC,QAAQ,MAAM,mBAAmB;AACxC,OAAOC,aAAa,MAAM,wBAAwB;AAClD,OAAOC,WAAW,MAAM,sBAAsB;AAC9C,OAAOC,UAAU,MAAM,qBAAqB;AAC5C,OAAOC,IAAI,MAAM,SAAS;AAC1B,OAAOC,KAAK,MAAM,UAAU;AAC5B,OAAOC,SAAS,MAAM,UAAU;AAChC,SAASC,sBAAsB,QAAQ,kBAAkB;AACzD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,OAAO,SAASC,YAAYA,CAACC,IAAI,EAAEC,QAAQ,EAAE;EAC3C,IAAIC,MAAM,GAAG,CAAC;EACd,IAAI,OAAOD,QAAQ,KAAK,QAAQ,EAAE;IAChCC,MAAM,GAAGD,QAAQ;EACnB,CAAC,MAAM,IAAIA,QAAQ,KAAK,QAAQ,EAAE;IAChCC,MAAM,GAAGF,IAAI,CAACG,MAAM,GAAG,CAAC;EAC1B,CAAC,MAAM,IAAIF,QAAQ,KAAK,QAAQ,EAAE;IAChCC,MAAM,GAAGF,IAAI,CAACG,MAAM;EACtB;EACA,OAAOD,MAAM;AACf;AACA,OAAO,SAASE,aAAaA,CAACJ,IAAI,EAAEK,UAAU,EAAE;EAC9C,IAAIH,MAAM,GAAG,CAAC;EACd,IAAI,OAAOG,UAAU,KAAK,QAAQ,EAAE;IAClCH,MAAM,GAAGG,UAAU;EACrB,CAAC,MAAM,IAAIA,UAAU,KAAK,QAAQ,EAAE;IAClCH,MAAM,GAAGF,IAAI,CAACM,KAAK,GAAG,CAAC;EACzB,CAAC,MAAM,IAAID,UAAU,KAAK,OAAO,EAAE;IACjCH,MAAM,GAAGF,IAAI,CAACM,KAAK;EACrB;EACA,OAAOJ,MAAM;AACf;AACA,SAASK,uBAAuBA,CAACC,eAAe,EAAE;EAChD,OAAO,CAACA,eAAe,CAACH,UAAU,EAAEG,eAAe,CAACP,QAAQ,CAAC,CAACQ,GAAG,CAACC,CAAC,IAAI,OAAOA,CAAC,KAAK,QAAQ,GAAG,GAAGA,CAAC,IAAI,GAAGA,CAAC,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;AACxH;AACA,SAASC,eAAeA,CAACC,QAAQ,EAAE;EACjC,OAAO,OAAOA,QAAQ,KAAK,UAAU,GAAGA,QAAQ,CAAC,CAAC,GAAGA,QAAQ;AAC/D;AACA,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC;EACF,CAAC,GAAGD,UAAU;EACd,MAAME,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,CAAC;IACdC,KAAK,EAAE,CAAC,OAAO;EACjB,CAAC;EACD,OAAOxC,cAAc,CAACsC,KAAK,EAAErB,sBAAsB,EAAEoB,OAAO,CAAC;AAC/D,CAAC;AACD,OAAO,MAAMI,WAAW,GAAGjC,MAAM,CAACO,KAAK,EAAE;EACvC2B,IAAI,EAAE,YAAY;EAClBC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAKA,MAAM,CAACP;AAC/C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACN,OAAO,MAAMQ,YAAY,GAAGvC,MAAM,CAACQ,SAAS,EAAE;EAC5C0B,IAAI,EAAE,YAAY;EAClBC,IAAI,EAAE,OAAO;EACbC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAKA,MAAM,CAACN;AAC/C,CAAC,CAAC,CAAC;EACDQ,QAAQ,EAAE,UAAU;EACpBC,SAAS,EAAE,MAAM;EACjBC,SAAS,EAAE,QAAQ;EACnB;EACA;EACAC,QAAQ,EAAE,EAAE;EACZC,SAAS,EAAE,EAAE;EACbC,QAAQ,EAAE,mBAAmB;EAC7BC,SAAS,EAAE,mBAAmB;EAC9B;EACAC,OAAO,EAAE;AACX,CAAC,CAAC;AACF,MAAMC,OAAO,GAAG,aAAa3D,KAAK,CAAC4D,UAAU,CAAC,SAASD,OAAOA,CAACE,OAAO,EAAEC,GAAG,EAAE;EAC3E,IAAIC,gBAAgB,EAAEC,WAAW,EAAEC,YAAY;EAC/C,MAAMjB,KAAK,GAAGpC,eAAe,CAAC;IAC5BoC,KAAK,EAAEa,OAAO;IACdhB,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;MACFqB,MAAM;MACN7B,QAAQ;MACR8B,YAAY,GAAG;QACb1C,QAAQ,EAAE,KAAK;QACfI,UAAU,EAAE;MACd,CAAC;MACDuC,cAAc;MACdC,eAAe,GAAG,UAAU;MAC5BC,QAAQ;MACRC,SAAS;MACTC,SAAS,EAAEC,aAAa;MACxBC,SAAS,GAAG,CAAC;MACbC,eAAe,GAAG,EAAE;MACpBC,IAAI;MACJC,UAAU,EAAEC,cAAc,GAAG,CAAC,CAAC;MAC/BrC,KAAK;MACLsC,SAAS;MACT/C,eAAe,GAAG;QAChBP,QAAQ,EAAE,KAAK;QACfI,UAAU,EAAE;MACd,CAAC;MACDmD,mBAAmB,GAAG/D,IAAI;MAC1BgE,kBAAkB,EAAEC,sBAAsB,GAAG,MAAM;MACnDC,eAAe,EAAE;QACfC;MACF,CAAC,GAAG,CAAC,CAAC;MACNC,iBAAiB,GAAG;IACtB,CAAC,GAAGrC,KAAK;IACTmC,eAAe,GAAGvF,6BAA6B,CAACoD,KAAK,CAACmC,eAAe,EAAEtF,SAAS,CAAC;IACjFyF,KAAK,GAAG1F,6BAA6B,CAACoD,KAAK,EAAElD,UAAU,CAAC;EAC1D,MAAMyF,sBAAsB,GAAG,CAACxB,gBAAgB,GAAGgB,SAAS,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACpC,KAAK,KAAK,IAAI,GAAGoB,gBAAgB,GAAGe,cAAc;EAC5I,MAAMU,QAAQ,GAAGxF,KAAK,CAACyF,MAAM,CAAC,CAAC;EAC/B,MAAMC,cAAc,GAAG1E,UAAU,CAACwE,QAAQ,EAAED,sBAAsB,CAACzB,GAAG,CAAC;EACvE,MAAMvB,UAAU,GAAG5C,QAAQ,CAAC,CAAC,CAAC,EAAEqD,KAAK,EAAE;IACrCmB,YAAY;IACZE,eAAe;IACfK,SAAS;IACTC,eAAe;IACfY,sBAAsB;IACtBvD,eAAe;IACfgD,mBAAmB;IACnBC,kBAAkB,EAAEC,sBAAsB;IAC1CC;EACF,CAAC,CAAC;EACF,MAAM3C,OAAO,GAAGF,iBAAiB,CAACC,UAAU,CAAC;;EAE7C;EACA;EACA,MAAMoD,eAAe,GAAG3F,KAAK,CAAC4F,WAAW,CAAC,MAAM;IAC9C,IAAIvB,eAAe,KAAK,gBAAgB,EAAE;MACxC,IAAIwB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzC,IAAI,CAAC3B,cAAc,EAAE;UACnB4B,OAAO,CAACC,KAAK,CAAC,8DAA8D,GAAG,+CAA+C,CAAC;QACjI;MACF;MACA,OAAO7B,cAAc;IACvB;IACA,MAAM8B,gBAAgB,GAAG9D,eAAe,CAACC,QAAQ,CAAC;;IAElD;IACA,MAAM8D,aAAa,GAAGD,gBAAgB,IAAIA,gBAAgB,CAACE,QAAQ,KAAK,CAAC,GAAGF,gBAAgB,GAAGpF,aAAa,CAAC0E,QAAQ,CAACa,OAAO,CAAC,CAACC,IAAI;IACnI,MAAMC,UAAU,GAAGJ,aAAa,CAACK,qBAAqB,CAAC,CAAC;IACxD,IAAIX,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC,MAAMU,GAAG,GAAGN,aAAa,CAACK,qBAAqB,CAAC,CAAC;MACjD,IAAIX,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,MAAM,IAAIU,GAAG,CAACC,GAAG,KAAK,CAAC,IAAID,GAAG,CAACE,IAAI,KAAK,CAAC,IAAIF,GAAG,CAACG,KAAK,KAAK,CAAC,IAAIH,GAAG,CAACI,MAAM,KAAK,CAAC,EAAE;QAC7Gb,OAAO,CAACc,IAAI,CAAC,CAAC,gEAAgE,EAAE,2DAA2D,EAAE,iFAAiF,CAAC,CAAC3E,IAAI,CAAC,IAAI,CAAC,CAAC;MAC7O;IACF;IACA,OAAO;MACLuE,GAAG,EAAEH,UAAU,CAACG,GAAG,GAAGnF,YAAY,CAACgF,UAAU,EAAEpC,YAAY,CAAC1C,QAAQ,CAAC;MACrEkF,IAAI,EAAEJ,UAAU,CAACI,IAAI,GAAG/E,aAAa,CAAC2E,UAAU,EAAEpC,YAAY,CAACtC,UAAU;IAC3E,CAAC;EACH,CAAC,EAAE,CAACQ,QAAQ,EAAE8B,YAAY,CAACtC,UAAU,EAAEsC,YAAY,CAAC1C,QAAQ,EAAE2C,cAAc,EAAEC,eAAe,CAAC,CAAC;;EAE/F;EACA,MAAM0C,kBAAkB,GAAG/G,KAAK,CAAC4F,WAAW,CAACoB,QAAQ,IAAI;IACvD,OAAO;MACLvF,QAAQ,EAAEF,YAAY,CAACyF,QAAQ,EAAEhF,eAAe,CAACP,QAAQ,CAAC;MAC1DI,UAAU,EAAED,aAAa,CAACoF,QAAQ,EAAEhF,eAAe,CAACH,UAAU;IAChE,CAAC;EACH,CAAC,EAAE,CAACG,eAAe,CAACH,UAAU,EAAEG,eAAe,CAACP,QAAQ,CAAC,CAAC;EAC1D,MAAMwF,mBAAmB,GAAGjH,KAAK,CAAC4F,WAAW,CAACsB,OAAO,IAAI;IACvD,MAAMF,QAAQ,GAAG;MACflF,KAAK,EAAEoF,OAAO,CAACC,WAAW;MAC1BxF,MAAM,EAAEuF,OAAO,CAACE;IAClB,CAAC;;IAED;IACA,MAAMC,mBAAmB,GAAGN,kBAAkB,CAACC,QAAQ,CAAC;IACxD,IAAI3C,eAAe,KAAK,MAAM,EAAE;MAC9B,OAAO;QACLqC,GAAG,EAAE,IAAI;QACTC,IAAI,EAAE,IAAI;QACV3E,eAAe,EAAED,uBAAuB,CAACsF,mBAAmB;MAC9D,CAAC;IACH;;IAEA;IACA,MAAMC,YAAY,GAAG3B,eAAe,CAAC,CAAC;;IAEtC;IACA,IAAIe,GAAG,GAAGY,YAAY,CAACZ,GAAG,GAAGW,mBAAmB,CAAC5F,QAAQ;IACzD,IAAIkF,IAAI,GAAGW,YAAY,CAACX,IAAI,GAAGU,mBAAmB,CAACxF,UAAU;IAC7D,MAAMgF,MAAM,GAAGH,GAAG,GAAGM,QAAQ,CAACrF,MAAM;IACpC,MAAMiF,KAAK,GAAGD,IAAI,GAAGK,QAAQ,CAAClF,KAAK;;IAEnC;IACA,MAAMyF,eAAe,GAAGxG,WAAW,CAACqB,eAAe,CAACC,QAAQ,CAAC,CAAC;;IAE9D;IACA,MAAMmF,eAAe,GAAGD,eAAe,CAACE,WAAW,GAAG9C,eAAe;IACrE,MAAM+C,cAAc,GAAGH,eAAe,CAACI,UAAU,GAAGhD,eAAe;;IAEnE;IACA,IAAIA,eAAe,KAAK,IAAI,IAAI+B,GAAG,GAAG/B,eAAe,EAAE;MACrD,MAAMiD,IAAI,GAAGlB,GAAG,GAAG/B,eAAe;MAClC+B,GAAG,IAAIkB,IAAI;MACXP,mBAAmB,CAAC5F,QAAQ,IAAImG,IAAI;IACtC,CAAC,MAAM,IAAIjD,eAAe,KAAK,IAAI,IAAIkC,MAAM,GAAGW,eAAe,EAAE;MAC/D,MAAMI,IAAI,GAAGf,MAAM,GAAGW,eAAe;MACrCd,GAAG,IAAIkB,IAAI;MACXP,mBAAmB,CAAC5F,QAAQ,IAAImG,IAAI;IACtC;IACA,IAAI/B,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC,IAAIiB,QAAQ,CAACrF,MAAM,GAAG6F,eAAe,IAAIR,QAAQ,CAACrF,MAAM,IAAI6F,eAAe,EAAE;QAC3ExB,OAAO,CAACC,KAAK,CAAC,CAAC,yCAAyC,EAAE,kDAAkDe,QAAQ,CAACrF,MAAM,GAAG6F,eAAe,MAAM,EAAE,uEAAuE,CAAC,CAACrF,IAAI,CAAC,IAAI,CAAC,CAAC;MAC3O;IACF;;IAEA;IACA,IAAIwC,eAAe,KAAK,IAAI,IAAIgC,IAAI,GAAGhC,eAAe,EAAE;MACtD,MAAMiD,IAAI,GAAGjB,IAAI,GAAGhC,eAAe;MACnCgC,IAAI,IAAIiB,IAAI;MACZP,mBAAmB,CAACxF,UAAU,IAAI+F,IAAI;IACxC,CAAC,MAAM,IAAIhB,KAAK,GAAGc,cAAc,EAAE;MACjC,MAAME,IAAI,GAAGhB,KAAK,GAAGc,cAAc;MACnCf,IAAI,IAAIiB,IAAI;MACZP,mBAAmB,CAACxF,UAAU,IAAI+F,IAAI;IACxC;IACA,OAAO;MACLlB,GAAG,EAAE,GAAGmB,IAAI,CAACC,KAAK,CAACpB,GAAG,CAAC,IAAI;MAC3BC,IAAI,EAAE,GAAGkB,IAAI,CAACC,KAAK,CAACnB,IAAI,CAAC,IAAI;MAC7B3E,eAAe,EAAED,uBAAuB,CAACsF,mBAAmB;IAC9D,CAAC;EACH,CAAC,EAAE,CAAChF,QAAQ,EAAEgC,eAAe,EAAEsB,eAAe,EAAEoB,kBAAkB,EAAEpC,eAAe,CAAC,CAAC;EACrF,MAAM,CAACoD,YAAY,EAAEC,eAAe,CAAC,GAAGhI,KAAK,CAACiI,QAAQ,CAACrD,IAAI,CAAC;EAC5D,MAAMsD,oBAAoB,GAAGlI,KAAK,CAAC4F,WAAW,CAAC,MAAM;IACnD,MAAMsB,OAAO,GAAG1B,QAAQ,CAACa,OAAO;IAChC,IAAI,CAACa,OAAO,EAAE;MACZ;IACF;IACA,MAAMiB,WAAW,GAAGlB,mBAAmB,CAACC,OAAO,CAAC;IAChD,IAAIiB,WAAW,CAACzB,GAAG,KAAK,IAAI,EAAE;MAC5BQ,OAAO,CAACkB,KAAK,CAAC1B,GAAG,GAAGyB,WAAW,CAACzB,GAAG;IACrC;IACA,IAAIyB,WAAW,CAACxB,IAAI,KAAK,IAAI,EAAE;MAC7BO,OAAO,CAACkB,KAAK,CAACzB,IAAI,GAAGwB,WAAW,CAACxB,IAAI;IACvC;IACAO,OAAO,CAACkB,KAAK,CAACpG,eAAe,GAAGmG,WAAW,CAACnG,eAAe;IAC3DgG,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC,EAAE,CAACf,mBAAmB,CAAC,CAAC;EACzBjH,KAAK,CAACqI,SAAS,CAAC,MAAM;IACpB,IAAIhD,iBAAiB,EAAE;MACrBiD,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAEL,oBAAoB,CAAC;IACzD;IACA,OAAO,MAAMI,MAAM,CAACE,mBAAmB,CAAC,QAAQ,EAAEN,oBAAoB,CAAC;EACzE,CAAC,EAAE,CAAC7F,QAAQ,EAAEgD,iBAAiB,EAAE6C,oBAAoB,CAAC,CAAC;EACvD,MAAMO,cAAc,GAAGA,CAACvB,OAAO,EAAEwB,WAAW,KAAK;IAC/C,IAAItD,UAAU,EAAE;MACdA,UAAU,CAAC8B,OAAO,EAAEwB,WAAW,CAAC;IAClC;IACAR,oBAAoB,CAAC,CAAC;EACxB,CAAC;EACD,MAAMS,YAAY,GAAGA,CAAA,KAAM;IACzBX,eAAe,CAAC,KAAK,CAAC;EACxB,CAAC;EACDhI,KAAK,CAACqI,SAAS,CAAC,MAAM;IACpB,IAAIzD,IAAI,EAAE;MACRsD,oBAAoB,CAAC,CAAC;IACxB;EACF,CAAC,CAAC;EACFlI,KAAK,CAAC4I,mBAAmB,CAAC1E,MAAM,EAAE,MAAMU,IAAI,GAAG;IAC7CiE,cAAc,EAAEA,CAAA,KAAM;MACpBX,oBAAoB,CAAC,CAAC;IACxB;EACF,CAAC,GAAG,IAAI,EAAE,CAACtD,IAAI,EAAEsD,oBAAoB,CAAC,CAAC;EACvClI,KAAK,CAACqI,SAAS,CAAC,MAAM;IACpB,IAAI,CAACzD,IAAI,EAAE;MACT,OAAOkE,SAAS;IAClB;IACA,MAAMC,YAAY,GAAGlI,QAAQ,CAAC,MAAM;MAClCqH,oBAAoB,CAAC,CAAC;IACxB,CAAC,CAAC;IACF,MAAMX,eAAe,GAAGxG,WAAW,CAACsB,QAAQ,CAAC;IAC7CkF,eAAe,CAACgB,gBAAgB,CAAC,QAAQ,EAAEQ,YAAY,CAAC;IACxD,OAAO,MAAM;MACXA,YAAY,CAACC,KAAK,CAAC,CAAC;MACpBzB,eAAe,CAACiB,mBAAmB,CAAC,QAAQ,EAAEO,YAAY,CAAC;IAC7D,CAAC;EACH,CAAC,EAAE,CAAC1G,QAAQ,EAAEuC,IAAI,EAAEsD,oBAAoB,CAAC,CAAC;EAC1C,IAAIjD,kBAAkB,GAAGC,sBAAsB;EAC/C,IAAIA,sBAAsB,KAAK,MAAM,IAAI,CAACF,mBAAmB,CAACiE,cAAc,EAAE;IAC5EhE,kBAAkB,GAAG6D,SAAS;EAChC;;EAEA;EACA;EACA;EACA,MAAMtE,SAAS,GAAGC,aAAa,KAAKpC,QAAQ,GAAGvB,aAAa,CAACsB,eAAe,CAACC,QAAQ,CAAC,CAAC,CAACiE,IAAI,GAAGwC,SAAS,CAAC;EACzG,MAAMI,QAAQ,GAAG,CAAClF,WAAW,GAAGvB,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACC,IAAI,KAAK,IAAI,GAAGsB,WAAW,GAAGpB,WAAW;EACxG,MAAMuG,SAAS,GAAG,CAAClF,YAAY,GAAGxB,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACE,KAAK,KAAK,IAAI,GAAGsB,YAAY,GAAGf,YAAY;EAC7G,MAAMkG,UAAU,GAAG3I,YAAY,CAAC;IAC9B4I,WAAW,EAAEF,SAAS;IACtBG,iBAAiB,EAAE3J,QAAQ,CAAC,CAAC,CAAC,EAAE4F,sBAAsB,EAAE;MACtD6C,KAAK,EAAEL,YAAY,GAAGxC,sBAAsB,CAAC6C,KAAK,GAAGzI,QAAQ,CAAC,CAAC,CAAC,EAAE4F,sBAAsB,CAAC6C,KAAK,EAAE;QAC9FmB,OAAO,EAAE;MACX,CAAC;IACH,CAAC,CAAC;IACFC,eAAe,EAAE;MACf9E,SAAS;MACTZ,GAAG,EAAE4B;IACP,CAAC;IACDnD,UAAU;IACVgC,SAAS,EAAErE,IAAI,CAACsC,OAAO,CAACG,KAAK,EAAE4C,sBAAsB,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,sBAAsB,CAAChB,SAAS;EAC3G,CAAC,CAAC;EACF,MAAMkF,aAAa,GAAGhJ,YAAY,CAAC;MAC/B4I,WAAW,EAAEH,QAAQ;MACrBI,iBAAiB,EAAE,CAACvE,SAAS,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACrC,IAAI,KAAK,CAAC,CAAC;MACtEgH,sBAAsB,EAAEpE,KAAK;MAC7BkE,eAAe,EAAE;QACf1F,GAAG;QACHiB,SAAS,EAAE;UACT4E,QAAQ,EAAE;YACRC,SAAS,EAAE;UACb;QACF,CAAC;QACDpF,SAAS;QACTI;MACF,CAAC;MACDrC,UAAU;MACVgC,SAAS,EAAErE,IAAI,CAACsC,OAAO,CAACE,IAAI,EAAE6B,SAAS;IACzC,CAAC,CAAC;IACF;MACEQ,SAAS,EAAE8E;IACb,CAAC,GAAGJ,aAAa;IACjBK,SAAS,GAAGlK,6BAA6B,CAAC6J,aAAa,EAAE1J,UAAU,CAAC;EACtE,OAAO,aAAauB,IAAI,CAAC4H,QAAQ,EAAEvJ,QAAQ,CAAC,CAAC,CAAC,EAAEmK,SAAS,EAAE,CAACpJ,eAAe,CAACwI,QAAQ,CAAC,IAAI;IACvFnE,SAAS,EAAE8E,iBAAiB;IAC5BxE;EACF,CAAC,EAAE;IACDf,QAAQ,EAAE,aAAahD,IAAI,CAAC0D,mBAAmB,EAAErF,QAAQ,CAAC;MACxDoK,MAAM,EAAE,IAAI;MACZC,EAAE,EAAEpF,IAAI;MACRQ,UAAU,EAAEqD,cAAc;MAC1BwB,QAAQ,EAAEtB,YAAY;MACtBuB,OAAO,EAAEjF;IACX,CAAC,EAAEE,eAAe,EAAE;MAClBb,QAAQ,EAAE,aAAahD,IAAI,CAAC6H,SAAS,EAAExJ,QAAQ,CAAC,CAAC,CAAC,EAAEyJ,UAAU,EAAE;QAC9D9E,QAAQ,EAAEA;MACZ,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACFuB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGpC,OAAO,CAACwG,SAAS,CAAC,yBAAyB;EACjF;EACA;EACA;EACA;EACA;AACF;AACA;AACA;EACEjG,MAAM,EAAE7D,OAAO;EACf;AACF;AACA;AACA;AACA;EACEgC,QAAQ,EAAE7B,cAAc,CAACP,SAAS,CAACmK,SAAS,CAAC,CAAChK,eAAe,EAAEH,SAAS,CAACoK,IAAI,CAAC,CAAC,EAAErH,KAAK,IAAI;IACxF,IAAIA,KAAK,CAAC4B,IAAI,KAAK,CAAC5B,KAAK,CAACqB,eAAe,IAAIrB,KAAK,CAACqB,eAAe,KAAK,UAAU,CAAC,EAAE;MAClF,MAAM6B,gBAAgB,GAAG9D,eAAe,CAACY,KAAK,CAACX,QAAQ,CAAC;MACxD,IAAI6D,gBAAgB,IAAIA,gBAAgB,CAACE,QAAQ,KAAK,CAAC,EAAE;QACvD,MAAMK,GAAG,GAAGP,gBAAgB,CAACM,qBAAqB,CAAC,CAAC;QACpD,IAAIX,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,MAAM,IAAIU,GAAG,CAACC,GAAG,KAAK,CAAC,IAAID,GAAG,CAACE,IAAI,KAAK,CAAC,IAAIF,GAAG,CAACG,KAAK,KAAK,CAAC,IAAIH,GAAG,CAACI,MAAM,KAAK,CAAC,EAAE;UAC7G,OAAO,IAAIyD,KAAK,CAAC,CAAC,gEAAgE,EAAE,2DAA2D,EAAE,iFAAiF,CAAC,CAACnI,IAAI,CAAC,IAAI,CAAC,CAAC;QACjP;MACF,CAAC,MAAM;QACL,OAAO,IAAImI,KAAK,CAAC,CAAC,gEAAgE,EAAE,wEAAwEpE,gBAAgB,aAAa,CAAC,CAAC/D,IAAI,CAAC,IAAI,CAAC,CAAC;MACxM;IACF;IACA,OAAO,IAAI;EACb,CAAC,CAAC;EACF;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEgC,YAAY,EAAElE,SAAS,CAACsK,KAAK,CAAC;IAC5B1I,UAAU,EAAE5B,SAAS,CAACmK,SAAS,CAAC,CAACnK,SAAS,CAACuK,KAAK,CAAC,CAAC,QAAQ,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC,EAAEvK,SAAS,CAACwK,MAAM,CAAC,CAAC,CAACC,UAAU;IAC5GjJ,QAAQ,EAAExB,SAAS,CAACmK,SAAS,CAAC,CAACnK,SAAS,CAACuK,KAAK,CAAC,CAAC,QAAQ,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC,EAAEvK,SAAS,CAACwK,MAAM,CAAC,CAAC,CAACC;EAClG,CAAC,CAAC;EACF;AACF;AACA;AACA;EACEtG,cAAc,EAAEnE,SAAS,CAACsK,KAAK,CAAC;IAC9B5D,IAAI,EAAE1G,SAAS,CAACwK,MAAM,CAACC,UAAU;IACjChE,GAAG,EAAEzG,SAAS,CAACwK,MAAM,CAACC;EACxB,CAAC,CAAC;EACF;AACF;AACA;AACA;AACA;EACErG,eAAe,EAAEpE,SAAS,CAACuK,KAAK,CAAC,CAAC,UAAU,EAAE,gBAAgB,EAAE,MAAM,CAAC,CAAC;EACxE;AACF;AACA;EACElG,QAAQ,EAAErE,SAAS,CAAC0K,IAAI;EACxB;AACF;AACA;EACEnI,OAAO,EAAEvC,SAAS,CAAC2K,MAAM;EACzB;AACF;AACA;EACErG,SAAS,EAAEtE,SAAS,CAAC4K,MAAM;EAC3B;AACF;AACA;AACA;AACA;AACA;AACA;EACErG,SAAS,EAAEvE,SAAS,CAAC,sCAAsCmK,SAAS,CAAC,CAAChK,eAAe,EAAEH,SAAS,CAACoK,IAAI,CAAC,CAAC;EACvG;AACF;AACA;AACA;EACEhF,iBAAiB,EAAEpF,SAAS,CAAC6K,IAAI;EACjC;AACF;AACA;AACA;EACEpG,SAAS,EAAEnE,eAAe;EAC1B;AACF;AACA;AACA;AACA;EACEoE,eAAe,EAAE1E,SAAS,CAACwK,MAAM;EACjC;AACF;AACA;AACA;EACEM,OAAO,EAAE9K,SAAS,CAACoK,IAAI;EACvB;AACF;AACA;EACEzF,IAAI,EAAE3E,SAAS,CAAC6K,IAAI,CAACJ,UAAU;EAC/B;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE7F,UAAU,EAAE5E,SAAS,CAAC,sCAAsCsK,KAAK,CAAC;IAChES,SAAS,EAAE1K;EACb,CAAC,CAAC;EACF;AACF;AACA;AACA;AACA;AACA;EACEyE,SAAS,EAAE9E,SAAS,CAACsK,KAAK,CAAC;IACzB5H,KAAK,EAAE1C,SAAS,CAACmK,SAAS,CAAC,CAACnK,SAAS,CAACoK,IAAI,EAAEpK,SAAS,CAAC2K,MAAM,CAAC,CAAC;IAC9DlI,IAAI,EAAEzC,SAAS,CAACmK,SAAS,CAAC,CAACnK,SAAS,CAACoK,IAAI,EAAEpK,SAAS,CAAC2K,MAAM,CAAC;EAC9D,CAAC,CAAC;EACF;AACF;AACA;AACA;AACA;EACEnI,KAAK,EAAExC,SAAS,CAACsK,KAAK,CAAC;IACrB5H,KAAK,EAAE1C,SAAS,CAACoJ,WAAW;IAC5B3G,IAAI,EAAEzC,SAAS,CAACoJ;EAClB,CAAC,CAAC;EACF;AACF;AACA;EACE4B,EAAE,EAAEhL,SAAS,CAACmK,SAAS,CAAC,CAACnK,SAAS,CAACiL,OAAO,CAACjL,SAAS,CAACmK,SAAS,CAAC,CAACnK,SAAS,CAACoK,IAAI,EAAEpK,SAAS,CAAC2K,MAAM,EAAE3K,SAAS,CAAC6K,IAAI,CAAC,CAAC,CAAC,EAAE7K,SAAS,CAACoK,IAAI,EAAEpK,SAAS,CAAC2K,MAAM,CAAC,CAAC;EACvJ;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE5I,eAAe,EAAE/B,SAAS,CAACsK,KAAK,CAAC;IAC/B1I,UAAU,EAAE5B,SAAS,CAACmK,SAAS,CAAC,CAACnK,SAAS,CAACuK,KAAK,CAAC,CAAC,QAAQ,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC,EAAEvK,SAAS,CAACwK,MAAM,CAAC,CAAC,CAACC,UAAU;IAC5GjJ,QAAQ,EAAExB,SAAS,CAACmK,SAAS,CAAC,CAACnK,SAAS,CAACuK,KAAK,CAAC,CAAC,QAAQ,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC,EAAEvK,SAAS,CAACwK,MAAM,CAAC,CAAC,CAACC;EAClG,CAAC,CAAC;EACF;AACF;AACA;AACA;AACA;EACE1F,mBAAmB,EAAE/E,SAAS,CAACoJ,WAAW;EAC1C;AACF;AACA;AACA;EACEpE,kBAAkB,EAAEhF,SAAS,CAACmK,SAAS,CAAC,CAACnK,SAAS,CAACuK,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC,EAAEvK,SAAS,CAACwK,MAAM,EAAExK,SAAS,CAACsK,KAAK,CAAC;IACpGR,MAAM,EAAE9J,SAAS,CAACwK,MAAM;IACxBU,KAAK,EAAElL,SAAS,CAACwK,MAAM;IACvBW,IAAI,EAAEnL,SAAS,CAACwK;EAClB,CAAC,CAAC,CAAC,CAAC;EACJ;AACF;AACA;AACA;AACA;EACEtF,eAAe,EAAElF,SAAS,CAAC2K;AAC7B,CAAC,GAAG,KAAK,CAAC;AACV,eAAejH,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}