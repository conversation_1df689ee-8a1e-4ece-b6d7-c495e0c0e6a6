{"ast": null, "code": "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"ref\"],\n  _excluded2 = [\"ariaLabel\", \"FabProps\", \"children\", \"className\", \"direction\", \"hidden\", \"icon\", \"onBlur\", \"onClose\", \"onFocus\", \"onKeyDown\", \"onMouseEnter\", \"onMouseLeave\", \"onOpen\", \"open\", \"openIcon\", \"TransitionComponent\", \"transitionDuration\", \"TransitionProps\"],\n  _excluded3 = [\"ref\"];\nimport * as React from 'react';\nimport { isFragment } from 'react-is';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport useTimeout from '@mui/utils/useTimeout';\nimport clamp from '@mui/utils/clamp';\nimport styled from '../styles/styled';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport useTheme from '../styles/useTheme';\nimport Zoom from '../Zoom';\nimport Fab from '../Fab';\nimport capitalize from '../utils/capitalize';\nimport isMuiElement from '../utils/isMuiElement';\nimport useForkRef from '../utils/useForkRef';\nimport useControlled from '../utils/useControlled';\nimport speedDialClasses, { getSpeedDialUtilityClass } from './speedDialClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    open,\n    direction\n  } = ownerState;\n  const slots = {\n    root: ['root', `direction${capitalize(direction)}`],\n    fab: ['fab'],\n    actions: ['actions', !open && 'actionsClosed']\n  };\n  return composeClasses(slots, getSpeedDialUtilityClass, classes);\n};\nfunction getOrientation(direction) {\n  if (direction === 'up' || direction === 'down') {\n    return 'vertical';\n  }\n  if (direction === 'right' || direction === 'left') {\n    return 'horizontal';\n  }\n  return undefined;\n}\nconst dialRadius = 32;\nconst spacingActions = 16;\nconst SpeedDialRoot = styled('div', {\n  name: 'MuiSpeedDial',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, styles[`direction${capitalize(ownerState.direction)}`]];\n  }\n})(_ref => {\n  let {\n    theme,\n    ownerState\n  } = _ref;\n  return _extends({\n    zIndex: (theme.vars || theme).zIndex.speedDial,\n    display: 'flex',\n    alignItems: 'center',\n    pointerEvents: 'none'\n  }, ownerState.direction === 'up' && {\n    flexDirection: 'column-reverse',\n    [`& .${speedDialClasses.actions}`]: {\n      flexDirection: 'column-reverse',\n      marginBottom: -dialRadius,\n      paddingBottom: spacingActions + dialRadius\n    }\n  }, ownerState.direction === 'down' && {\n    flexDirection: 'column',\n    [`& .${speedDialClasses.actions}`]: {\n      flexDirection: 'column',\n      marginTop: -dialRadius,\n      paddingTop: spacingActions + dialRadius\n    }\n  }, ownerState.direction === 'left' && {\n    flexDirection: 'row-reverse',\n    [`& .${speedDialClasses.actions}`]: {\n      flexDirection: 'row-reverse',\n      marginRight: -dialRadius,\n      paddingRight: spacingActions + dialRadius\n    }\n  }, ownerState.direction === 'right' && {\n    flexDirection: 'row',\n    [`& .${speedDialClasses.actions}`]: {\n      flexDirection: 'row',\n      marginLeft: -dialRadius,\n      paddingLeft: spacingActions + dialRadius\n    }\n  });\n});\nconst SpeedDialFab = styled(Fab, {\n  name: 'MuiSpeedDial',\n  slot: 'Fab',\n  overridesResolver: (props, styles) => styles.fab\n})(() => ({\n  pointerEvents: 'auto'\n}));\nconst SpeedDialActions = styled('div', {\n  name: 'MuiSpeedDial',\n  slot: 'Actions',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.actions, !ownerState.open && styles.actionsClosed];\n  }\n})(_ref2 => {\n  let {\n    ownerState\n  } = _ref2;\n  return _extends({\n    display: 'flex',\n    pointerEvents: 'auto'\n  }, !ownerState.open && {\n    transition: 'top 0s linear 0.2s',\n    pointerEvents: 'none'\n  });\n});\nconst SpeedDial = /*#__PURE__*/React.forwardRef(function SpeedDial(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiSpeedDial'\n  });\n  const theme = useTheme();\n  const defaultTransitionDuration = {\n    enter: theme.transitions.duration.enteringScreen,\n    exit: theme.transitions.duration.leavingScreen\n  };\n  const {\n      ariaLabel,\n      FabProps: {\n        ref: origDialButtonRef\n      } = {},\n      children: childrenProp,\n      className,\n      direction = 'up',\n      hidden = false,\n      icon,\n      onBlur,\n      onClose,\n      onFocus,\n      onKeyDown,\n      onMouseEnter,\n      onMouseLeave,\n      onOpen,\n      open: openProp,\n      TransitionComponent = Zoom,\n      transitionDuration = defaultTransitionDuration,\n      TransitionProps\n    } = props,\n    FabProps = _objectWithoutPropertiesLoose(props.FabProps, _excluded),\n    other = _objectWithoutPropertiesLoose(props, _excluded2);\n  const [open, setOpenState] = useControlled({\n    controlled: openProp,\n    default: false,\n    name: 'SpeedDial',\n    state: 'open'\n  });\n  const ownerState = _extends({}, props, {\n    open,\n    direction\n  });\n  const classes = useUtilityClasses(ownerState);\n  const eventTimer = useTimeout();\n\n  /**\n   * an index in actions.current\n   */\n  const focusedAction = React.useRef(0);\n\n  /**\n   * pressing this key while the focus is on a child SpeedDialAction focuses\n   * the next SpeedDialAction.\n   * It is equal to the first arrow key pressed while focus is on the SpeedDial\n   * that is not orthogonal to the direction.\n   * @type {utils.ArrowKey?}\n   */\n  const nextItemArrowKey = React.useRef();\n\n  /**\n   * refs to the Button that have an action associated to them in this SpeedDial\n   * [Fab, ...(SpeedDialActions > Button)]\n   * @type {HTMLButtonElement[]}\n   */\n  const actions = React.useRef([]);\n  actions.current = [actions.current[0]];\n  const handleOwnFabRef = React.useCallback(fabFef => {\n    actions.current[0] = fabFef;\n  }, []);\n  const handleFabRef = useForkRef(origDialButtonRef, handleOwnFabRef);\n\n  /**\n   * creates a ref callback for the Button in a SpeedDialAction\n   * Is called before the original ref callback for Button that was set in buttonProps\n   *\n   * @param dialActionIndex {number}\n   * @param origButtonRef {React.RefObject?}\n   */\n  const createHandleSpeedDialActionButtonRef = (dialActionIndex, origButtonRef) => {\n    return buttonRef => {\n      actions.current[dialActionIndex + 1] = buttonRef;\n      if (origButtonRef) {\n        origButtonRef(buttonRef);\n      }\n    };\n  };\n  const handleKeyDown = event => {\n    if (onKeyDown) {\n      onKeyDown(event);\n    }\n    const key = event.key.replace('Arrow', '').toLowerCase();\n    const {\n      current: nextItemArrowKeyCurrent = key\n    } = nextItemArrowKey;\n    if (event.key === 'Escape') {\n      setOpenState(false);\n      actions.current[0].focus();\n      if (onClose) {\n        onClose(event, 'escapeKeyDown');\n      }\n      return;\n    }\n    if (getOrientation(key) === getOrientation(nextItemArrowKeyCurrent) && getOrientation(key) !== undefined) {\n      event.preventDefault();\n      const actionStep = key === nextItemArrowKeyCurrent ? 1 : -1;\n\n      // stay within array indices\n      const nextAction = clamp(focusedAction.current + actionStep, 0, actions.current.length - 1);\n      actions.current[nextAction].focus();\n      focusedAction.current = nextAction;\n      nextItemArrowKey.current = nextItemArrowKeyCurrent;\n    }\n  };\n  React.useEffect(() => {\n    // actions were closed while navigation state was not reset\n    if (!open) {\n      focusedAction.current = 0;\n      nextItemArrowKey.current = undefined;\n    }\n  }, [open]);\n  const handleClose = event => {\n    if (event.type === 'mouseleave' && onMouseLeave) {\n      onMouseLeave(event);\n    }\n    if (event.type === 'blur' && onBlur) {\n      onBlur(event);\n    }\n    eventTimer.clear();\n    if (event.type === 'blur') {\n      eventTimer.start(0, () => {\n        setOpenState(false);\n        if (onClose) {\n          onClose(event, 'blur');\n        }\n      });\n    } else {\n      setOpenState(false);\n      if (onClose) {\n        onClose(event, 'mouseLeave');\n      }\n    }\n  };\n  const handleClick = event => {\n    if (FabProps.onClick) {\n      FabProps.onClick(event);\n    }\n    eventTimer.clear();\n    if (open) {\n      setOpenState(false);\n      if (onClose) {\n        onClose(event, 'toggle');\n      }\n    } else {\n      setOpenState(true);\n      if (onOpen) {\n        onOpen(event, 'toggle');\n      }\n    }\n  };\n  const handleOpen = event => {\n    if (event.type === 'mouseenter' && onMouseEnter) {\n      onMouseEnter(event);\n    }\n    if (event.type === 'focus' && onFocus) {\n      onFocus(event);\n    }\n\n    // When moving the focus between two items,\n    // a chain if blur and focus event is triggered.\n    // We only handle the last event.\n    eventTimer.clear();\n    if (!open) {\n      // Wait for a future focus or click event\n      eventTimer.start(0, () => {\n        setOpenState(true);\n        if (onOpen) {\n          const eventMap = {\n            focus: 'focus',\n            mouseenter: 'mouseEnter'\n          };\n          onOpen(event, eventMap[event.type]);\n        }\n      });\n    }\n  };\n\n  // Filter the label for valid id characters.\n  const id = ariaLabel.replace(/^[^a-z]+|[^\\w:.-]+/gi, '');\n  const allItems = React.Children.toArray(childrenProp).filter(child => {\n    if (process.env.NODE_ENV !== 'production') {\n      if (isFragment(child)) {\n        console.error([\"MUI: The SpeedDial component doesn't accept a Fragment as a child.\", 'Consider providing an array instead.'].join('\\n'));\n      }\n    }\n    return /*#__PURE__*/React.isValidElement(child);\n  });\n  const children = allItems.map((child, index) => {\n    const _child$props = child.props,\n      {\n        FabProps: {\n          ref: origButtonRef\n        } = {},\n        tooltipPlacement: tooltipPlacementProp\n      } = _child$props,\n      ChildFabProps = _objectWithoutPropertiesLoose(_child$props.FabProps, _excluded3);\n    const tooltipPlacement = tooltipPlacementProp || (getOrientation(direction) === 'vertical' ? 'left' : 'top');\n    return /*#__PURE__*/React.cloneElement(child, {\n      FabProps: _extends({}, ChildFabProps, {\n        ref: createHandleSpeedDialActionButtonRef(index, origButtonRef)\n      }),\n      delay: 30 * (open ? index : allItems.length - index),\n      open,\n      tooltipPlacement,\n      id: `${id}-action-${index}`\n    });\n  });\n  return /*#__PURE__*/_jsxs(SpeedDialRoot, _extends({\n    className: clsx(classes.root, className),\n    ref: ref,\n    role: \"presentation\",\n    onKeyDown: handleKeyDown,\n    onBlur: handleClose,\n    onFocus: handleOpen,\n    onMouseEnter: handleOpen,\n    onMouseLeave: handleClose,\n    ownerState: ownerState\n  }, other, {\n    children: [/*#__PURE__*/_jsx(TransitionComponent, _extends({\n      in: !hidden,\n      timeout: transitionDuration,\n      unmountOnExit: true\n    }, TransitionProps, {\n      children: /*#__PURE__*/_jsx(SpeedDialFab, _extends({\n        color: \"primary\",\n        \"aria-label\": ariaLabel,\n        \"aria-haspopup\": \"true\",\n        \"aria-expanded\": open,\n        \"aria-controls\": `${id}-actions`\n      }, FabProps, {\n        onClick: handleClick,\n        className: clsx(classes.fab, FabProps.className),\n        ref: handleFabRef,\n        ownerState: ownerState,\n        children: /*#__PURE__*/React.isValidElement(icon) && isMuiElement(icon, ['SpeedDialIcon']) ? /*#__PURE__*/React.cloneElement(icon, {\n          open\n        }) : icon\n      }))\n    })), /*#__PURE__*/_jsx(SpeedDialActions, {\n      id: `${id}-actions`,\n      role: \"menu\",\n      \"aria-orientation\": getOrientation(direction),\n      className: clsx(classes.actions, !open && classes.actionsClosed),\n      ownerState: ownerState,\n      children: children\n    })]\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? SpeedDial.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The aria-label of the button element.\n   * Also used to provide the `id` for the `SpeedDial` element and its children.\n   */\n  ariaLabel: PropTypes.string.isRequired,\n  /**\n   * SpeedDialActions to display when the SpeedDial is `open`.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The direction the actions open relative to the floating action button.\n   * @default 'up'\n   */\n  direction: PropTypes.oneOf(['down', 'left', 'right', 'up']),\n  /**\n   * Props applied to the [`Fab`](/material-ui/api/fab/) element.\n   * @default {}\n   */\n  FabProps: PropTypes.object,\n  /**\n   * If `true`, the SpeedDial is hidden.\n   * @default false\n   */\n  hidden: PropTypes.bool,\n  /**\n   * The icon to display in the SpeedDial Fab. The `SpeedDialIcon` component\n   * provides a default Icon with animation.\n   */\n  icon: PropTypes.node,\n  /**\n   * @ignore\n   */\n  onBlur: PropTypes.func,\n  /**\n   * Callback fired when the component requests to be closed.\n   *\n   * @param {object} event The event source of the callback.\n   * @param {string} reason Can be: `\"toggle\"`, `\"blur\"`, `\"mouseLeave\"`, `\"escapeKeyDown\"`.\n   */\n  onClose: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onFocus: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onKeyDown: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onMouseEnter: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onMouseLeave: PropTypes.func,\n  /**\n   * Callback fired when the component requests to be open.\n   *\n   * @param {object} event The event source of the callback.\n   * @param {string} reason Can be: `\"toggle\"`, `\"focus\"`, `\"mouseEnter\"`.\n   */\n  onOpen: PropTypes.func,\n  /**\n   * If `true`, the component is shown.\n   */\n  open: PropTypes.bool,\n  /**\n   * The icon to display in the SpeedDial Fab when the SpeedDial is open.\n   */\n  openIcon: PropTypes.node,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The component used for the transition.\n   * [Follow this guide](/material-ui/transitions/#transitioncomponent-prop) to learn more about the requirements for this component.\n   * @default Zoom\n   */\n  TransitionComponent: PropTypes.elementType,\n  /**\n   * The duration for the transition, in milliseconds.\n   * You may specify a single timeout for all transitions, or individually with an object.\n   * @default {\n   *   enter: theme.transitions.duration.enteringScreen,\n   *   exit: theme.transitions.duration.leavingScreen,\n   * }\n   */\n  transitionDuration: PropTypes.oneOfType([PropTypes.number, PropTypes.shape({\n    appear: PropTypes.number,\n    enter: PropTypes.number,\n    exit: PropTypes.number\n  })]),\n  /**\n   * Props applied to the transition element.\n   * By default, the element is based on this [`Transition`](https://reactcommunity.org/react-transition-group/transition/) component.\n   */\n  TransitionProps: PropTypes.object\n} : void 0;\nexport default SpeedDial;", "map": {"version": 3, "names": ["_objectWithoutPropertiesLoose", "_extends", "_excluded", "_excluded2", "_excluded3", "React", "isFragment", "PropTypes", "clsx", "composeClasses", "useTimeout", "clamp", "styled", "useDefaultProps", "useTheme", "Zoom", "Fab", "capitalize", "isMuiElement", "useForkRef", "useControlled", "speedDialClasses", "getSpeedDialUtilityClass", "jsx", "_jsx", "jsxs", "_jsxs", "useUtilityClasses", "ownerState", "classes", "open", "direction", "slots", "root", "fab", "actions", "getOrientation", "undefined", "dialRadius", "spacingActions", "SpeedDialRoot", "name", "slot", "overridesResolver", "props", "styles", "_ref", "theme", "zIndex", "vars", "speedDial", "display", "alignItems", "pointerEvents", "flexDirection", "marginBottom", "paddingBottom", "marginTop", "paddingTop", "marginRight", "paddingRight", "marginLeft", "paddingLeft", "SpeedDialFab", "SpeedDialActions", "actionsClosed", "_ref2", "transition", "SpeedDial", "forwardRef", "inProps", "ref", "defaultTransitionDuration", "enter", "transitions", "duration", "enteringScreen", "exit", "leavingScreen", "aria<PERSON><PERSON><PERSON>", "FabProps", "origDialButtonRef", "children", "childrenProp", "className", "hidden", "icon", "onBlur", "onClose", "onFocus", "onKeyDown", "onMouseEnter", "onMouseLeave", "onOpen", "openProp", "TransitionComponent", "transitionDuration", "TransitionProps", "other", "setOpenState", "controlled", "default", "state", "eventTimer", "focusedAction", "useRef", "nextItemArrowKey", "current", "handleOwnFabRef", "useCallback", "fabFef", "handleFabRef", "createHandleSpeedDialActionButtonRef", "dialActionIndex", "origButtonRef", "buttonRef", "handleKeyDown", "event", "key", "replace", "toLowerCase", "nextItemArrowKeyCurrent", "focus", "preventDefault", "actionStep", "nextAction", "length", "useEffect", "handleClose", "type", "clear", "start", "handleClick", "onClick", "handleOpen", "eventMap", "mouseenter", "id", "allItems", "Children", "toArray", "filter", "child", "process", "env", "NODE_ENV", "console", "error", "join", "isValidElement", "map", "index", "_child$props", "tooltipPlacement", "tooltipPlacementProp", "ChildFabProps", "cloneElement", "delay", "role", "in", "timeout", "unmountOnExit", "color", "propTypes", "string", "isRequired", "node", "object", "oneOf", "bool", "func", "openIcon", "sx", "oneOfType", "arrayOf", "elementType", "number", "shape", "appear"], "sources": ["/home/<USER>/PycharmProjects/ai-voice-mate-dev/ai_voice_mate/frontend/node_modules/@mui/material/SpeedDial/SpeedDial.js"], "sourcesContent": ["'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"ref\"],\n  _excluded2 = [\"ariaLabel\", \"FabProps\", \"children\", \"className\", \"direction\", \"hidden\", \"icon\", \"onBlur\", \"onClose\", \"onFocus\", \"onKeyDown\", \"onMouseEnter\", \"onMouseLeave\", \"onOpen\", \"open\", \"openIcon\", \"TransitionComponent\", \"transitionDuration\", \"TransitionProps\"],\n  _excluded3 = [\"ref\"];\nimport * as React from 'react';\nimport { isFragment } from 'react-is';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport useTimeout from '@mui/utils/useTimeout';\nimport clamp from '@mui/utils/clamp';\nimport styled from '../styles/styled';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport useTheme from '../styles/useTheme';\nimport Zoom from '../Zoom';\nimport Fab from '../Fab';\nimport capitalize from '../utils/capitalize';\nimport isMuiElement from '../utils/isMuiElement';\nimport useForkRef from '../utils/useForkRef';\nimport useControlled from '../utils/useControlled';\nimport speedDialClasses, { getSpeedDialUtilityClass } from './speedDialClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    open,\n    direction\n  } = ownerState;\n  const slots = {\n    root: ['root', `direction${capitalize(direction)}`],\n    fab: ['fab'],\n    actions: ['actions', !open && 'actionsClosed']\n  };\n  return composeClasses(slots, getSpeedDialUtilityClass, classes);\n};\nfunction getOrientation(direction) {\n  if (direction === 'up' || direction === 'down') {\n    return 'vertical';\n  }\n  if (direction === 'right' || direction === 'left') {\n    return 'horizontal';\n  }\n  return undefined;\n}\nconst dialRadius = 32;\nconst spacingActions = 16;\nconst SpeedDialRoot = styled('div', {\n  name: 'MuiSpeedDial',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, styles[`direction${capitalize(ownerState.direction)}`]];\n  }\n})(({\n  theme,\n  ownerState\n}) => _extends({\n  zIndex: (theme.vars || theme).zIndex.speedDial,\n  display: 'flex',\n  alignItems: 'center',\n  pointerEvents: 'none'\n}, ownerState.direction === 'up' && {\n  flexDirection: 'column-reverse',\n  [`& .${speedDialClasses.actions}`]: {\n    flexDirection: 'column-reverse',\n    marginBottom: -dialRadius,\n    paddingBottom: spacingActions + dialRadius\n  }\n}, ownerState.direction === 'down' && {\n  flexDirection: 'column',\n  [`& .${speedDialClasses.actions}`]: {\n    flexDirection: 'column',\n    marginTop: -dialRadius,\n    paddingTop: spacingActions + dialRadius\n  }\n}, ownerState.direction === 'left' && {\n  flexDirection: 'row-reverse',\n  [`& .${speedDialClasses.actions}`]: {\n    flexDirection: 'row-reverse',\n    marginRight: -dialRadius,\n    paddingRight: spacingActions + dialRadius\n  }\n}, ownerState.direction === 'right' && {\n  flexDirection: 'row',\n  [`& .${speedDialClasses.actions}`]: {\n    flexDirection: 'row',\n    marginLeft: -dialRadius,\n    paddingLeft: spacingActions + dialRadius\n  }\n}));\nconst SpeedDialFab = styled(Fab, {\n  name: 'MuiSpeedDial',\n  slot: 'Fab',\n  overridesResolver: (props, styles) => styles.fab\n})(() => ({\n  pointerEvents: 'auto'\n}));\nconst SpeedDialActions = styled('div', {\n  name: 'MuiSpeedDial',\n  slot: 'Actions',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.actions, !ownerState.open && styles.actionsClosed];\n  }\n})(({\n  ownerState\n}) => _extends({\n  display: 'flex',\n  pointerEvents: 'auto'\n}, !ownerState.open && {\n  transition: 'top 0s linear 0.2s',\n  pointerEvents: 'none'\n}));\nconst SpeedDial = /*#__PURE__*/React.forwardRef(function SpeedDial(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiSpeedDial'\n  });\n  const theme = useTheme();\n  const defaultTransitionDuration = {\n    enter: theme.transitions.duration.enteringScreen,\n    exit: theme.transitions.duration.leavingScreen\n  };\n  const {\n      ariaLabel,\n      FabProps: {\n        ref: origDialButtonRef\n      } = {},\n      children: childrenProp,\n      className,\n      direction = 'up',\n      hidden = false,\n      icon,\n      onBlur,\n      onClose,\n      onFocus,\n      onKeyDown,\n      onMouseEnter,\n      onMouseLeave,\n      onOpen,\n      open: openProp,\n      TransitionComponent = Zoom,\n      transitionDuration = defaultTransitionDuration,\n      TransitionProps\n    } = props,\n    FabProps = _objectWithoutPropertiesLoose(props.FabProps, _excluded),\n    other = _objectWithoutPropertiesLoose(props, _excluded2);\n  const [open, setOpenState] = useControlled({\n    controlled: openProp,\n    default: false,\n    name: 'SpeedDial',\n    state: 'open'\n  });\n  const ownerState = _extends({}, props, {\n    open,\n    direction\n  });\n  const classes = useUtilityClasses(ownerState);\n  const eventTimer = useTimeout();\n\n  /**\n   * an index in actions.current\n   */\n  const focusedAction = React.useRef(0);\n\n  /**\n   * pressing this key while the focus is on a child SpeedDialAction focuses\n   * the next SpeedDialAction.\n   * It is equal to the first arrow key pressed while focus is on the SpeedDial\n   * that is not orthogonal to the direction.\n   * @type {utils.ArrowKey?}\n   */\n  const nextItemArrowKey = React.useRef();\n\n  /**\n   * refs to the Button that have an action associated to them in this SpeedDial\n   * [Fab, ...(SpeedDialActions > Button)]\n   * @type {HTMLButtonElement[]}\n   */\n  const actions = React.useRef([]);\n  actions.current = [actions.current[0]];\n  const handleOwnFabRef = React.useCallback(fabFef => {\n    actions.current[0] = fabFef;\n  }, []);\n  const handleFabRef = useForkRef(origDialButtonRef, handleOwnFabRef);\n\n  /**\n   * creates a ref callback for the Button in a SpeedDialAction\n   * Is called before the original ref callback for Button that was set in buttonProps\n   *\n   * @param dialActionIndex {number}\n   * @param origButtonRef {React.RefObject?}\n   */\n  const createHandleSpeedDialActionButtonRef = (dialActionIndex, origButtonRef) => {\n    return buttonRef => {\n      actions.current[dialActionIndex + 1] = buttonRef;\n      if (origButtonRef) {\n        origButtonRef(buttonRef);\n      }\n    };\n  };\n  const handleKeyDown = event => {\n    if (onKeyDown) {\n      onKeyDown(event);\n    }\n    const key = event.key.replace('Arrow', '').toLowerCase();\n    const {\n      current: nextItemArrowKeyCurrent = key\n    } = nextItemArrowKey;\n    if (event.key === 'Escape') {\n      setOpenState(false);\n      actions.current[0].focus();\n      if (onClose) {\n        onClose(event, 'escapeKeyDown');\n      }\n      return;\n    }\n    if (getOrientation(key) === getOrientation(nextItemArrowKeyCurrent) && getOrientation(key) !== undefined) {\n      event.preventDefault();\n      const actionStep = key === nextItemArrowKeyCurrent ? 1 : -1;\n\n      // stay within array indices\n      const nextAction = clamp(focusedAction.current + actionStep, 0, actions.current.length - 1);\n      actions.current[nextAction].focus();\n      focusedAction.current = nextAction;\n      nextItemArrowKey.current = nextItemArrowKeyCurrent;\n    }\n  };\n  React.useEffect(() => {\n    // actions were closed while navigation state was not reset\n    if (!open) {\n      focusedAction.current = 0;\n      nextItemArrowKey.current = undefined;\n    }\n  }, [open]);\n  const handleClose = event => {\n    if (event.type === 'mouseleave' && onMouseLeave) {\n      onMouseLeave(event);\n    }\n    if (event.type === 'blur' && onBlur) {\n      onBlur(event);\n    }\n    eventTimer.clear();\n    if (event.type === 'blur') {\n      eventTimer.start(0, () => {\n        setOpenState(false);\n        if (onClose) {\n          onClose(event, 'blur');\n        }\n      });\n    } else {\n      setOpenState(false);\n      if (onClose) {\n        onClose(event, 'mouseLeave');\n      }\n    }\n  };\n  const handleClick = event => {\n    if (FabProps.onClick) {\n      FabProps.onClick(event);\n    }\n    eventTimer.clear();\n    if (open) {\n      setOpenState(false);\n      if (onClose) {\n        onClose(event, 'toggle');\n      }\n    } else {\n      setOpenState(true);\n      if (onOpen) {\n        onOpen(event, 'toggle');\n      }\n    }\n  };\n  const handleOpen = event => {\n    if (event.type === 'mouseenter' && onMouseEnter) {\n      onMouseEnter(event);\n    }\n    if (event.type === 'focus' && onFocus) {\n      onFocus(event);\n    }\n\n    // When moving the focus between two items,\n    // a chain if blur and focus event is triggered.\n    // We only handle the last event.\n    eventTimer.clear();\n    if (!open) {\n      // Wait for a future focus or click event\n      eventTimer.start(0, () => {\n        setOpenState(true);\n        if (onOpen) {\n          const eventMap = {\n            focus: 'focus',\n            mouseenter: 'mouseEnter'\n          };\n          onOpen(event, eventMap[event.type]);\n        }\n      });\n    }\n  };\n\n  // Filter the label for valid id characters.\n  const id = ariaLabel.replace(/^[^a-z]+|[^\\w:.-]+/gi, '');\n  const allItems = React.Children.toArray(childrenProp).filter(child => {\n    if (process.env.NODE_ENV !== 'production') {\n      if (isFragment(child)) {\n        console.error([\"MUI: The SpeedDial component doesn't accept a Fragment as a child.\", 'Consider providing an array instead.'].join('\\n'));\n      }\n    }\n    return /*#__PURE__*/React.isValidElement(child);\n  });\n  const children = allItems.map((child, index) => {\n    const _child$props = child.props,\n      {\n        FabProps: {\n          ref: origButtonRef\n        } = {},\n        tooltipPlacement: tooltipPlacementProp\n      } = _child$props,\n      ChildFabProps = _objectWithoutPropertiesLoose(_child$props.FabProps, _excluded3);\n    const tooltipPlacement = tooltipPlacementProp || (getOrientation(direction) === 'vertical' ? 'left' : 'top');\n    return /*#__PURE__*/React.cloneElement(child, {\n      FabProps: _extends({}, ChildFabProps, {\n        ref: createHandleSpeedDialActionButtonRef(index, origButtonRef)\n      }),\n      delay: 30 * (open ? index : allItems.length - index),\n      open,\n      tooltipPlacement,\n      id: `${id}-action-${index}`\n    });\n  });\n  return /*#__PURE__*/_jsxs(SpeedDialRoot, _extends({\n    className: clsx(classes.root, className),\n    ref: ref,\n    role: \"presentation\",\n    onKeyDown: handleKeyDown,\n    onBlur: handleClose,\n    onFocus: handleOpen,\n    onMouseEnter: handleOpen,\n    onMouseLeave: handleClose,\n    ownerState: ownerState\n  }, other, {\n    children: [/*#__PURE__*/_jsx(TransitionComponent, _extends({\n      in: !hidden,\n      timeout: transitionDuration,\n      unmountOnExit: true\n    }, TransitionProps, {\n      children: /*#__PURE__*/_jsx(SpeedDialFab, _extends({\n        color: \"primary\",\n        \"aria-label\": ariaLabel,\n        \"aria-haspopup\": \"true\",\n        \"aria-expanded\": open,\n        \"aria-controls\": `${id}-actions`\n      }, FabProps, {\n        onClick: handleClick,\n        className: clsx(classes.fab, FabProps.className),\n        ref: handleFabRef,\n        ownerState: ownerState,\n        children: /*#__PURE__*/React.isValidElement(icon) && isMuiElement(icon, ['SpeedDialIcon']) ? /*#__PURE__*/React.cloneElement(icon, {\n          open\n        }) : icon\n      }))\n    })), /*#__PURE__*/_jsx(SpeedDialActions, {\n      id: `${id}-actions`,\n      role: \"menu\",\n      \"aria-orientation\": getOrientation(direction),\n      className: clsx(classes.actions, !open && classes.actionsClosed),\n      ownerState: ownerState,\n      children: children\n    })]\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? SpeedDial.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The aria-label of the button element.\n   * Also used to provide the `id` for the `SpeedDial` element and its children.\n   */\n  ariaLabel: PropTypes.string.isRequired,\n  /**\n   * SpeedDialActions to display when the SpeedDial is `open`.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The direction the actions open relative to the floating action button.\n   * @default 'up'\n   */\n  direction: PropTypes.oneOf(['down', 'left', 'right', 'up']),\n  /**\n   * Props applied to the [`Fab`](/material-ui/api/fab/) element.\n   * @default {}\n   */\n  FabProps: PropTypes.object,\n  /**\n   * If `true`, the SpeedDial is hidden.\n   * @default false\n   */\n  hidden: PropTypes.bool,\n  /**\n   * The icon to display in the SpeedDial Fab. The `SpeedDialIcon` component\n   * provides a default Icon with animation.\n   */\n  icon: PropTypes.node,\n  /**\n   * @ignore\n   */\n  onBlur: PropTypes.func,\n  /**\n   * Callback fired when the component requests to be closed.\n   *\n   * @param {object} event The event source of the callback.\n   * @param {string} reason Can be: `\"toggle\"`, `\"blur\"`, `\"mouseLeave\"`, `\"escapeKeyDown\"`.\n   */\n  onClose: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onFocus: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onKeyDown: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onMouseEnter: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onMouseLeave: PropTypes.func,\n  /**\n   * Callback fired when the component requests to be open.\n   *\n   * @param {object} event The event source of the callback.\n   * @param {string} reason Can be: `\"toggle\"`, `\"focus\"`, `\"mouseEnter\"`.\n   */\n  onOpen: PropTypes.func,\n  /**\n   * If `true`, the component is shown.\n   */\n  open: PropTypes.bool,\n  /**\n   * The icon to display in the SpeedDial Fab when the SpeedDial is open.\n   */\n  openIcon: PropTypes.node,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The component used for the transition.\n   * [Follow this guide](/material-ui/transitions/#transitioncomponent-prop) to learn more about the requirements for this component.\n   * @default Zoom\n   */\n  TransitionComponent: PropTypes.elementType,\n  /**\n   * The duration for the transition, in milliseconds.\n   * You may specify a single timeout for all transitions, or individually with an object.\n   * @default {\n   *   enter: theme.transitions.duration.enteringScreen,\n   *   exit: theme.transitions.duration.leavingScreen,\n   * }\n   */\n  transitionDuration: PropTypes.oneOfType([PropTypes.number, PropTypes.shape({\n    appear: PropTypes.number,\n    enter: PropTypes.number,\n    exit: PropTypes.number\n  })]),\n  /**\n   * Props applied to the transition element.\n   * By default, the element is based on this [`Transition`](https://reactcommunity.org/react-transition-group/transition/) component.\n   */\n  TransitionProps: PropTypes.object\n} : void 0;\nexport default SpeedDial;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,MAAMC,SAAS,GAAG,CAAC,KAAK,CAAC;EACvBC,UAAU,GAAG,CAAC,WAAW,EAAE,UAAU,EAAE,UAAU,EAAE,WAAW,EAAE,WAAW,EAAE,QAAQ,EAAE,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,WAAW,EAAE,cAAc,EAAE,cAAc,EAAE,QAAQ,EAAE,MAAM,EAAE,UAAU,EAAE,qBAAqB,EAAE,oBAAoB,EAAE,iBAAiB,CAAC;EACzQC,UAAU,GAAG,CAAC,KAAK,CAAC;AACtB,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,QAAQ,UAAU;AACrC,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,cAAc,MAAM,2BAA2B;AACtD,OAAOC,UAAU,MAAM,uBAAuB;AAC9C,OAAOC,KAAK,MAAM,kBAAkB;AACpC,OAAOC,MAAM,MAAM,kBAAkB;AACrC,SAASC,eAAe,QAAQ,yBAAyB;AACzD,OAAOC,QAAQ,MAAM,oBAAoB;AACzC,OAAOC,IAAI,MAAM,SAAS;AAC1B,OAAOC,GAAG,MAAM,QAAQ;AACxB,OAAOC,UAAU,MAAM,qBAAqB;AAC5C,OAAOC,YAAY,MAAM,uBAAuB;AAChD,OAAOC,UAAU,MAAM,qBAAqB;AAC5C,OAAOC,aAAa,MAAM,wBAAwB;AAClD,OAAOC,gBAAgB,IAAIC,wBAAwB,QAAQ,oBAAoB;AAC/E,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AACjD,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC,OAAO;IACPC,IAAI;IACJC;EACF,CAAC,GAAGH,UAAU;EACd,MAAMI,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,EAAE,YAAYhB,UAAU,CAACc,SAAS,CAAC,EAAE,CAAC;IACnDG,GAAG,EAAE,CAAC,KAAK,CAAC;IACZC,OAAO,EAAE,CAAC,SAAS,EAAE,CAACL,IAAI,IAAI,eAAe;EAC/C,CAAC;EACD,OAAOrB,cAAc,CAACuB,KAAK,EAAEV,wBAAwB,EAAEO,OAAO,CAAC;AACjE,CAAC;AACD,SAASO,cAAcA,CAACL,SAAS,EAAE;EACjC,IAAIA,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,MAAM,EAAE;IAC9C,OAAO,UAAU;EACnB;EACA,IAAIA,SAAS,KAAK,OAAO,IAAIA,SAAS,KAAK,MAAM,EAAE;IACjD,OAAO,YAAY;EACrB;EACA,OAAOM,SAAS;AAClB;AACA,MAAMC,UAAU,GAAG,EAAE;AACrB,MAAMC,cAAc,GAAG,EAAE;AACzB,MAAMC,aAAa,GAAG5B,MAAM,CAAC,KAAK,EAAE;EAClC6B,IAAI,EAAE,cAAc;EACpBC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJjB;IACF,CAAC,GAAGgB,KAAK;IACT,OAAO,CAACC,MAAM,CAACZ,IAAI,EAAEY,MAAM,CAAC,YAAY5B,UAAU,CAACW,UAAU,CAACG,SAAS,CAAC,EAAE,CAAC,CAAC;EAC9E;AACF,CAAC,CAAC,CAACe,IAAA;EAAA,IAAC;IACFC,KAAK;IACLnB;EACF,CAAC,GAAAkB,IAAA;EAAA,OAAK7C,QAAQ,CAAC;IACb+C,MAAM,EAAE,CAACD,KAAK,CAACE,IAAI,IAAIF,KAAK,EAAEC,MAAM,CAACE,SAAS;IAC9CC,OAAO,EAAE,MAAM;IACfC,UAAU,EAAE,QAAQ;IACpBC,aAAa,EAAE;EACjB,CAAC,EAAEzB,UAAU,CAACG,SAAS,KAAK,IAAI,IAAI;IAClCuB,aAAa,EAAE,gBAAgB;IAC/B,CAAC,MAAMjC,gBAAgB,CAACc,OAAO,EAAE,GAAG;MAClCmB,aAAa,EAAE,gBAAgB;MAC/BC,YAAY,EAAE,CAACjB,UAAU;MACzBkB,aAAa,EAAEjB,cAAc,GAAGD;IAClC;EACF,CAAC,EAAEV,UAAU,CAACG,SAAS,KAAK,MAAM,IAAI;IACpCuB,aAAa,EAAE,QAAQ;IACvB,CAAC,MAAMjC,gBAAgB,CAACc,OAAO,EAAE,GAAG;MAClCmB,aAAa,EAAE,QAAQ;MACvBG,SAAS,EAAE,CAACnB,UAAU;MACtBoB,UAAU,EAAEnB,cAAc,GAAGD;IAC/B;EACF,CAAC,EAAEV,UAAU,CAACG,SAAS,KAAK,MAAM,IAAI;IACpCuB,aAAa,EAAE,aAAa;IAC5B,CAAC,MAAMjC,gBAAgB,CAACc,OAAO,EAAE,GAAG;MAClCmB,aAAa,EAAE,aAAa;MAC5BK,WAAW,EAAE,CAACrB,UAAU;MACxBsB,YAAY,EAAErB,cAAc,GAAGD;IACjC;EACF,CAAC,EAAEV,UAAU,CAACG,SAAS,KAAK,OAAO,IAAI;IACrCuB,aAAa,EAAE,KAAK;IACpB,CAAC,MAAMjC,gBAAgB,CAACc,OAAO,EAAE,GAAG;MAClCmB,aAAa,EAAE,KAAK;MACpBO,UAAU,EAAE,CAACvB,UAAU;MACvBwB,WAAW,EAAEvB,cAAc,GAAGD;IAChC;EACF,CAAC,CAAC;AAAA,EAAC;AACH,MAAMyB,YAAY,GAAGnD,MAAM,CAACI,GAAG,EAAE;EAC/ByB,IAAI,EAAE,cAAc;EACpBC,IAAI,EAAE,KAAK;EACXC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAKA,MAAM,CAACX;AAC/C,CAAC,CAAC,CAAC,OAAO;EACRmB,aAAa,EAAE;AACjB,CAAC,CAAC,CAAC;AACH,MAAMW,gBAAgB,GAAGpD,MAAM,CAAC,KAAK,EAAE;EACrC6B,IAAI,EAAE,cAAc;EACpBC,IAAI,EAAE,SAAS;EACfC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJjB;IACF,CAAC,GAAGgB,KAAK;IACT,OAAO,CAACC,MAAM,CAACV,OAAO,EAAE,CAACP,UAAU,CAACE,IAAI,IAAIe,MAAM,CAACoB,aAAa,CAAC;EACnE;AACF,CAAC,CAAC,CAACC,KAAA;EAAA,IAAC;IACFtC;EACF,CAAC,GAAAsC,KAAA;EAAA,OAAKjE,QAAQ,CAAC;IACbkD,OAAO,EAAE,MAAM;IACfE,aAAa,EAAE;EACjB,CAAC,EAAE,CAACzB,UAAU,CAACE,IAAI,IAAI;IACrBqC,UAAU,EAAE,oBAAoB;IAChCd,aAAa,EAAE;EACjB,CAAC,CAAC;AAAA,EAAC;AACH,MAAMe,SAAS,GAAG,aAAa/D,KAAK,CAACgE,UAAU,CAAC,SAASD,SAASA,CAACE,OAAO,EAAEC,GAAG,EAAE;EAC/E,MAAM3B,KAAK,GAAG/B,eAAe,CAAC;IAC5B+B,KAAK,EAAE0B,OAAO;IACd7B,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAMM,KAAK,GAAGjC,QAAQ,CAAC,CAAC;EACxB,MAAM0D,yBAAyB,GAAG;IAChCC,KAAK,EAAE1B,KAAK,CAAC2B,WAAW,CAACC,QAAQ,CAACC,cAAc;IAChDC,IAAI,EAAE9B,KAAK,CAAC2B,WAAW,CAACC,QAAQ,CAACG;EACnC,CAAC;EACD,MAAM;MACFC,SAAS;MACTC,QAAQ,EAAE;QACRT,GAAG,EAAEU;MACP,CAAC,GAAG,CAAC,CAAC;MACNC,QAAQ,EAAEC,YAAY;MACtBC,SAAS;MACTrD,SAAS,GAAG,IAAI;MAChBsD,MAAM,GAAG,KAAK;MACdC,IAAI;MACJC,MAAM;MACNC,OAAO;MACPC,OAAO;MACPC,SAAS;MACTC,YAAY;MACZC,YAAY;MACZC,MAAM;MACN/D,IAAI,EAAEgE,QAAQ;MACdC,mBAAmB,GAAGhF,IAAI;MAC1BiF,kBAAkB,GAAGxB,yBAAyB;MAC9CyB;IACF,CAAC,GAAGrD,KAAK;IACToC,QAAQ,GAAGhF,6BAA6B,CAAC4C,KAAK,CAACoC,QAAQ,EAAE9E,SAAS,CAAC;IACnEgG,KAAK,GAAGlG,6BAA6B,CAAC4C,KAAK,EAAEzC,UAAU,CAAC;EAC1D,MAAM,CAAC2B,IAAI,EAAEqE,YAAY,CAAC,GAAG/E,aAAa,CAAC;IACzCgF,UAAU,EAAEN,QAAQ;IACpBO,OAAO,EAAE,KAAK;IACd5D,IAAI,EAAE,WAAW;IACjB6D,KAAK,EAAE;EACT,CAAC,CAAC;EACF,MAAM1E,UAAU,GAAG3B,QAAQ,CAAC,CAAC,CAAC,EAAE2C,KAAK,EAAE;IACrCd,IAAI;IACJC;EACF,CAAC,CAAC;EACF,MAAMF,OAAO,GAAGF,iBAAiB,CAACC,UAAU,CAAC;EAC7C,MAAM2E,UAAU,GAAG7F,UAAU,CAAC,CAAC;;EAE/B;AACF;AACA;EACE,MAAM8F,aAAa,GAAGnG,KAAK,CAACoG,MAAM,CAAC,CAAC,CAAC;;EAErC;AACF;AACA;AACA;AACA;AACA;AACA;EACE,MAAMC,gBAAgB,GAAGrG,KAAK,CAACoG,MAAM,CAAC,CAAC;;EAEvC;AACF;AACA;AACA;AACA;EACE,MAAMtE,OAAO,GAAG9B,KAAK,CAACoG,MAAM,CAAC,EAAE,CAAC;EAChCtE,OAAO,CAACwE,OAAO,GAAG,CAACxE,OAAO,CAACwE,OAAO,CAAC,CAAC,CAAC,CAAC;EACtC,MAAMC,eAAe,GAAGvG,KAAK,CAACwG,WAAW,CAACC,MAAM,IAAI;IAClD3E,OAAO,CAACwE,OAAO,CAAC,CAAC,CAAC,GAAGG,MAAM;EAC7B,CAAC,EAAE,EAAE,CAAC;EACN,MAAMC,YAAY,GAAG5F,UAAU,CAAC8D,iBAAiB,EAAE2B,eAAe,CAAC;;EAEnE;AACF;AACA;AACA;AACA;AACA;AACA;EACE,MAAMI,oCAAoC,GAAGA,CAACC,eAAe,EAAEC,aAAa,KAAK;IAC/E,OAAOC,SAAS,IAAI;MAClBhF,OAAO,CAACwE,OAAO,CAACM,eAAe,GAAG,CAAC,CAAC,GAAGE,SAAS;MAChD,IAAID,aAAa,EAAE;QACjBA,aAAa,CAACC,SAAS,CAAC;MAC1B;IACF,CAAC;EACH,CAAC;EACD,MAAMC,aAAa,GAAGC,KAAK,IAAI;IAC7B,IAAI3B,SAAS,EAAE;MACbA,SAAS,CAAC2B,KAAK,CAAC;IAClB;IACA,MAAMC,GAAG,GAAGD,KAAK,CAACC,GAAG,CAACC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAACC,WAAW,CAAC,CAAC;IACxD,MAAM;MACJb,OAAO,EAAEc,uBAAuB,GAAGH;IACrC,CAAC,GAAGZ,gBAAgB;IACpB,IAAIW,KAAK,CAACC,GAAG,KAAK,QAAQ,EAAE;MAC1BnB,YAAY,CAAC,KAAK,CAAC;MACnBhE,OAAO,CAACwE,OAAO,CAAC,CAAC,CAAC,CAACe,KAAK,CAAC,CAAC;MAC1B,IAAIlC,OAAO,EAAE;QACXA,OAAO,CAAC6B,KAAK,EAAE,eAAe,CAAC;MACjC;MACA;IACF;IACA,IAAIjF,cAAc,CAACkF,GAAG,CAAC,KAAKlF,cAAc,CAACqF,uBAAuB,CAAC,IAAIrF,cAAc,CAACkF,GAAG,CAAC,KAAKjF,SAAS,EAAE;MACxGgF,KAAK,CAACM,cAAc,CAAC,CAAC;MACtB,MAAMC,UAAU,GAAGN,GAAG,KAAKG,uBAAuB,GAAG,CAAC,GAAG,CAAC,CAAC;;MAE3D;MACA,MAAMI,UAAU,GAAGlH,KAAK,CAAC6F,aAAa,CAACG,OAAO,GAAGiB,UAAU,EAAE,CAAC,EAAEzF,OAAO,CAACwE,OAAO,CAACmB,MAAM,GAAG,CAAC,CAAC;MAC3F3F,OAAO,CAACwE,OAAO,CAACkB,UAAU,CAAC,CAACH,KAAK,CAAC,CAAC;MACnClB,aAAa,CAACG,OAAO,GAAGkB,UAAU;MAClCnB,gBAAgB,CAACC,OAAO,GAAGc,uBAAuB;IACpD;EACF,CAAC;EACDpH,KAAK,CAAC0H,SAAS,CAAC,MAAM;IACpB;IACA,IAAI,CAACjG,IAAI,EAAE;MACT0E,aAAa,CAACG,OAAO,GAAG,CAAC;MACzBD,gBAAgB,CAACC,OAAO,GAAGtE,SAAS;IACtC;EACF,CAAC,EAAE,CAACP,IAAI,CAAC,CAAC;EACV,MAAMkG,WAAW,GAAGX,KAAK,IAAI;IAC3B,IAAIA,KAAK,CAACY,IAAI,KAAK,YAAY,IAAIrC,YAAY,EAAE;MAC/CA,YAAY,CAACyB,KAAK,CAAC;IACrB;IACA,IAAIA,KAAK,CAACY,IAAI,KAAK,MAAM,IAAI1C,MAAM,EAAE;MACnCA,MAAM,CAAC8B,KAAK,CAAC;IACf;IACAd,UAAU,CAAC2B,KAAK,CAAC,CAAC;IAClB,IAAIb,KAAK,CAACY,IAAI,KAAK,MAAM,EAAE;MACzB1B,UAAU,CAAC4B,KAAK,CAAC,CAAC,EAAE,MAAM;QACxBhC,YAAY,CAAC,KAAK,CAAC;QACnB,IAAIX,OAAO,EAAE;UACXA,OAAO,CAAC6B,KAAK,EAAE,MAAM,CAAC;QACxB;MACF,CAAC,CAAC;IACJ,CAAC,MAAM;MACLlB,YAAY,CAAC,KAAK,CAAC;MACnB,IAAIX,OAAO,EAAE;QACXA,OAAO,CAAC6B,KAAK,EAAE,YAAY,CAAC;MAC9B;IACF;EACF,CAAC;EACD,MAAMe,WAAW,GAAGf,KAAK,IAAI;IAC3B,IAAIrC,QAAQ,CAACqD,OAAO,EAAE;MACpBrD,QAAQ,CAACqD,OAAO,CAAChB,KAAK,CAAC;IACzB;IACAd,UAAU,CAAC2B,KAAK,CAAC,CAAC;IAClB,IAAIpG,IAAI,EAAE;MACRqE,YAAY,CAAC,KAAK,CAAC;MACnB,IAAIX,OAAO,EAAE;QACXA,OAAO,CAAC6B,KAAK,EAAE,QAAQ,CAAC;MAC1B;IACF,CAAC,MAAM;MACLlB,YAAY,CAAC,IAAI,CAAC;MAClB,IAAIN,MAAM,EAAE;QACVA,MAAM,CAACwB,KAAK,EAAE,QAAQ,CAAC;MACzB;IACF;EACF,CAAC;EACD,MAAMiB,UAAU,GAAGjB,KAAK,IAAI;IAC1B,IAAIA,KAAK,CAACY,IAAI,KAAK,YAAY,IAAItC,YAAY,EAAE;MAC/CA,YAAY,CAAC0B,KAAK,CAAC;IACrB;IACA,IAAIA,KAAK,CAACY,IAAI,KAAK,OAAO,IAAIxC,OAAO,EAAE;MACrCA,OAAO,CAAC4B,KAAK,CAAC;IAChB;;IAEA;IACA;IACA;IACAd,UAAU,CAAC2B,KAAK,CAAC,CAAC;IAClB,IAAI,CAACpG,IAAI,EAAE;MACT;MACAyE,UAAU,CAAC4B,KAAK,CAAC,CAAC,EAAE,MAAM;QACxBhC,YAAY,CAAC,IAAI,CAAC;QAClB,IAAIN,MAAM,EAAE;UACV,MAAM0C,QAAQ,GAAG;YACfb,KAAK,EAAE,OAAO;YACdc,UAAU,EAAE;UACd,CAAC;UACD3C,MAAM,CAACwB,KAAK,EAAEkB,QAAQ,CAAClB,KAAK,CAACY,IAAI,CAAC,CAAC;QACrC;MACF,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAMQ,EAAE,GAAG1D,SAAS,CAACwC,OAAO,CAAC,sBAAsB,EAAE,EAAE,CAAC;EACxD,MAAMmB,QAAQ,GAAGrI,KAAK,CAACsI,QAAQ,CAACC,OAAO,CAACzD,YAAY,CAAC,CAAC0D,MAAM,CAACC,KAAK,IAAI;IACpE,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC,IAAI3I,UAAU,CAACwI,KAAK,CAAC,EAAE;QACrBI,OAAO,CAACC,KAAK,CAAC,CAAC,oEAAoE,EAAE,sCAAsC,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;MAC1I;IACF;IACA,OAAO,aAAa/I,KAAK,CAACgJ,cAAc,CAACP,KAAK,CAAC;EACjD,CAAC,CAAC;EACF,MAAM5D,QAAQ,GAAGwD,QAAQ,CAACY,GAAG,CAAC,CAACR,KAAK,EAAES,KAAK,KAAK;IAC9C,MAAMC,YAAY,GAAGV,KAAK,CAAClG,KAAK;MAC9B;QACEoC,QAAQ,EAAE;UACRT,GAAG,EAAE2C;QACP,CAAC,GAAG,CAAC,CAAC;QACNuC,gBAAgB,EAAEC;MACpB,CAAC,GAAGF,YAAY;MAChBG,aAAa,GAAG3J,6BAA6B,CAACwJ,YAAY,CAACxE,QAAQ,EAAE5E,UAAU,CAAC;IAClF,MAAMqJ,gBAAgB,GAAGC,oBAAoB,KAAKtH,cAAc,CAACL,SAAS,CAAC,KAAK,UAAU,GAAG,MAAM,GAAG,KAAK,CAAC;IAC5G,OAAO,aAAa1B,KAAK,CAACuJ,YAAY,CAACd,KAAK,EAAE;MAC5C9D,QAAQ,EAAE/E,QAAQ,CAAC,CAAC,CAAC,EAAE0J,aAAa,EAAE;QACpCpF,GAAG,EAAEyC,oCAAoC,CAACuC,KAAK,EAAErC,aAAa;MAChE,CAAC,CAAC;MACF2C,KAAK,EAAE,EAAE,IAAI/H,IAAI,GAAGyH,KAAK,GAAGb,QAAQ,CAACZ,MAAM,GAAGyB,KAAK,CAAC;MACpDzH,IAAI;MACJ2H,gBAAgB;MAChBhB,EAAE,EAAE,GAAGA,EAAE,WAAWc,KAAK;IAC3B,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,OAAO,aAAa7H,KAAK,CAACc,aAAa,EAAEvC,QAAQ,CAAC;IAChDmF,SAAS,EAAE5E,IAAI,CAACqB,OAAO,CAACI,IAAI,EAAEmD,SAAS,CAAC;IACxCb,GAAG,EAAEA,GAAG;IACRuF,IAAI,EAAE,cAAc;IACpBpE,SAAS,EAAE0B,aAAa;IACxB7B,MAAM,EAAEyC,WAAW;IACnBvC,OAAO,EAAE6C,UAAU;IACnB3C,YAAY,EAAE2C,UAAU;IACxB1C,YAAY,EAAEoC,WAAW;IACzBpG,UAAU,EAAEA;EACd,CAAC,EAAEsE,KAAK,EAAE;IACRhB,QAAQ,EAAE,CAAC,aAAa1D,IAAI,CAACuE,mBAAmB,EAAE9F,QAAQ,CAAC;MACzD8J,EAAE,EAAE,CAAC1E,MAAM;MACX2E,OAAO,EAAEhE,kBAAkB;MAC3BiE,aAAa,EAAE;IACjB,CAAC,EAAEhE,eAAe,EAAE;MAClBf,QAAQ,EAAE,aAAa1D,IAAI,CAACuC,YAAY,EAAE9D,QAAQ,CAAC;QACjDiK,KAAK,EAAE,SAAS;QAChB,YAAY,EAAEnF,SAAS;QACvB,eAAe,EAAE,MAAM;QACvB,eAAe,EAAEjD,IAAI;QACrB,eAAe,EAAE,GAAG2G,EAAE;MACxB,CAAC,EAAEzD,QAAQ,EAAE;QACXqD,OAAO,EAAED,WAAW;QACpBhD,SAAS,EAAE5E,IAAI,CAACqB,OAAO,CAACK,GAAG,EAAE8C,QAAQ,CAACI,SAAS,CAAC;QAChDb,GAAG,EAAEwC,YAAY;QACjBnF,UAAU,EAAEA,UAAU;QACtBsD,QAAQ,EAAE,aAAa7E,KAAK,CAACgJ,cAAc,CAAC/D,IAAI,CAAC,IAAIpE,YAAY,CAACoE,IAAI,EAAE,CAAC,eAAe,CAAC,CAAC,GAAG,aAAajF,KAAK,CAACuJ,YAAY,CAACtE,IAAI,EAAE;UACjIxD;QACF,CAAC,CAAC,GAAGwD;MACP,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC,EAAE,aAAa9D,IAAI,CAACwC,gBAAgB,EAAE;MACvCyE,EAAE,EAAE,GAAGA,EAAE,UAAU;MACnBqB,IAAI,EAAE,MAAM;MACZ,kBAAkB,EAAE1H,cAAc,CAACL,SAAS,CAAC;MAC7CqD,SAAS,EAAE5E,IAAI,CAACqB,OAAO,CAACM,OAAO,EAAE,CAACL,IAAI,IAAID,OAAO,CAACoC,aAAa,CAAC;MAChErC,UAAU,EAAEA,UAAU;MACtBsD,QAAQ,EAAEA;IACZ,CAAC,CAAC;EACJ,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACF6D,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG7E,SAAS,CAAC+F,SAAS,CAAC,yBAAyB;EACnF;EACA;EACA;EACA;EACA;AACF;AACA;AACA;EACEpF,SAAS,EAAExE,SAAS,CAAC6J,MAAM,CAACC,UAAU;EACtC;AACF;AACA;EACEnF,QAAQ,EAAE3E,SAAS,CAAC+J,IAAI;EACxB;AACF;AACA;EACEzI,OAAO,EAAEtB,SAAS,CAACgK,MAAM;EACzB;AACF;AACA;EACEnF,SAAS,EAAE7E,SAAS,CAAC6J,MAAM;EAC3B;AACF;AACA;AACA;EACErI,SAAS,EAAExB,SAAS,CAACiK,KAAK,CAAC,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;EAC3D;AACF;AACA;AACA;EACExF,QAAQ,EAAEzE,SAAS,CAACgK,MAAM;EAC1B;AACF;AACA;AACA;EACElF,MAAM,EAAE9E,SAAS,CAACkK,IAAI;EACtB;AACF;AACA;AACA;EACEnF,IAAI,EAAE/E,SAAS,CAAC+J,IAAI;EACpB;AACF;AACA;EACE/E,MAAM,EAAEhF,SAAS,CAACmK,IAAI;EACtB;AACF;AACA;AACA;AACA;AACA;EACElF,OAAO,EAAEjF,SAAS,CAACmK,IAAI;EACvB;AACF;AACA;EACEjF,OAAO,EAAElF,SAAS,CAACmK,IAAI;EACvB;AACF;AACA;EACEhF,SAAS,EAAEnF,SAAS,CAACmK,IAAI;EACzB;AACF;AACA;EACE/E,YAAY,EAAEpF,SAAS,CAACmK,IAAI;EAC5B;AACF;AACA;EACE9E,YAAY,EAAErF,SAAS,CAACmK,IAAI;EAC5B;AACF;AACA;AACA;AACA;AACA;EACE7E,MAAM,EAAEtF,SAAS,CAACmK,IAAI;EACtB;AACF;AACA;EACE5I,IAAI,EAAEvB,SAAS,CAACkK,IAAI;EACpB;AACF;AACA;EACEE,QAAQ,EAAEpK,SAAS,CAAC+J,IAAI;EACxB;AACF;AACA;EACEM,EAAE,EAAErK,SAAS,CAACsK,SAAS,CAAC,CAACtK,SAAS,CAACuK,OAAO,CAACvK,SAAS,CAACsK,SAAS,CAAC,CAACtK,SAAS,CAACmK,IAAI,EAAEnK,SAAS,CAACgK,MAAM,EAAEhK,SAAS,CAACkK,IAAI,CAAC,CAAC,CAAC,EAAElK,SAAS,CAACmK,IAAI,EAAEnK,SAAS,CAACgK,MAAM,CAAC,CAAC;EACvJ;AACF;AACA;AACA;AACA;EACExE,mBAAmB,EAAExF,SAAS,CAACwK,WAAW;EAC1C;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE/E,kBAAkB,EAAEzF,SAAS,CAACsK,SAAS,CAAC,CAACtK,SAAS,CAACyK,MAAM,EAAEzK,SAAS,CAAC0K,KAAK,CAAC;IACzEC,MAAM,EAAE3K,SAAS,CAACyK,MAAM;IACxBvG,KAAK,EAAElE,SAAS,CAACyK,MAAM;IACvBnG,IAAI,EAAEtE,SAAS,CAACyK;EAClB,CAAC,CAAC,CAAC,CAAC;EACJ;AACF;AACA;AACA;EACE/E,eAAe,EAAE1F,SAAS,CAACgK;AAC7B,CAAC,GAAG,KAAK,CAAC;AACV,eAAenG,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}