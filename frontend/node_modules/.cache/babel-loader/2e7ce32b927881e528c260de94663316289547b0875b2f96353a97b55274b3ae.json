{"ast": null, "code": "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"children\", \"className\", \"only\"];\nimport * as React from 'react';\nimport clsx from 'clsx';\nimport PropTypes from 'prop-types';\nimport composeClasses from '@mui/utils/composeClasses';\nimport capitalize from '../utils/capitalize';\nimport styled from '../styles/styled';\nimport useTheme from '../styles/useTheme';\nimport { getHiddenCssUtilityClass } from './hiddenCssClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    breakpoints\n  } = ownerState;\n  const slots = {\n    root: ['root', ...breakpoints.map(_ref => {\n      let {\n        breakpoint,\n        dir\n      } = _ref;\n      return dir === 'only' ? `${dir}${capitalize(breakpoint)}` : `${breakpoint}${capitalize(dir)}`;\n    })]\n  };\n  return composeClasses(slots, getHiddenCssUtilityClass, classes);\n};\nconst HiddenCssRoot = styled('div', {\n  name: 'PrivateHiddenCss',\n  slot: 'Root'\n})(_ref2 => {\n  let {\n    theme,\n    ownerState\n  } = _ref2;\n  const hidden = {\n    display: 'none'\n  };\n  return _extends({}, ownerState.breakpoints.map(_ref3 => {\n    let {\n      breakpoint,\n      dir\n    } = _ref3;\n    if (dir === 'only') {\n      return {\n        [theme.breakpoints.only(breakpoint)]: hidden\n      };\n    }\n    return dir === 'up' ? {\n      [theme.breakpoints.up(breakpoint)]: hidden\n    } : {\n      [theme.breakpoints.down(breakpoint)]: hidden\n    };\n  }).reduce((r, o) => {\n    Object.keys(o).forEach(k => {\n      r[k] = o[k];\n    });\n    return r;\n  }, {}));\n});\n\n/**\n * @ignore - internal component.\n */\nfunction HiddenCss(props) {\n  const {\n      children,\n      className,\n      only\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const theme = useTheme();\n  if (process.env.NODE_ENV !== 'production') {\n    const unknownProps = Object.keys(other).filter(propName => {\n      const isUndeclaredBreakpoint = !theme.breakpoints.keys.some(breakpoint => {\n        return `${breakpoint}Up` === propName || `${breakpoint}Down` === propName;\n      });\n      return !['classes', 'theme', 'isRtl', 'sx'].includes(propName) && isUndeclaredBreakpoint;\n    });\n    if (unknownProps.length > 0) {\n      console.error(`MUI: Unsupported props received by \\`<Hidden implementation=\"css\" />\\`: ${unknownProps.join(', ')}. Did you forget to wrap this component in a ThemeProvider declaring these breakpoints?`);\n    }\n  }\n  const breakpoints = [];\n  for (let i = 0; i < theme.breakpoints.keys.length; i += 1) {\n    const breakpoint = theme.breakpoints.keys[i];\n    const breakpointUp = other[`${breakpoint}Up`];\n    const breakpointDown = other[`${breakpoint}Down`];\n    if (breakpointUp) {\n      breakpoints.push({\n        breakpoint,\n        dir: 'up'\n      });\n    }\n    if (breakpointDown) {\n      breakpoints.push({\n        breakpoint,\n        dir: 'down'\n      });\n    }\n  }\n  if (only) {\n    const onlyBreakpoints = Array.isArray(only) ? only : [only];\n    onlyBreakpoints.forEach(breakpoint => {\n      breakpoints.push({\n        breakpoint,\n        dir: 'only'\n      });\n    });\n  }\n  const ownerState = _extends({}, props, {\n    breakpoints\n  });\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(HiddenCssRoot, {\n    className: clsx(classes.root, className),\n    ownerState: ownerState,\n    children: children\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? HiddenCss.propTypes = {\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * Specify which implementation to use.  'js' is the default, 'css' works better for\n   * server-side rendering.\n   */\n  implementation: PropTypes.oneOf(['js', 'css']),\n  /**\n   * If `true`, screens this size and down are hidden.\n   */\n  lgDown: PropTypes.bool,\n  /**\n   * If `true`, screens this size and up are hidden.\n   */\n  lgUp: PropTypes.bool,\n  /**\n   * If `true`, screens this size and down are hidden.\n   */\n  mdDown: PropTypes.bool,\n  /**\n   * If `true`, screens this size and up are hidden.\n   */\n  mdUp: PropTypes.bool,\n  /**\n   * Hide the given breakpoint(s).\n   */\n  only: PropTypes.oneOfType([PropTypes.oneOf(['xs', 'sm', 'md', 'lg', 'xl']), PropTypes.arrayOf(PropTypes.oneOf(['xs', 'sm', 'md', 'lg', 'xl']))]),\n  /**\n   * If `true`, screens this size and down are hidden.\n   */\n  smDown: PropTypes.bool,\n  /**\n   * If `true`, screens this size and up are hidden.\n   */\n  smUp: PropTypes.bool,\n  /**\n   * If `true`, screens this size and down are hidden.\n   */\n  xlDown: PropTypes.bool,\n  /**\n   * If `true`, screens this size and up are hidden.\n   */\n  xlUp: PropTypes.bool,\n  /**\n   * If `true`, screens this size and down are hidden.\n   */\n  xsDown: PropTypes.bool,\n  /**\n   * If `true`, screens this size and up are hidden.\n   */\n  xsUp: PropTypes.bool\n} : void 0;\nexport default HiddenCss;", "map": {"version": 3, "names": ["_objectWithoutPropertiesLoose", "_extends", "_excluded", "React", "clsx", "PropTypes", "composeClasses", "capitalize", "styled", "useTheme", "getHiddenCssUtilityClass", "jsx", "_jsx", "useUtilityClasses", "ownerState", "classes", "breakpoints", "slots", "root", "map", "_ref", "breakpoint", "dir", "HiddenCssRoot", "name", "slot", "_ref2", "theme", "hidden", "display", "_ref3", "only", "up", "down", "reduce", "r", "o", "Object", "keys", "for<PERSON>ach", "k", "HiddenCss", "props", "children", "className", "other", "process", "env", "NODE_ENV", "unknownProps", "filter", "propName", "isUndeclaredBreakpoint", "some", "includes", "length", "console", "error", "join", "i", "breakpointUp", "breakpointDown", "push", "onlyBreakpoints", "Array", "isArray", "propTypes", "node", "string", "implementation", "oneOf", "lgDown", "bool", "lgUp", "mdDown", "mdUp", "oneOfType", "arrayOf", "smDown", "smUp", "xlDown", "xlUp", "xsDown", "xsUp"], "sources": ["/home/<USER>/PycharmProjects/ai-voice-mate-dev/ai_voice_mate/frontend/node_modules/@mui/material/Hidden/HiddenCss.js"], "sourcesContent": ["'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"children\", \"className\", \"only\"];\nimport * as React from 'react';\nimport clsx from 'clsx';\nimport PropTypes from 'prop-types';\nimport composeClasses from '@mui/utils/composeClasses';\nimport capitalize from '../utils/capitalize';\nimport styled from '../styles/styled';\nimport useTheme from '../styles/useTheme';\nimport { getHiddenCssUtilityClass } from './hiddenCssClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    breakpoints\n  } = ownerState;\n  const slots = {\n    root: ['root', ...breakpoints.map(({\n      breakpoint,\n      dir\n    }) => {\n      return dir === 'only' ? `${dir}${capitalize(breakpoint)}` : `${breakpoint}${capitalize(dir)}`;\n    })]\n  };\n  return composeClasses(slots, getHiddenCssUtilityClass, classes);\n};\nconst HiddenCssRoot = styled('div', {\n  name: 'PrivateHiddenCss',\n  slot: 'Root'\n})(({\n  theme,\n  ownerState\n}) => {\n  const hidden = {\n    display: 'none'\n  };\n  return _extends({}, ownerState.breakpoints.map(({\n    breakpoint,\n    dir\n  }) => {\n    if (dir === 'only') {\n      return {\n        [theme.breakpoints.only(breakpoint)]: hidden\n      };\n    }\n    return dir === 'up' ? {\n      [theme.breakpoints.up(breakpoint)]: hidden\n    } : {\n      [theme.breakpoints.down(breakpoint)]: hidden\n    };\n  }).reduce((r, o) => {\n    Object.keys(o).forEach(k => {\n      r[k] = o[k];\n    });\n    return r;\n  }, {}));\n});\n\n/**\n * @ignore - internal component.\n */\nfunction HiddenCss(props) {\n  const {\n      children,\n      className,\n      only\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const theme = useTheme();\n  if (process.env.NODE_ENV !== 'production') {\n    const unknownProps = Object.keys(other).filter(propName => {\n      const isUndeclaredBreakpoint = !theme.breakpoints.keys.some(breakpoint => {\n        return `${breakpoint}Up` === propName || `${breakpoint}Down` === propName;\n      });\n      return !['classes', 'theme', 'isRtl', 'sx'].includes(propName) && isUndeclaredBreakpoint;\n    });\n    if (unknownProps.length > 0) {\n      console.error(`MUI: Unsupported props received by \\`<Hidden implementation=\"css\" />\\`: ${unknownProps.join(', ')}. Did you forget to wrap this component in a ThemeProvider declaring these breakpoints?`);\n    }\n  }\n  const breakpoints = [];\n  for (let i = 0; i < theme.breakpoints.keys.length; i += 1) {\n    const breakpoint = theme.breakpoints.keys[i];\n    const breakpointUp = other[`${breakpoint}Up`];\n    const breakpointDown = other[`${breakpoint}Down`];\n    if (breakpointUp) {\n      breakpoints.push({\n        breakpoint,\n        dir: 'up'\n      });\n    }\n    if (breakpointDown) {\n      breakpoints.push({\n        breakpoint,\n        dir: 'down'\n      });\n    }\n  }\n  if (only) {\n    const onlyBreakpoints = Array.isArray(only) ? only : [only];\n    onlyBreakpoints.forEach(breakpoint => {\n      breakpoints.push({\n        breakpoint,\n        dir: 'only'\n      });\n    });\n  }\n  const ownerState = _extends({}, props, {\n    breakpoints\n  });\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(HiddenCssRoot, {\n    className: clsx(classes.root, className),\n    ownerState: ownerState,\n    children: children\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? HiddenCss.propTypes = {\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * Specify which implementation to use.  'js' is the default, 'css' works better for\n   * server-side rendering.\n   */\n  implementation: PropTypes.oneOf(['js', 'css']),\n  /**\n   * If `true`, screens this size and down are hidden.\n   */\n  lgDown: PropTypes.bool,\n  /**\n   * If `true`, screens this size and up are hidden.\n   */\n  lgUp: PropTypes.bool,\n  /**\n   * If `true`, screens this size and down are hidden.\n   */\n  mdDown: PropTypes.bool,\n  /**\n   * If `true`, screens this size and up are hidden.\n   */\n  mdUp: PropTypes.bool,\n  /**\n   * Hide the given breakpoint(s).\n   */\n  only: PropTypes.oneOfType([PropTypes.oneOf(['xs', 'sm', 'md', 'lg', 'xl']), PropTypes.arrayOf(PropTypes.oneOf(['xs', 'sm', 'md', 'lg', 'xl']))]),\n  /**\n   * If `true`, screens this size and down are hidden.\n   */\n  smDown: PropTypes.bool,\n  /**\n   * If `true`, screens this size and up are hidden.\n   */\n  smUp: PropTypes.bool,\n  /**\n   * If `true`, screens this size and down are hidden.\n   */\n  xlDown: PropTypes.bool,\n  /**\n   * If `true`, screens this size and up are hidden.\n   */\n  xlUp: PropTypes.bool,\n  /**\n   * If `true`, screens this size and down are hidden.\n   */\n  xsDown: PropTypes.bool,\n  /**\n   * If `true`, screens this size and up are hidden.\n   */\n  xsUp: PropTypes.bool\n} : void 0;\nexport default HiddenCss;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,MAAMC,SAAS,GAAG,CAAC,UAAU,EAAE,WAAW,EAAE,MAAM,CAAC;AACnD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,cAAc,MAAM,2BAA2B;AACtD,OAAOC,UAAU,MAAM,qBAAqB;AAC5C,OAAOC,MAAM,MAAM,kBAAkB;AACrC,OAAOC,QAAQ,MAAM,oBAAoB;AACzC,SAASC,wBAAwB,QAAQ,oBAAoB;AAC7D,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC,OAAO;IACPC;EACF,CAAC,GAAGF,UAAU;EACd,MAAMG,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,EAAE,GAAGF,WAAW,CAACG,GAAG,CAACC,IAAA,IAG5B;MAAA,IAH6B;QACjCC,UAAU;QACVC;MACF,CAAC,GAAAF,IAAA;MACC,OAAOE,GAAG,KAAK,MAAM,GAAG,GAAGA,GAAG,GAAGf,UAAU,CAACc,UAAU,CAAC,EAAE,GAAG,GAAGA,UAAU,GAAGd,UAAU,CAACe,GAAG,CAAC,EAAE;IAC/F,CAAC,CAAC;EACJ,CAAC;EACD,OAAOhB,cAAc,CAACW,KAAK,EAAEP,wBAAwB,EAAEK,OAAO,CAAC;AACjE,CAAC;AACD,MAAMQ,aAAa,GAAGf,MAAM,CAAC,KAAK,EAAE;EAClCgB,IAAI,EAAE,kBAAkB;EACxBC,IAAI,EAAE;AACR,CAAC,CAAC,CAACC,KAAA,IAGG;EAAA,IAHF;IACFC,KAAK;IACLb;EACF,CAAC,GAAAY,KAAA;EACC,MAAME,MAAM,GAAG;IACbC,OAAO,EAAE;EACX,CAAC;EACD,OAAO5B,QAAQ,CAAC,CAAC,CAAC,EAAEa,UAAU,CAACE,WAAW,CAACG,GAAG,CAACW,KAAA,IAGzC;IAAA,IAH0C;MAC9CT,UAAU;MACVC;IACF,CAAC,GAAAQ,KAAA;IACC,IAAIR,GAAG,KAAK,MAAM,EAAE;MAClB,OAAO;QACL,CAACK,KAAK,CAACX,WAAW,CAACe,IAAI,CAACV,UAAU,CAAC,GAAGO;MACxC,CAAC;IACH;IACA,OAAON,GAAG,KAAK,IAAI,GAAG;MACpB,CAACK,KAAK,CAACX,WAAW,CAACgB,EAAE,CAACX,UAAU,CAAC,GAAGO;IACtC,CAAC,GAAG;MACF,CAACD,KAAK,CAACX,WAAW,CAACiB,IAAI,CAACZ,UAAU,CAAC,GAAGO;IACxC,CAAC;EACH,CAAC,CAAC,CAACM,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;IAClBC,MAAM,CAACC,IAAI,CAACF,CAAC,CAAC,CAACG,OAAO,CAACC,CAAC,IAAI;MAC1BL,CAAC,CAACK,CAAC,CAAC,GAAGJ,CAAC,CAACI,CAAC,CAAC;IACb,CAAC,CAAC;IACF,OAAOL,CAAC;EACV,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACT,CAAC,CAAC;;AAEF;AACA;AACA;AACA,SAASM,SAASA,CAACC,KAAK,EAAE;EACxB,MAAM;MACFC,QAAQ;MACRC,SAAS;MACTb;IACF,CAAC,GAAGW,KAAK;IACTG,KAAK,GAAG7C,6BAA6B,CAAC0C,KAAK,EAAExC,SAAS,CAAC;EACzD,MAAMyB,KAAK,GAAGlB,QAAQ,CAAC,CAAC;EACxB,IAAIqC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,MAAMC,YAAY,GAAGZ,MAAM,CAACC,IAAI,CAACO,KAAK,CAAC,CAACK,MAAM,CAACC,QAAQ,IAAI;MACzD,MAAMC,sBAAsB,GAAG,CAACzB,KAAK,CAACX,WAAW,CAACsB,IAAI,CAACe,IAAI,CAAChC,UAAU,IAAI;QACxE,OAAO,GAAGA,UAAU,IAAI,KAAK8B,QAAQ,IAAI,GAAG9B,UAAU,MAAM,KAAK8B,QAAQ;MAC3E,CAAC,CAAC;MACF,OAAO,CAAC,CAAC,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,IAAI,CAAC,CAACG,QAAQ,CAACH,QAAQ,CAAC,IAAIC,sBAAsB;IAC1F,CAAC,CAAC;IACF,IAAIH,YAAY,CAACM,MAAM,GAAG,CAAC,EAAE;MAC3BC,OAAO,CAACC,KAAK,CAAC,2EAA2ER,YAAY,CAACS,IAAI,CAAC,IAAI,CAAC,yFAAyF,CAAC;IAC5M;EACF;EACA,MAAM1C,WAAW,GAAG,EAAE;EACtB,KAAK,IAAI2C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGhC,KAAK,CAACX,WAAW,CAACsB,IAAI,CAACiB,MAAM,EAAEI,CAAC,IAAI,CAAC,EAAE;IACzD,MAAMtC,UAAU,GAAGM,KAAK,CAACX,WAAW,CAACsB,IAAI,CAACqB,CAAC,CAAC;IAC5C,MAAMC,YAAY,GAAGf,KAAK,CAAC,GAAGxB,UAAU,IAAI,CAAC;IAC7C,MAAMwC,cAAc,GAAGhB,KAAK,CAAC,GAAGxB,UAAU,MAAM,CAAC;IACjD,IAAIuC,YAAY,EAAE;MAChB5C,WAAW,CAAC8C,IAAI,CAAC;QACfzC,UAAU;QACVC,GAAG,EAAE;MACP,CAAC,CAAC;IACJ;IACA,IAAIuC,cAAc,EAAE;MAClB7C,WAAW,CAAC8C,IAAI,CAAC;QACfzC,UAAU;QACVC,GAAG,EAAE;MACP,CAAC,CAAC;IACJ;EACF;EACA,IAAIS,IAAI,EAAE;IACR,MAAMgC,eAAe,GAAGC,KAAK,CAACC,OAAO,CAAClC,IAAI,CAAC,GAAGA,IAAI,GAAG,CAACA,IAAI,CAAC;IAC3DgC,eAAe,CAACxB,OAAO,CAAClB,UAAU,IAAI;MACpCL,WAAW,CAAC8C,IAAI,CAAC;QACfzC,UAAU;QACVC,GAAG,EAAE;MACP,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EACA,MAAMR,UAAU,GAAGb,QAAQ,CAAC,CAAC,CAAC,EAAEyC,KAAK,EAAE;IACrC1B;EACF,CAAC,CAAC;EACF,MAAMD,OAAO,GAAGF,iBAAiB,CAACC,UAAU,CAAC;EAC7C,OAAO,aAAaF,IAAI,CAACW,aAAa,EAAE;IACtCqB,SAAS,EAAExC,IAAI,CAACW,OAAO,CAACG,IAAI,EAAE0B,SAAS,CAAC;IACxC9B,UAAU,EAAEA,UAAU;IACtB6B,QAAQ,EAAEA;EACZ,CAAC,CAAC;AACJ;AACAG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGP,SAAS,CAACyB,SAAS,GAAG;EAC5D;AACF;AACA;EACEvB,QAAQ,EAAEtC,SAAS,CAAC8D,IAAI;EACxB;AACF;AACA;EACEvB,SAAS,EAAEvC,SAAS,CAAC+D,MAAM;EAC3B;AACF;AACA;AACA;EACEC,cAAc,EAAEhE,SAAS,CAACiE,KAAK,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;EAC9C;AACF;AACA;EACEC,MAAM,EAAElE,SAAS,CAACmE,IAAI;EACtB;AACF;AACA;EACEC,IAAI,EAAEpE,SAAS,CAACmE,IAAI;EACpB;AACF;AACA;EACEE,MAAM,EAAErE,SAAS,CAACmE,IAAI;EACtB;AACF;AACA;EACEG,IAAI,EAAEtE,SAAS,CAACmE,IAAI;EACpB;AACF;AACA;EACEzC,IAAI,EAAE1B,SAAS,CAACuE,SAAS,CAAC,CAACvE,SAAS,CAACiE,KAAK,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,EAAEjE,SAAS,CAACwE,OAAO,CAACxE,SAAS,CAACiE,KAAK,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;EAChJ;AACF;AACA;EACEQ,MAAM,EAAEzE,SAAS,CAACmE,IAAI;EACtB;AACF;AACA;EACEO,IAAI,EAAE1E,SAAS,CAACmE,IAAI;EACpB;AACF;AACA;EACEQ,MAAM,EAAE3E,SAAS,CAACmE,IAAI;EACtB;AACF;AACA;EACES,IAAI,EAAE5E,SAAS,CAACmE,IAAI;EACpB;AACF;AACA;EACEU,MAAM,EAAE7E,SAAS,CAACmE,IAAI;EACtB;AACF;AACA;EACEW,IAAI,EAAE9E,SAAS,CAACmE;AAClB,CAAC,GAAG,KAAK,CAAC;AACV,eAAe/B,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}