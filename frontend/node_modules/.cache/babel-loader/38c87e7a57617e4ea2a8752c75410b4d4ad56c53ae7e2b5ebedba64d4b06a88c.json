{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport useEnhancedEffect from '@mui/utils/useEnhancedEffect';\nimport useId from '@mui/utils/useId';\nimport GlobalStyles from '../GlobalStyles';\nimport useThemeWithoutDefault from '../useThemeWithoutDefault';\n\n/**\n * This hook returns a `GlobalStyles` component that sets the CSS layer order (for server-side rendering).\n * Then on client-side, it injects the CSS layer order into the document head to ensure that the layer order is always present first before other Emotion styles.\n */\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default function useLayerOrder(theme) {\n  const upperTheme = useThemeWithoutDefault();\n  const id = useId() || '';\n  const {\n    modularCssLayers\n  } = theme;\n  let layerOrder = 'mui.global, mui.components, mui.theme, mui.custom, mui.sx';\n  if (!modularCssLayers || upperTheme !== null) {\n    // skip this hook if upper theme exists.\n    layerOrder = '';\n  } else if (typeof modularCssLayers === 'string') {\n    layerOrder = modularCssLayers.replace(/mui(?!\\.)/g, layerOrder);\n  } else {\n    layerOrder = `@layer ${layerOrder};`;\n  }\n  useEnhancedEffect(() => {\n    const head = document.querySelector('head');\n    if (!head) {\n      return;\n    }\n    const firstChild = head.firstChild;\n    if (layerOrder) {\n      var _firstChild$hasAttrib;\n      // Only insert if first child doesn't have data-mui-layer-order attribute\n      if (firstChild && (_firstChild$hasAttrib = firstChild.hasAttribute) != null && _firstChild$hasAttrib.call(firstChild, 'data-mui-layer-order') && firstChild.getAttribute('data-mui-layer-order') === id) {\n        return;\n      }\n      const styleElement = document.createElement('style');\n      styleElement.setAttribute('data-mui-layer-order', id);\n      styleElement.textContent = layerOrder;\n      head.prepend(styleElement);\n    } else {\n      var _head$querySelector;\n      (_head$querySelector = head.querySelector(`style[data-mui-layer-order=\"${id}\"]`)) == null || _head$querySelector.remove();\n    }\n  }, [layerOrder, id]);\n  if (!layerOrder) {\n    return null;\n  }\n  return /*#__PURE__*/_jsx(GlobalStyles, {\n    styles: layerOrder\n  });\n}", "map": {"version": 3, "names": ["React", "useEnhancedEffect", "useId", "GlobalStyles", "useThemeWithoutDefault", "jsx", "_jsx", "useLayerOrder", "theme", "upperTheme", "id", "modularCssLayers", "layerOrder", "replace", "head", "document", "querySelector", "<PERSON><PERSON><PERSON><PERSON>", "_firstChild$hasAttrib", "hasAttribute", "call", "getAttribute", "styleElement", "createElement", "setAttribute", "textContent", "prepend", "_head$querySelector", "remove", "styles"], "sources": ["/home/<USER>/PycharmProjects/ai-voice-mate-dev/ai_voice_mate/frontend/node_modules/@mui/system/esm/ThemeProvider/useLayerOrder.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport useEnhancedEffect from '@mui/utils/useEnhancedEffect';\nimport useId from '@mui/utils/useId';\nimport GlobalStyles from '../GlobalStyles';\nimport useThemeWithoutDefault from '../useThemeWithoutDefault';\n\n/**\n * This hook returns a `GlobalStyles` component that sets the CSS layer order (for server-side rendering).\n * Then on client-side, it injects the CSS layer order into the document head to ensure that the layer order is always present first before other Emotion styles.\n */\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default function useLayerOrder(theme) {\n  const upperTheme = useThemeWithoutDefault();\n  const id = useId() || '';\n  const {\n    modularCssLayers\n  } = theme;\n  let layerOrder = 'mui.global, mui.components, mui.theme, mui.custom, mui.sx';\n  if (!modularCssLayers || upperTheme !== null) {\n    // skip this hook if upper theme exists.\n    layerOrder = '';\n  } else if (typeof modularCssLayers === 'string') {\n    layerOrder = modularCssLayers.replace(/mui(?!\\.)/g, layerOrder);\n  } else {\n    layerOrder = `@layer ${layerOrder};`;\n  }\n  useEnhancedEffect(() => {\n    const head = document.querySelector('head');\n    if (!head) {\n      return;\n    }\n    const firstChild = head.firstChild;\n    if (layerOrder) {\n      var _firstChild$hasAttrib;\n      // Only insert if first child doesn't have data-mui-layer-order attribute\n      if (firstChild && (_firstChild$hasAttrib = firstChild.hasAttribute) != null && _firstChild$hasAttrib.call(firstChild, 'data-mui-layer-order') && firstChild.getAttribute('data-mui-layer-order') === id) {\n        return;\n      }\n      const styleElement = document.createElement('style');\n      styleElement.setAttribute('data-mui-layer-order', id);\n      styleElement.textContent = layerOrder;\n      head.prepend(styleElement);\n    } else {\n      var _head$querySelector;\n      (_head$querySelector = head.querySelector(`style[data-mui-layer-order=\"${id}\"]`)) == null || _head$querySelector.remove();\n    }\n  }, [layerOrder, id]);\n  if (!layerOrder) {\n    return null;\n  }\n  return /*#__PURE__*/_jsx(GlobalStyles, {\n    styles: layerOrder\n  });\n}"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,iBAAiB,MAAM,8BAA8B;AAC5D,OAAOC,KAAK,MAAM,kBAAkB;AACpC,OAAOC,YAAY,MAAM,iBAAiB;AAC1C,OAAOC,sBAAsB,MAAM,2BAA2B;;AAE9D;AACA;AACA;AACA;AACA,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,eAAe,SAASC,aAAaA,CAACC,KAAK,EAAE;EAC3C,MAAMC,UAAU,GAAGL,sBAAsB,CAAC,CAAC;EAC3C,MAAMM,EAAE,GAAGR,KAAK,CAAC,CAAC,IAAI,EAAE;EACxB,MAAM;IACJS;EACF,CAAC,GAAGH,KAAK;EACT,IAAII,UAAU,GAAG,2DAA2D;EAC5E,IAAI,CAACD,gBAAgB,IAAIF,UAAU,KAAK,IAAI,EAAE;IAC5C;IACAG,UAAU,GAAG,EAAE;EACjB,CAAC,MAAM,IAAI,OAAOD,gBAAgB,KAAK,QAAQ,EAAE;IAC/CC,UAAU,GAAGD,gBAAgB,CAACE,OAAO,CAAC,YAAY,EAAED,UAAU,CAAC;EACjE,CAAC,MAAM;IACLA,UAAU,GAAG,UAAUA,UAAU,GAAG;EACtC;EACAX,iBAAiB,CAAC,MAAM;IACtB,MAAMa,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC;IAC3C,IAAI,CAACF,IAAI,EAAE;MACT;IACF;IACA,MAAMG,UAAU,GAAGH,IAAI,CAACG,UAAU;IAClC,IAAIL,UAAU,EAAE;MACd,IAAIM,qBAAqB;MACzB;MACA,IAAID,UAAU,IAAI,CAACC,qBAAqB,GAAGD,UAAU,CAACE,YAAY,KAAK,IAAI,IAAID,qBAAqB,CAACE,IAAI,CAACH,UAAU,EAAE,sBAAsB,CAAC,IAAIA,UAAU,CAACI,YAAY,CAAC,sBAAsB,CAAC,KAAKX,EAAE,EAAE;QACvM;MACF;MACA,MAAMY,YAAY,GAAGP,QAAQ,CAACQ,aAAa,CAAC,OAAO,CAAC;MACpDD,YAAY,CAACE,YAAY,CAAC,sBAAsB,EAAEd,EAAE,CAAC;MACrDY,YAAY,CAACG,WAAW,GAAGb,UAAU;MACrCE,IAAI,CAACY,OAAO,CAACJ,YAAY,CAAC;IAC5B,CAAC,MAAM;MACL,IAAIK,mBAAmB;MACvB,CAACA,mBAAmB,GAAGb,IAAI,CAACE,aAAa,CAAC,+BAA+BN,EAAE,IAAI,CAAC,KAAK,IAAI,IAAIiB,mBAAmB,CAACC,MAAM,CAAC,CAAC;IAC3H;EACF,CAAC,EAAE,CAAChB,UAAU,EAAEF,EAAE,CAAC,CAAC;EACpB,IAAI,CAACE,UAAU,EAAE;IACf,OAAO,IAAI;EACb;EACA,OAAO,aAAaN,IAAI,CAACH,YAAY,EAAE;IACrC0B,MAAM,EAAEjB;EACV,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}