{"ast": null, "code": "'use client';\n\n// @inheritedComponent IconButton\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"className\", \"color\", \"edge\", \"size\", \"sx\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport refType from '@mui/utils/refType';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { alpha, darken, lighten } from '@mui/system/colorManipulator';\nimport capitalize from '../utils/capitalize';\nimport SwitchBase from '../internal/SwitchBase';\nimport { styled } from '../zero-styled';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport switchClasses, { getSwitchUtilityClass } from './switchClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    edge,\n    size,\n    color,\n    checked,\n    disabled\n  } = ownerState;\n  const slots = {\n    root: ['root', edge && `edge${capitalize(edge)}`, `size${capitalize(size)}`],\n    switchBase: ['switchBase', `color${capitalize(color)}`, checked && 'checked', disabled && 'disabled'],\n    thumb: ['thumb'],\n    track: ['track'],\n    input: ['input']\n  };\n  const composedClasses = composeClasses(slots, getSwitchUtilityClass, classes);\n  return _extends({}, classes, composedClasses);\n};\nconst SwitchRoot = styled('span', {\n  name: 'MuiSwitch',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, ownerState.edge && styles[`edge${capitalize(ownerState.edge)}`], styles[`size${capitalize(ownerState.size)}`]];\n  }\n})({\n  display: 'inline-flex',\n  width: 34 + 12 * 2,\n  height: 14 + 12 * 2,\n  overflow: 'hidden',\n  padding: 12,\n  boxSizing: 'border-box',\n  position: 'relative',\n  flexShrink: 0,\n  zIndex: 0,\n  // Reset the stacking context.\n  verticalAlign: 'middle',\n  // For correct alignment with the text.\n  '@media print': {\n    colorAdjust: 'exact'\n  },\n  variants: [{\n    props: {\n      edge: 'start'\n    },\n    style: {\n      marginLeft: -8\n    }\n  }, {\n    props: {\n      edge: 'end'\n    },\n    style: {\n      marginRight: -8\n    }\n  }, {\n    props: {\n      size: 'small'\n    },\n    style: {\n      width: 40,\n      height: 24,\n      padding: 7,\n      [`& .${switchClasses.thumb}`]: {\n        width: 16,\n        height: 16\n      },\n      [`& .${switchClasses.switchBase}`]: {\n        padding: 4,\n        [`&.${switchClasses.checked}`]: {\n          transform: 'translateX(16px)'\n        }\n      }\n    }\n  }]\n});\nconst SwitchSwitchBase = styled(SwitchBase, {\n  name: 'MuiSwitch',\n  slot: 'SwitchBase',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.switchBase, {\n      [`& .${switchClasses.input}`]: styles.input\n    }, ownerState.color !== 'default' && styles[`color${capitalize(ownerState.color)}`]];\n  }\n})(_ref => {\n  let {\n    theme\n  } = _ref;\n  return {\n    position: 'absolute',\n    top: 0,\n    left: 0,\n    zIndex: 1,\n    // Render above the focus ripple.\n    color: theme.vars ? theme.vars.palette.Switch.defaultColor : `${theme.palette.mode === 'light' ? theme.palette.common.white : theme.palette.grey[300]}`,\n    transition: theme.transitions.create(['left', 'transform'], {\n      duration: theme.transitions.duration.shortest\n    }),\n    [`&.${switchClasses.checked}`]: {\n      transform: 'translateX(20px)'\n    },\n    [`&.${switchClasses.disabled}`]: {\n      color: theme.vars ? theme.vars.palette.Switch.defaultDisabledColor : `${theme.palette.mode === 'light' ? theme.palette.grey[100] : theme.palette.grey[600]}`\n    },\n    [`&.${switchClasses.checked} + .${switchClasses.track}`]: {\n      opacity: 0.5\n    },\n    [`&.${switchClasses.disabled} + .${switchClasses.track}`]: {\n      opacity: theme.vars ? theme.vars.opacity.switchTrackDisabled : `${theme.palette.mode === 'light' ? 0.12 : 0.2}`\n    },\n    [`& .${switchClasses.input}`]: {\n      left: '-100%',\n      width: '300%'\n    }\n  };\n}, _ref2 => {\n  let {\n    theme\n  } = _ref2;\n  return {\n    '&:hover': {\n      backgroundColor: theme.vars ? `rgba(${theme.vars.palette.action.activeChannel} / ${theme.vars.palette.action.hoverOpacity})` : alpha(theme.palette.action.active, theme.palette.action.hoverOpacity),\n      // Reset on touch devices, it doesn't add specificity\n      '@media (hover: none)': {\n        backgroundColor: 'transparent'\n      }\n    },\n    variants: [...Object.entries(theme.palette).filter(_ref3 => {\n      let [, value] = _ref3;\n      return value.main && value.light;\n    }) // check all the used fields in the style below\n    .map(_ref4 => {\n      let [color] = _ref4;\n      return {\n        props: {\n          color\n        },\n        style: {\n          [`&.${switchClasses.checked}`]: {\n            color: (theme.vars || theme).palette[color].main,\n            '&:hover': {\n              backgroundColor: theme.vars ? `rgba(${theme.vars.palette[color].mainChannel} / ${theme.vars.palette.action.hoverOpacity})` : alpha(theme.palette[color].main, theme.palette.action.hoverOpacity),\n              '@media (hover: none)': {\n                backgroundColor: 'transparent'\n              }\n            },\n            [`&.${switchClasses.disabled}`]: {\n              color: theme.vars ? theme.vars.palette.Switch[`${color}DisabledColor`] : `${theme.palette.mode === 'light' ? lighten(theme.palette[color].main, 0.62) : darken(theme.palette[color].main, 0.55)}`\n            }\n          },\n          [`&.${switchClasses.checked} + .${switchClasses.track}`]: {\n            backgroundColor: (theme.vars || theme).palette[color].main\n          }\n        }\n      };\n    })]\n  };\n});\nconst SwitchTrack = styled('span', {\n  name: 'MuiSwitch',\n  slot: 'Track',\n  overridesResolver: (props, styles) => styles.track\n})(_ref5 => {\n  let {\n    theme\n  } = _ref5;\n  return {\n    height: '100%',\n    width: '100%',\n    borderRadius: 14 / 2,\n    zIndex: -1,\n    transition: theme.transitions.create(['opacity', 'background-color'], {\n      duration: theme.transitions.duration.shortest\n    }),\n    backgroundColor: theme.vars ? theme.vars.palette.common.onBackground : `${theme.palette.mode === 'light' ? theme.palette.common.black : theme.palette.common.white}`,\n    opacity: theme.vars ? theme.vars.opacity.switchTrack : `${theme.palette.mode === 'light' ? 0.38 : 0.3}`\n  };\n});\nconst SwitchThumb = styled('span', {\n  name: 'MuiSwitch',\n  slot: 'Thumb',\n  overridesResolver: (props, styles) => styles.thumb\n})(_ref6 => {\n  let {\n    theme\n  } = _ref6;\n  return {\n    boxShadow: (theme.vars || theme).shadows[1],\n    backgroundColor: 'currentColor',\n    width: 20,\n    height: 20,\n    borderRadius: '50%'\n  };\n});\nconst Switch = /*#__PURE__*/React.forwardRef(function Switch(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiSwitch'\n  });\n  const {\n      className,\n      color = 'primary',\n      edge = false,\n      size = 'medium',\n      sx\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = _extends({}, props, {\n    color,\n    edge,\n    size\n  });\n  const classes = useUtilityClasses(ownerState);\n  const icon = /*#__PURE__*/_jsx(SwitchThumb, {\n    className: classes.thumb,\n    ownerState: ownerState\n  });\n  return /*#__PURE__*/_jsxs(SwitchRoot, {\n    className: clsx(classes.root, className),\n    sx: sx,\n    ownerState: ownerState,\n    children: [/*#__PURE__*/_jsx(SwitchSwitchBase, _extends({\n      type: \"checkbox\",\n      icon: icon,\n      checkedIcon: icon,\n      ref: ref,\n      ownerState: ownerState\n    }, other, {\n      classes: _extends({}, classes, {\n        root: classes.switchBase\n      })\n    })), /*#__PURE__*/_jsx(SwitchTrack, {\n      className: classes.track,\n      ownerState: ownerState\n    })]\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? Switch.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * If `true`, the component is checked.\n   */\n  checked: PropTypes.bool,\n  /**\n   * The icon to display when the component is checked.\n   */\n  checkedIcon: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The color of the component.\n   * It supports both default and custom theme colors, which can be added as shown in the\n   * [palette customization guide](https://mui.com/material-ui/customization/palette/#custom-colors).\n   * @default 'primary'\n   */\n  color: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['default', 'primary', 'secondary', 'error', 'info', 'success', 'warning']), PropTypes.string]),\n  /**\n   * The default checked state. Use when the component is not controlled.\n   */\n  defaultChecked: PropTypes.bool,\n  /**\n   * If `true`, the component is disabled.\n   */\n  disabled: PropTypes.bool,\n  /**\n   * If `true`, the ripple effect is disabled.\n   * @default false\n   */\n  disableRipple: PropTypes.bool,\n  /**\n   * If given, uses a negative margin to counteract the padding on one\n   * side (this is often helpful for aligning the left or right\n   * side of the icon with content above or below, without ruining the border\n   * size and shape).\n   * @default false\n   */\n  edge: PropTypes.oneOf(['end', 'start', false]),\n  /**\n   * The icon to display when the component is unchecked.\n   */\n  icon: PropTypes.node,\n  /**\n   * The id of the `input` element.\n   */\n  id: PropTypes.string,\n  /**\n   * [Attributes](https://developer.mozilla.org/en-US/docs/Web/HTML/Element/input#Attributes) applied to the `input` element.\n   */\n  inputProps: PropTypes.object,\n  /**\n   * Pass a ref to the `input` element.\n   */\n  inputRef: refType,\n  /**\n   * Callback fired when the state is changed.\n   *\n   * @param {React.ChangeEvent<HTMLInputElement>} event The event source of the callback.\n   * You can pull out the new value by accessing `event.target.value` (string).\n   * You can pull out the new checked state by accessing `event.target.checked` (boolean).\n   */\n  onChange: PropTypes.func,\n  /**\n   * If `true`, the `input` element is required.\n   * @default false\n   */\n  required: PropTypes.bool,\n  /**\n   * The size of the component.\n   * `small` is equivalent to the dense switch styling.\n   * @default 'medium'\n   */\n  size: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['medium', 'small']), PropTypes.string]),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The value of the component. The DOM API casts this to a string.\n   * The browser uses \"on\" as the default value.\n   */\n  value: PropTypes.any\n} : void 0;\nexport default Switch;", "map": {"version": 3, "names": ["_objectWithoutPropertiesLoose", "_extends", "_excluded", "React", "PropTypes", "clsx", "refType", "composeClasses", "alpha", "darken", "lighten", "capitalize", "SwitchBase", "styled", "useDefaultProps", "switchClasses", "getSwitchUtilityClass", "jsx", "_jsx", "jsxs", "_jsxs", "useUtilityClasses", "ownerState", "classes", "edge", "size", "color", "checked", "disabled", "slots", "root", "switchBase", "thumb", "track", "input", "composedClasses", "SwitchRoot", "name", "slot", "overridesResolver", "props", "styles", "display", "width", "height", "overflow", "padding", "boxSizing", "position", "flexShrink", "zIndex", "verticalAlign", "colorAdjust", "variants", "style", "marginLeft", "marginRight", "transform", "SwitchSwitchBase", "_ref", "theme", "top", "left", "vars", "palette", "Switch", "defaultColor", "mode", "common", "white", "grey", "transition", "transitions", "create", "duration", "shortest", "defaultDisabledColor", "opacity", "switchTrackDisabled", "_ref2", "backgroundColor", "action", "activeChannel", "hoverOpacity", "active", "Object", "entries", "filter", "_ref3", "value", "main", "light", "map", "_ref4", "mainChannel", "SwitchTrack", "_ref5", "borderRadius", "onBackground", "black", "switchTrack", "SwitchThumb", "_ref6", "boxShadow", "shadows", "forwardRef", "inProps", "ref", "className", "sx", "other", "icon", "children", "type", "checkedIcon", "process", "env", "NODE_ENV", "propTypes", "bool", "node", "object", "string", "oneOfType", "oneOf", "defaultChecked", "disable<PERSON><PERSON><PERSON>", "id", "inputProps", "inputRef", "onChange", "func", "required", "arrayOf", "any"], "sources": ["/home/<USER>/PycharmProjects/ai-voice-mate-dev/ai_voice_mate/frontend/node_modules/@mui/material/Switch/Switch.js"], "sourcesContent": ["'use client';\n\n// @inheritedComponent IconButton\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"className\", \"color\", \"edge\", \"size\", \"sx\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport refType from '@mui/utils/refType';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { alpha, darken, lighten } from '@mui/system/colorManipulator';\nimport capitalize from '../utils/capitalize';\nimport SwitchBase from '../internal/SwitchBase';\nimport { styled } from '../zero-styled';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport switchClasses, { getSwitchUtilityClass } from './switchClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    edge,\n    size,\n    color,\n    checked,\n    disabled\n  } = ownerState;\n  const slots = {\n    root: ['root', edge && `edge${capitalize(edge)}`, `size${capitalize(size)}`],\n    switchBase: ['switchBase', `color${capitalize(color)}`, checked && 'checked', disabled && 'disabled'],\n    thumb: ['thumb'],\n    track: ['track'],\n    input: ['input']\n  };\n  const composedClasses = composeClasses(slots, getSwitchUtilityClass, classes);\n  return _extends({}, classes, composedClasses);\n};\nconst SwitchRoot = styled('span', {\n  name: 'MuiSwitch',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, ownerState.edge && styles[`edge${capitalize(ownerState.edge)}`], styles[`size${capitalize(ownerState.size)}`]];\n  }\n})({\n  display: 'inline-flex',\n  width: 34 + 12 * 2,\n  height: 14 + 12 * 2,\n  overflow: 'hidden',\n  padding: 12,\n  boxSizing: 'border-box',\n  position: 'relative',\n  flexShrink: 0,\n  zIndex: 0,\n  // Reset the stacking context.\n  verticalAlign: 'middle',\n  // For correct alignment with the text.\n  '@media print': {\n    colorAdjust: 'exact'\n  },\n  variants: [{\n    props: {\n      edge: 'start'\n    },\n    style: {\n      marginLeft: -8\n    }\n  }, {\n    props: {\n      edge: 'end'\n    },\n    style: {\n      marginRight: -8\n    }\n  }, {\n    props: {\n      size: 'small'\n    },\n    style: {\n      width: 40,\n      height: 24,\n      padding: 7,\n      [`& .${switchClasses.thumb}`]: {\n        width: 16,\n        height: 16\n      },\n      [`& .${switchClasses.switchBase}`]: {\n        padding: 4,\n        [`&.${switchClasses.checked}`]: {\n          transform: 'translateX(16px)'\n        }\n      }\n    }\n  }]\n});\nconst SwitchSwitchBase = styled(SwitchBase, {\n  name: 'MuiSwitch',\n  slot: 'SwitchBase',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.switchBase, {\n      [`& .${switchClasses.input}`]: styles.input\n    }, ownerState.color !== 'default' && styles[`color${capitalize(ownerState.color)}`]];\n  }\n})(({\n  theme\n}) => ({\n  position: 'absolute',\n  top: 0,\n  left: 0,\n  zIndex: 1,\n  // Render above the focus ripple.\n  color: theme.vars ? theme.vars.palette.Switch.defaultColor : `${theme.palette.mode === 'light' ? theme.palette.common.white : theme.palette.grey[300]}`,\n  transition: theme.transitions.create(['left', 'transform'], {\n    duration: theme.transitions.duration.shortest\n  }),\n  [`&.${switchClasses.checked}`]: {\n    transform: 'translateX(20px)'\n  },\n  [`&.${switchClasses.disabled}`]: {\n    color: theme.vars ? theme.vars.palette.Switch.defaultDisabledColor : `${theme.palette.mode === 'light' ? theme.palette.grey[100] : theme.palette.grey[600]}`\n  },\n  [`&.${switchClasses.checked} + .${switchClasses.track}`]: {\n    opacity: 0.5\n  },\n  [`&.${switchClasses.disabled} + .${switchClasses.track}`]: {\n    opacity: theme.vars ? theme.vars.opacity.switchTrackDisabled : `${theme.palette.mode === 'light' ? 0.12 : 0.2}`\n  },\n  [`& .${switchClasses.input}`]: {\n    left: '-100%',\n    width: '300%'\n  }\n}), ({\n  theme\n}) => ({\n  '&:hover': {\n    backgroundColor: theme.vars ? `rgba(${theme.vars.palette.action.activeChannel} / ${theme.vars.palette.action.hoverOpacity})` : alpha(theme.palette.action.active, theme.palette.action.hoverOpacity),\n    // Reset on touch devices, it doesn't add specificity\n    '@media (hover: none)': {\n      backgroundColor: 'transparent'\n    }\n  },\n  variants: [...Object.entries(theme.palette).filter(([, value]) => value.main && value.light) // check all the used fields in the style below\n  .map(([color]) => ({\n    props: {\n      color\n    },\n    style: {\n      [`&.${switchClasses.checked}`]: {\n        color: (theme.vars || theme).palette[color].main,\n        '&:hover': {\n          backgroundColor: theme.vars ? `rgba(${theme.vars.palette[color].mainChannel} / ${theme.vars.palette.action.hoverOpacity})` : alpha(theme.palette[color].main, theme.palette.action.hoverOpacity),\n          '@media (hover: none)': {\n            backgroundColor: 'transparent'\n          }\n        },\n        [`&.${switchClasses.disabled}`]: {\n          color: theme.vars ? theme.vars.palette.Switch[`${color}DisabledColor`] : `${theme.palette.mode === 'light' ? lighten(theme.palette[color].main, 0.62) : darken(theme.palette[color].main, 0.55)}`\n        }\n      },\n      [`&.${switchClasses.checked} + .${switchClasses.track}`]: {\n        backgroundColor: (theme.vars || theme).palette[color].main\n      }\n    }\n  }))]\n}));\nconst SwitchTrack = styled('span', {\n  name: 'MuiSwitch',\n  slot: 'Track',\n  overridesResolver: (props, styles) => styles.track\n})(({\n  theme\n}) => ({\n  height: '100%',\n  width: '100%',\n  borderRadius: 14 / 2,\n  zIndex: -1,\n  transition: theme.transitions.create(['opacity', 'background-color'], {\n    duration: theme.transitions.duration.shortest\n  }),\n  backgroundColor: theme.vars ? theme.vars.palette.common.onBackground : `${theme.palette.mode === 'light' ? theme.palette.common.black : theme.palette.common.white}`,\n  opacity: theme.vars ? theme.vars.opacity.switchTrack : `${theme.palette.mode === 'light' ? 0.38 : 0.3}`\n}));\nconst SwitchThumb = styled('span', {\n  name: 'MuiSwitch',\n  slot: 'Thumb',\n  overridesResolver: (props, styles) => styles.thumb\n})(({\n  theme\n}) => ({\n  boxShadow: (theme.vars || theme).shadows[1],\n  backgroundColor: 'currentColor',\n  width: 20,\n  height: 20,\n  borderRadius: '50%'\n}));\nconst Switch = /*#__PURE__*/React.forwardRef(function Switch(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiSwitch'\n  });\n  const {\n      className,\n      color = 'primary',\n      edge = false,\n      size = 'medium',\n      sx\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = _extends({}, props, {\n    color,\n    edge,\n    size\n  });\n  const classes = useUtilityClasses(ownerState);\n  const icon = /*#__PURE__*/_jsx(SwitchThumb, {\n    className: classes.thumb,\n    ownerState: ownerState\n  });\n  return /*#__PURE__*/_jsxs(SwitchRoot, {\n    className: clsx(classes.root, className),\n    sx: sx,\n    ownerState: ownerState,\n    children: [/*#__PURE__*/_jsx(SwitchSwitchBase, _extends({\n      type: \"checkbox\",\n      icon: icon,\n      checkedIcon: icon,\n      ref: ref,\n      ownerState: ownerState\n    }, other, {\n      classes: _extends({}, classes, {\n        root: classes.switchBase\n      })\n    })), /*#__PURE__*/_jsx(SwitchTrack, {\n      className: classes.track,\n      ownerState: ownerState\n    })]\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? Switch.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * If `true`, the component is checked.\n   */\n  checked: PropTypes.bool,\n  /**\n   * The icon to display when the component is checked.\n   */\n  checkedIcon: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The color of the component.\n   * It supports both default and custom theme colors, which can be added as shown in the\n   * [palette customization guide](https://mui.com/material-ui/customization/palette/#custom-colors).\n   * @default 'primary'\n   */\n  color: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['default', 'primary', 'secondary', 'error', 'info', 'success', 'warning']), PropTypes.string]),\n  /**\n   * The default checked state. Use when the component is not controlled.\n   */\n  defaultChecked: PropTypes.bool,\n  /**\n   * If `true`, the component is disabled.\n   */\n  disabled: PropTypes.bool,\n  /**\n   * If `true`, the ripple effect is disabled.\n   * @default false\n   */\n  disableRipple: PropTypes.bool,\n  /**\n   * If given, uses a negative margin to counteract the padding on one\n   * side (this is often helpful for aligning the left or right\n   * side of the icon with content above or below, without ruining the border\n   * size and shape).\n   * @default false\n   */\n  edge: PropTypes.oneOf(['end', 'start', false]),\n  /**\n   * The icon to display when the component is unchecked.\n   */\n  icon: PropTypes.node,\n  /**\n   * The id of the `input` element.\n   */\n  id: PropTypes.string,\n  /**\n   * [Attributes](https://developer.mozilla.org/en-US/docs/Web/HTML/Element/input#Attributes) applied to the `input` element.\n   */\n  inputProps: PropTypes.object,\n  /**\n   * Pass a ref to the `input` element.\n   */\n  inputRef: refType,\n  /**\n   * Callback fired when the state is changed.\n   *\n   * @param {React.ChangeEvent<HTMLInputElement>} event The event source of the callback.\n   * You can pull out the new value by accessing `event.target.value` (string).\n   * You can pull out the new checked state by accessing `event.target.checked` (boolean).\n   */\n  onChange: PropTypes.func,\n  /**\n   * If `true`, the `input` element is required.\n   * @default false\n   */\n  required: PropTypes.bool,\n  /**\n   * The size of the component.\n   * `small` is equivalent to the dense switch styling.\n   * @default 'medium'\n   */\n  size: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['medium', 'small']), PropTypes.string]),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The value of the component. The DOM API casts this to a string.\n   * The browser uses \"on\" as the default value.\n   */\n  value: PropTypes.any\n} : void 0;\nexport default Switch;"], "mappings": "AAAA,YAAY;;AAEZ;AACA,OAAOA,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,MAAMC,SAAS,GAAG,CAAC,WAAW,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,CAAC;AAC9D,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAOC,cAAc,MAAM,2BAA2B;AACtD,SAASC,KAAK,EAAEC,MAAM,EAAEC,OAAO,QAAQ,8BAA8B;AACrE,OAAOC,UAAU,MAAM,qBAAqB;AAC5C,OAAOC,UAAU,MAAM,wBAAwB;AAC/C,SAASC,MAAM,QAAQ,gBAAgB;AACvC,SAASC,eAAe,QAAQ,yBAAyB;AACzD,OAAOC,aAAa,IAAIC,qBAAqB,QAAQ,iBAAiB;AACtE,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AACjD,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC,OAAO;IACPC,IAAI;IACJC,IAAI;IACJC,KAAK;IACLC,OAAO;IACPC;EACF,CAAC,GAAGN,UAAU;EACd,MAAMO,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,EAAEN,IAAI,IAAI,OAAOb,UAAU,CAACa,IAAI,CAAC,EAAE,EAAE,OAAOb,UAAU,CAACc,IAAI,CAAC,EAAE,CAAC;IAC5EM,UAAU,EAAE,CAAC,YAAY,EAAE,QAAQpB,UAAU,CAACe,KAAK,CAAC,EAAE,EAAEC,OAAO,IAAI,SAAS,EAAEC,QAAQ,IAAI,UAAU,CAAC;IACrGI,KAAK,EAAE,CAAC,OAAO,CAAC;IAChBC,KAAK,EAAE,CAAC,OAAO,CAAC;IAChBC,KAAK,EAAE,CAAC,OAAO;EACjB,CAAC;EACD,MAAMC,eAAe,GAAG5B,cAAc,CAACsB,KAAK,EAAEb,qBAAqB,EAAEO,OAAO,CAAC;EAC7E,OAAOtB,QAAQ,CAAC,CAAC,CAAC,EAAEsB,OAAO,EAAEY,eAAe,CAAC;AAC/C,CAAC;AACD,MAAMC,UAAU,GAAGvB,MAAM,CAAC,MAAM,EAAE;EAChCwB,IAAI,EAAE,WAAW;EACjBC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJnB;IACF,CAAC,GAAGkB,KAAK;IACT,OAAO,CAACC,MAAM,CAACX,IAAI,EAAER,UAAU,CAACE,IAAI,IAAIiB,MAAM,CAAC,OAAO9B,UAAU,CAACW,UAAU,CAACE,IAAI,CAAC,EAAE,CAAC,EAAEiB,MAAM,CAAC,OAAO9B,UAAU,CAACW,UAAU,CAACG,IAAI,CAAC,EAAE,CAAC,CAAC;EACrI;AACF,CAAC,CAAC,CAAC;EACDiB,OAAO,EAAE,aAAa;EACtBC,KAAK,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC;EAClBC,MAAM,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC;EACnBC,QAAQ,EAAE,QAAQ;EAClBC,OAAO,EAAE,EAAE;EACXC,SAAS,EAAE,YAAY;EACvBC,QAAQ,EAAE,UAAU;EACpBC,UAAU,EAAE,CAAC;EACbC,MAAM,EAAE,CAAC;EACT;EACAC,aAAa,EAAE,QAAQ;EACvB;EACA,cAAc,EAAE;IACdC,WAAW,EAAE;EACf,CAAC;EACDC,QAAQ,EAAE,CAAC;IACTb,KAAK,EAAE;MACLhB,IAAI,EAAE;IACR,CAAC;IACD8B,KAAK,EAAE;MACLC,UAAU,EAAE,CAAC;IACf;EACF,CAAC,EAAE;IACDf,KAAK,EAAE;MACLhB,IAAI,EAAE;IACR,CAAC;IACD8B,KAAK,EAAE;MACLE,WAAW,EAAE,CAAC;IAChB;EACF,CAAC,EAAE;IACDhB,KAAK,EAAE;MACLf,IAAI,EAAE;IACR,CAAC;IACD6B,KAAK,EAAE;MACLX,KAAK,EAAE,EAAE;MACTC,MAAM,EAAE,EAAE;MACVE,OAAO,EAAE,CAAC;MACV,CAAC,MAAM/B,aAAa,CAACiB,KAAK,EAAE,GAAG;QAC7BW,KAAK,EAAE,EAAE;QACTC,MAAM,EAAE;MACV,CAAC;MACD,CAAC,MAAM7B,aAAa,CAACgB,UAAU,EAAE,GAAG;QAClCe,OAAO,EAAE,CAAC;QACV,CAAC,KAAK/B,aAAa,CAACY,OAAO,EAAE,GAAG;UAC9B8B,SAAS,EAAE;QACb;MACF;IACF;EACF,CAAC;AACH,CAAC,CAAC;AACF,MAAMC,gBAAgB,GAAG7C,MAAM,CAACD,UAAU,EAAE;EAC1CyB,IAAI,EAAE,WAAW;EACjBC,IAAI,EAAE,YAAY;EAClBC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJnB;IACF,CAAC,GAAGkB,KAAK;IACT,OAAO,CAACC,MAAM,CAACV,UAAU,EAAE;MACzB,CAAC,MAAMhB,aAAa,CAACmB,KAAK,EAAE,GAAGO,MAAM,CAACP;IACxC,CAAC,EAAEZ,UAAU,CAACI,KAAK,KAAK,SAAS,IAAIe,MAAM,CAAC,QAAQ9B,UAAU,CAACW,UAAU,CAACI,KAAK,CAAC,EAAE,CAAC,CAAC;EACtF;AACF,CAAC,CAAC,CAACiC,IAAA;EAAA,IAAC;IACFC;EACF,CAAC,GAAAD,IAAA;EAAA,OAAM;IACLX,QAAQ,EAAE,UAAU;IACpBa,GAAG,EAAE,CAAC;IACNC,IAAI,EAAE,CAAC;IACPZ,MAAM,EAAE,CAAC;IACT;IACAxB,KAAK,EAAEkC,KAAK,CAACG,IAAI,GAAGH,KAAK,CAACG,IAAI,CAACC,OAAO,CAACC,MAAM,CAACC,YAAY,GAAG,GAAGN,KAAK,CAACI,OAAO,CAACG,IAAI,KAAK,OAAO,GAAGP,KAAK,CAACI,OAAO,CAACI,MAAM,CAACC,KAAK,GAAGT,KAAK,CAACI,OAAO,CAACM,IAAI,CAAC,GAAG,CAAC,EAAE;IACvJC,UAAU,EAAEX,KAAK,CAACY,WAAW,CAACC,MAAM,CAAC,CAAC,MAAM,EAAE,WAAW,CAAC,EAAE;MAC1DC,QAAQ,EAAEd,KAAK,CAACY,WAAW,CAACE,QAAQ,CAACC;IACvC,CAAC,CAAC;IACF,CAAC,KAAK5D,aAAa,CAACY,OAAO,EAAE,GAAG;MAC9B8B,SAAS,EAAE;IACb,CAAC;IACD,CAAC,KAAK1C,aAAa,CAACa,QAAQ,EAAE,GAAG;MAC/BF,KAAK,EAAEkC,KAAK,CAACG,IAAI,GAAGH,KAAK,CAACG,IAAI,CAACC,OAAO,CAACC,MAAM,CAACW,oBAAoB,GAAG,GAAGhB,KAAK,CAACI,OAAO,CAACG,IAAI,KAAK,OAAO,GAAGP,KAAK,CAACI,OAAO,CAACM,IAAI,CAAC,GAAG,CAAC,GAAGV,KAAK,CAACI,OAAO,CAACM,IAAI,CAAC,GAAG,CAAC;IAC5J,CAAC;IACD,CAAC,KAAKvD,aAAa,CAACY,OAAO,OAAOZ,aAAa,CAACkB,KAAK,EAAE,GAAG;MACxD4C,OAAO,EAAE;IACX,CAAC;IACD,CAAC,KAAK9D,aAAa,CAACa,QAAQ,OAAOb,aAAa,CAACkB,KAAK,EAAE,GAAG;MACzD4C,OAAO,EAAEjB,KAAK,CAACG,IAAI,GAAGH,KAAK,CAACG,IAAI,CAACc,OAAO,CAACC,mBAAmB,GAAG,GAAGlB,KAAK,CAACI,OAAO,CAACG,IAAI,KAAK,OAAO,GAAG,IAAI,GAAG,GAAG;IAC/G,CAAC;IACD,CAAC,MAAMpD,aAAa,CAACmB,KAAK,EAAE,GAAG;MAC7B4B,IAAI,EAAE,OAAO;MACbnB,KAAK,EAAE;IACT;EACF,CAAC;AAAA,CAAC,EAAEoC,KAAA;EAAA,IAAC;IACHnB;EACF,CAAC,GAAAmB,KAAA;EAAA,OAAM;IACL,SAAS,EAAE;MACTC,eAAe,EAAEpB,KAAK,CAACG,IAAI,GAAG,QAAQH,KAAK,CAACG,IAAI,CAACC,OAAO,CAACiB,MAAM,CAACC,aAAa,MAAMtB,KAAK,CAACG,IAAI,CAACC,OAAO,CAACiB,MAAM,CAACE,YAAY,GAAG,GAAG3E,KAAK,CAACoD,KAAK,CAACI,OAAO,CAACiB,MAAM,CAACG,MAAM,EAAExB,KAAK,CAACI,OAAO,CAACiB,MAAM,CAACE,YAAY,CAAC;MACpM;MACA,sBAAsB,EAAE;QACtBH,eAAe,EAAE;MACnB;IACF,CAAC;IACD3B,QAAQ,EAAE,CAAC,GAAGgC,MAAM,CAACC,OAAO,CAAC1B,KAAK,CAACI,OAAO,CAAC,CAACuB,MAAM,CAACC,KAAA;MAAA,IAAC,GAAGC,KAAK,CAAC,GAAAD,KAAA;MAAA,OAAKC,KAAK,CAACC,IAAI,IAAID,KAAK,CAACE,KAAK;IAAA,EAAC,CAAC;IAAA,CAC5FC,GAAG,CAACC,KAAA;MAAA,IAAC,CAACnE,KAAK,CAAC,GAAAmE,KAAA;MAAA,OAAM;QACjBrD,KAAK,EAAE;UACLd;QACF,CAAC;QACD4B,KAAK,EAAE;UACL,CAAC,KAAKvC,aAAa,CAACY,OAAO,EAAE,GAAG;YAC9BD,KAAK,EAAE,CAACkC,KAAK,CAACG,IAAI,IAAIH,KAAK,EAAEI,OAAO,CAACtC,KAAK,CAAC,CAACgE,IAAI;YAChD,SAAS,EAAE;cACTV,eAAe,EAAEpB,KAAK,CAACG,IAAI,GAAG,QAAQH,KAAK,CAACG,IAAI,CAACC,OAAO,CAACtC,KAAK,CAAC,CAACoE,WAAW,MAAMlC,KAAK,CAACG,IAAI,CAACC,OAAO,CAACiB,MAAM,CAACE,YAAY,GAAG,GAAG3E,KAAK,CAACoD,KAAK,CAACI,OAAO,CAACtC,KAAK,CAAC,CAACgE,IAAI,EAAE9B,KAAK,CAACI,OAAO,CAACiB,MAAM,CAACE,YAAY,CAAC;cAChM,sBAAsB,EAAE;gBACtBH,eAAe,EAAE;cACnB;YACF,CAAC;YACD,CAAC,KAAKjE,aAAa,CAACa,QAAQ,EAAE,GAAG;cAC/BF,KAAK,EAAEkC,KAAK,CAACG,IAAI,GAAGH,KAAK,CAACG,IAAI,CAACC,OAAO,CAACC,MAAM,CAAC,GAAGvC,KAAK,eAAe,CAAC,GAAG,GAAGkC,KAAK,CAACI,OAAO,CAACG,IAAI,KAAK,OAAO,GAAGzD,OAAO,CAACkD,KAAK,CAACI,OAAO,CAACtC,KAAK,CAAC,CAACgE,IAAI,EAAE,IAAI,CAAC,GAAGjF,MAAM,CAACmD,KAAK,CAACI,OAAO,CAACtC,KAAK,CAAC,CAACgE,IAAI,EAAE,IAAI,CAAC;YACjM;UACF,CAAC;UACD,CAAC,KAAK3E,aAAa,CAACY,OAAO,OAAOZ,aAAa,CAACkB,KAAK,EAAE,GAAG;YACxD+C,eAAe,EAAE,CAACpB,KAAK,CAACG,IAAI,IAAIH,KAAK,EAAEI,OAAO,CAACtC,KAAK,CAAC,CAACgE;UACxD;QACF;MACF,CAAC;IAAA,CAAC,CAAC;EACL,CAAC;AAAA,CAAC,CAAC;AACH,MAAMK,WAAW,GAAGlF,MAAM,CAAC,MAAM,EAAE;EACjCwB,IAAI,EAAE,WAAW;EACjBC,IAAI,EAAE,OAAO;EACbC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAKA,MAAM,CAACR;AAC/C,CAAC,CAAC,CAAC+D,KAAA;EAAA,IAAC;IACFpC;EACF,CAAC,GAAAoC,KAAA;EAAA,OAAM;IACLpD,MAAM,EAAE,MAAM;IACdD,KAAK,EAAE,MAAM;IACbsD,YAAY,EAAE,EAAE,GAAG,CAAC;IACpB/C,MAAM,EAAE,CAAC,CAAC;IACVqB,UAAU,EAAEX,KAAK,CAACY,WAAW,CAACC,MAAM,CAAC,CAAC,SAAS,EAAE,kBAAkB,CAAC,EAAE;MACpEC,QAAQ,EAAEd,KAAK,CAACY,WAAW,CAACE,QAAQ,CAACC;IACvC,CAAC,CAAC;IACFK,eAAe,EAAEpB,KAAK,CAACG,IAAI,GAAGH,KAAK,CAACG,IAAI,CAACC,OAAO,CAACI,MAAM,CAAC8B,YAAY,GAAG,GAAGtC,KAAK,CAACI,OAAO,CAACG,IAAI,KAAK,OAAO,GAAGP,KAAK,CAACI,OAAO,CAACI,MAAM,CAAC+B,KAAK,GAAGvC,KAAK,CAACI,OAAO,CAACI,MAAM,CAACC,KAAK,EAAE;IACpKQ,OAAO,EAAEjB,KAAK,CAACG,IAAI,GAAGH,KAAK,CAACG,IAAI,CAACc,OAAO,CAACuB,WAAW,GAAG,GAAGxC,KAAK,CAACI,OAAO,CAACG,IAAI,KAAK,OAAO,GAAG,IAAI,GAAG,GAAG;EACvG,CAAC;AAAA,CAAC,CAAC;AACH,MAAMkC,WAAW,GAAGxF,MAAM,CAAC,MAAM,EAAE;EACjCwB,IAAI,EAAE,WAAW;EACjBC,IAAI,EAAE,OAAO;EACbC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAKA,MAAM,CAACT;AAC/C,CAAC,CAAC,CAACsE,KAAA;EAAA,IAAC;IACF1C;EACF,CAAC,GAAA0C,KAAA;EAAA,OAAM;IACLC,SAAS,EAAE,CAAC3C,KAAK,CAACG,IAAI,IAAIH,KAAK,EAAE4C,OAAO,CAAC,CAAC,CAAC;IAC3CxB,eAAe,EAAE,cAAc;IAC/BrC,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE,EAAE;IACVqD,YAAY,EAAE;EAChB,CAAC;AAAA,CAAC,CAAC;AACH,MAAMhC,MAAM,GAAG,aAAa9D,KAAK,CAACsG,UAAU,CAAC,SAASxC,MAAMA,CAACyC,OAAO,EAAEC,GAAG,EAAE;EACzE,MAAMnE,KAAK,GAAG1B,eAAe,CAAC;IAC5B0B,KAAK,EAAEkE,OAAO;IACdrE,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;MACFuE,SAAS;MACTlF,KAAK,GAAG,SAAS;MACjBF,IAAI,GAAG,KAAK;MACZC,IAAI,GAAG,QAAQ;MACfoF;IACF,CAAC,GAAGrE,KAAK;IACTsE,KAAK,GAAG9G,6BAA6B,CAACwC,KAAK,EAAEtC,SAAS,CAAC;EACzD,MAAMoB,UAAU,GAAGrB,QAAQ,CAAC,CAAC,CAAC,EAAEuC,KAAK,EAAE;IACrCd,KAAK;IACLF,IAAI;IACJC;EACF,CAAC,CAAC;EACF,MAAMF,OAAO,GAAGF,iBAAiB,CAACC,UAAU,CAAC;EAC7C,MAAMyF,IAAI,GAAG,aAAa7F,IAAI,CAACmF,WAAW,EAAE;IAC1CO,SAAS,EAAErF,OAAO,CAACS,KAAK;IACxBV,UAAU,EAAEA;EACd,CAAC,CAAC;EACF,OAAO,aAAaF,KAAK,CAACgB,UAAU,EAAE;IACpCwE,SAAS,EAAEvG,IAAI,CAACkB,OAAO,CAACO,IAAI,EAAE8E,SAAS,CAAC;IACxCC,EAAE,EAAEA,EAAE;IACNvF,UAAU,EAAEA,UAAU;IACtB0F,QAAQ,EAAE,CAAC,aAAa9F,IAAI,CAACwC,gBAAgB,EAAEzD,QAAQ,CAAC;MACtDgH,IAAI,EAAE,UAAU;MAChBF,IAAI,EAAEA,IAAI;MACVG,WAAW,EAAEH,IAAI;MACjBJ,GAAG,EAAEA,GAAG;MACRrF,UAAU,EAAEA;IACd,CAAC,EAAEwF,KAAK,EAAE;MACRvF,OAAO,EAAEtB,QAAQ,CAAC,CAAC,CAAC,EAAEsB,OAAO,EAAE;QAC7BO,IAAI,EAAEP,OAAO,CAACQ;MAChB,CAAC;IACH,CAAC,CAAC,CAAC,EAAE,aAAab,IAAI,CAAC6E,WAAW,EAAE;MAClCa,SAAS,EAAErF,OAAO,CAACU,KAAK;MACxBX,UAAU,EAAEA;IACd,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC;AACF6F,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGpD,MAAM,CAACqD,SAAS,CAAC,yBAAyB;EAChF;EACA;EACA;EACA;EACA;AACF;AACA;EACE3F,OAAO,EAAEvB,SAAS,CAACmH,IAAI;EACvB;AACF;AACA;EACEL,WAAW,EAAE9G,SAAS,CAACoH,IAAI;EAC3B;AACF;AACA;EACEjG,OAAO,EAAEnB,SAAS,CAACqH,MAAM;EACzB;AACF;AACA;EACEb,SAAS,EAAExG,SAAS,CAACsH,MAAM;EAC3B;AACF;AACA;AACA;AACA;AACA;EACEhG,KAAK,EAAEtB,SAAS,CAAC,sCAAsCuH,SAAS,CAAC,CAACvH,SAAS,CAACwH,KAAK,CAAC,CAAC,SAAS,EAAE,SAAS,EAAE,WAAW,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC,EAAExH,SAAS,CAACsH,MAAM,CAAC,CAAC;EACjL;AACF;AACA;EACEG,cAAc,EAAEzH,SAAS,CAACmH,IAAI;EAC9B;AACF;AACA;EACE3F,QAAQ,EAAExB,SAAS,CAACmH,IAAI;EACxB;AACF;AACA;AACA;EACEO,aAAa,EAAE1H,SAAS,CAACmH,IAAI;EAC7B;AACF;AACA;AACA;AACA;AACA;AACA;EACE/F,IAAI,EAAEpB,SAAS,CAACwH,KAAK,CAAC,CAAC,KAAK,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;EAC9C;AACF;AACA;EACEb,IAAI,EAAE3G,SAAS,CAACoH,IAAI;EACpB;AACF;AACA;EACEO,EAAE,EAAE3H,SAAS,CAACsH,MAAM;EACpB;AACF;AACA;EACEM,UAAU,EAAE5H,SAAS,CAACqH,MAAM;EAC5B;AACF;AACA;EACEQ,QAAQ,EAAE3H,OAAO;EACjB;AACF;AACA;AACA;AACA;AACA;AACA;EACE4H,QAAQ,EAAE9H,SAAS,CAAC+H,IAAI;EACxB;AACF;AACA;AACA;EACEC,QAAQ,EAAEhI,SAAS,CAACmH,IAAI;EACxB;AACF;AACA;AACA;AACA;EACE9F,IAAI,EAAErB,SAAS,CAAC,sCAAsCuH,SAAS,CAAC,CAACvH,SAAS,CAACwH,KAAK,CAAC,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,EAAExH,SAAS,CAACsH,MAAM,CAAC,CAAC;EACzH;AACF;AACA;EACEb,EAAE,EAAEzG,SAAS,CAACuH,SAAS,CAAC,CAACvH,SAAS,CAACiI,OAAO,CAACjI,SAAS,CAACuH,SAAS,CAAC,CAACvH,SAAS,CAAC+H,IAAI,EAAE/H,SAAS,CAACqH,MAAM,EAAErH,SAAS,CAACmH,IAAI,CAAC,CAAC,CAAC,EAAEnH,SAAS,CAAC+H,IAAI,EAAE/H,SAAS,CAACqH,MAAM,CAAC,CAAC;EACvJ;AACF;AACA;AACA;EACEhC,KAAK,EAAErF,SAAS,CAACkI;AACnB,CAAC,GAAG,KAAK,CAAC;AACV,eAAerE,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}