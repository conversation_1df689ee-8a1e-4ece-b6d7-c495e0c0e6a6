{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"BackdropProps\"],\n  _excluded2 = [\"anchor\", \"disableBackdropTransition\", \"disableDiscovery\", \"disableSwipeToOpen\", \"hideBackdrop\", \"hysteresis\", \"allowSwipeInChildren\", \"minFlingVelocity\", \"ModalProps\", \"onClose\", \"onOpen\", \"open\", \"PaperProps\", \"SwipeAreaProps\", \"swipeAreaWidth\", \"transitionDuration\", \"variant\"];\nimport * as React from 'react';\nimport * as ReactDOM from 'react-dom';\nimport PropTypes from 'prop-types';\nimport elementTypeAcceptingRef from '@mui/utils/elementTypeAcceptingRef';\nimport NoSsr from '../NoSsr';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport Drawer, { getAnchor, isHorizontal } from '../Drawer/Drawer';\nimport useForkRef from '../utils/useForkRef';\nimport ownerDocument from '../utils/ownerDocument';\nimport ownerWindow from '../utils/ownerWindow';\nimport useEventCallback from '../utils/useEventCallback';\nimport useEnhancedEffect from '../utils/useEnhancedEffect';\nimport useTheme from '../styles/useTheme';\nimport { getTransitionProps } from '../transitions/utils';\nimport SwipeArea from './SwipeArea';\n\n// This value is closed to what browsers are using internally to\n// trigger a native scroll.\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst UNCERTAINTY_THRESHOLD = 3; // px\n\n// This is the part of the drawer displayed on touch start.\nconst DRAG_STARTED_SIGNAL = 20; // px\n\n// We can only have one instance at the time claiming ownership for handling the swipe.\n// Otherwise, the UX would be confusing.\n// That's why we use a singleton here.\nlet claimedSwipeInstance = null;\n\n// Exported for test purposes.\nexport function reset() {\n  claimedSwipeInstance = null;\n}\nfunction calculateCurrentX(anchor, touches, doc) {\n  return anchor === 'right' ? doc.body.offsetWidth - touches[0].pageX : touches[0].pageX;\n}\nfunction calculateCurrentY(anchor, touches, containerWindow) {\n  return anchor === 'bottom' ? containerWindow.innerHeight - touches[0].clientY : touches[0].clientY;\n}\nfunction getMaxTranslate(horizontalSwipe, paperInstance) {\n  return horizontalSwipe ? paperInstance.clientWidth : paperInstance.clientHeight;\n}\nfunction getTranslate(currentTranslate, startLocation, open, maxTranslate) {\n  return Math.min(Math.max(open ? startLocation - currentTranslate : maxTranslate + startLocation - currentTranslate, 0), maxTranslate);\n}\n\n/**\n * @param {Element | null} element\n * @param {Element} rootNode\n */\nfunction getDomTreeShapes(element, rootNode) {\n  // Adapted from https://github.com/oliviertassinari/react-swipeable-views/blob/7666de1dba253b896911adf2790ce51467670856/packages/react-swipeable-views/src/SwipeableViews.js#L129\n  const domTreeShapes = [];\n  while (element && element !== rootNode.parentElement) {\n    const style = ownerWindow(rootNode).getComputedStyle(element);\n    if (\n    // Ignore the scroll children if the element is absolute positioned.\n    style.getPropertyValue('position') === 'absolute' ||\n    // Ignore the scroll children if the element has an overflowX hidden\n    style.getPropertyValue('overflow-x') === 'hidden') {\n      // noop\n    } else if (element.clientWidth > 0 && element.scrollWidth > element.clientWidth || element.clientHeight > 0 && element.scrollHeight > element.clientHeight) {\n      // Ignore the nodes that have no width.\n      // Keep elements with a scroll\n      domTreeShapes.push(element);\n    }\n    element = element.parentElement;\n  }\n  return domTreeShapes;\n}\n\n/**\n * @param {object} param0\n * @param {ReturnType<getDomTreeShapes>} param0.domTreeShapes\n */\nfunction computeHasNativeHandler(_ref) {\n  let {\n    domTreeShapes,\n    start,\n    current,\n    anchor\n  } = _ref;\n  // Adapted from https://github.com/oliviertassinari/react-swipeable-views/blob/7666de1dba253b896911adf2790ce51467670856/packages/react-swipeable-views/src/SwipeableViews.js#L175\n  const axisProperties = {\n    scrollPosition: {\n      x: 'scrollLeft',\n      y: 'scrollTop'\n    },\n    scrollLength: {\n      x: 'scrollWidth',\n      y: 'scrollHeight'\n    },\n    clientLength: {\n      x: 'clientWidth',\n      y: 'clientHeight'\n    }\n  };\n  return domTreeShapes.some(shape => {\n    // Determine if we are going backward or forward.\n    let goingForward = current >= start;\n    if (anchor === 'top' || anchor === 'left') {\n      goingForward = !goingForward;\n    }\n    const axis = anchor === 'left' || anchor === 'right' ? 'x' : 'y';\n    const scrollPosition = Math.round(shape[axisProperties.scrollPosition[axis]]);\n    const areNotAtStart = scrollPosition > 0;\n    const areNotAtEnd = scrollPosition + shape[axisProperties.clientLength[axis]] < shape[axisProperties.scrollLength[axis]];\n    if (goingForward && areNotAtEnd || !goingForward && areNotAtStart) {\n      return true;\n    }\n    return false;\n  });\n}\nconst iOS = typeof navigator !== 'undefined' && /iPad|iPhone|iPod/.test(navigator.userAgent);\nconst SwipeableDrawer = /*#__PURE__*/React.forwardRef(function SwipeableDrawer(inProps, ref) {\n  const props = useDefaultProps({\n    name: 'MuiSwipeableDrawer',\n    props: inProps\n  });\n  const theme = useTheme();\n  const transitionDurationDefault = {\n    enter: theme.transitions.duration.enteringScreen,\n    exit: theme.transitions.duration.leavingScreen\n  };\n  const {\n      anchor = 'left',\n      disableBackdropTransition = false,\n      disableDiscovery = false,\n      disableSwipeToOpen = iOS,\n      hideBackdrop,\n      hysteresis = 0.52,\n      allowSwipeInChildren = false,\n      minFlingVelocity = 450,\n      ModalProps: {\n        BackdropProps\n      } = {},\n      onClose,\n      onOpen,\n      open = false,\n      PaperProps = {},\n      SwipeAreaProps,\n      swipeAreaWidth = 20,\n      transitionDuration = transitionDurationDefault,\n      variant = 'temporary' // Mobile first.\n    } = props,\n    ModalPropsProp = _objectWithoutPropertiesLoose(props.ModalProps, _excluded),\n    other = _objectWithoutPropertiesLoose(props, _excluded2);\n  const [maybeSwiping, setMaybeSwiping] = React.useState(false);\n  const swipeInstance = React.useRef({\n    isSwiping: null\n  });\n  const swipeAreaRef = React.useRef();\n  const backdropRef = React.useRef();\n  const paperRef = React.useRef();\n  const handleRef = useForkRef(PaperProps.ref, paperRef);\n  const touchDetected = React.useRef(false);\n\n  // Ref for transition duration based on / to match swipe speed\n  const calculatedDurationRef = React.useRef();\n\n  // Use a ref so the open value used is always up to date inside useCallback.\n  useEnhancedEffect(() => {\n    calculatedDurationRef.current = null;\n  }, [open]);\n  const setPosition = React.useCallback(function (translate) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    const {\n      mode = null,\n      changeTransition = true\n    } = options;\n    const anchorRtl = getAnchor(theme, anchor);\n    const rtlTranslateMultiplier = ['right', 'bottom'].indexOf(anchorRtl) !== -1 ? 1 : -1;\n    const horizontalSwipe = isHorizontal(anchor);\n    const transform = horizontalSwipe ? `translate(${rtlTranslateMultiplier * translate}px, 0)` : `translate(0, ${rtlTranslateMultiplier * translate}px)`;\n    const drawerStyle = paperRef.current.style;\n    drawerStyle.webkitTransform = transform;\n    drawerStyle.transform = transform;\n    let transition = '';\n    if (mode) {\n      transition = theme.transitions.create('all', getTransitionProps({\n        easing: undefined,\n        style: undefined,\n        timeout: transitionDuration\n      }, {\n        mode\n      }));\n    }\n    if (changeTransition) {\n      drawerStyle.webkitTransition = transition;\n      drawerStyle.transition = transition;\n    }\n    if (!disableBackdropTransition && !hideBackdrop) {\n      const backdropStyle = backdropRef.current.style;\n      backdropStyle.opacity = 1 - translate / getMaxTranslate(horizontalSwipe, paperRef.current);\n      if (changeTransition) {\n        backdropStyle.webkitTransition = transition;\n        backdropStyle.transition = transition;\n      }\n    }\n  }, [anchor, disableBackdropTransition, hideBackdrop, theme, transitionDuration]);\n  const handleBodyTouchEnd = useEventCallback(nativeEvent => {\n    if (!touchDetected.current) {\n      return;\n    }\n    claimedSwipeInstance = null;\n    touchDetected.current = false;\n    ReactDOM.flushSync(() => {\n      setMaybeSwiping(false);\n    });\n\n    // The swipe wasn't started.\n    if (!swipeInstance.current.isSwiping) {\n      swipeInstance.current.isSwiping = null;\n      return;\n    }\n    swipeInstance.current.isSwiping = null;\n    const anchorRtl = getAnchor(theme, anchor);\n    const horizontal = isHorizontal(anchor);\n    let current;\n    if (horizontal) {\n      current = calculateCurrentX(anchorRtl, nativeEvent.changedTouches, ownerDocument(nativeEvent.currentTarget));\n    } else {\n      current = calculateCurrentY(anchorRtl, nativeEvent.changedTouches, ownerWindow(nativeEvent.currentTarget));\n    }\n    const startLocation = horizontal ? swipeInstance.current.startX : swipeInstance.current.startY;\n    const maxTranslate = getMaxTranslate(horizontal, paperRef.current);\n    const currentTranslate = getTranslate(current, startLocation, open, maxTranslate);\n    const translateRatio = currentTranslate / maxTranslate;\n    if (Math.abs(swipeInstance.current.velocity) > minFlingVelocity) {\n      // Calculate transition duration to match swipe speed\n      calculatedDurationRef.current = Math.abs((maxTranslate - currentTranslate) / swipeInstance.current.velocity) * 1000;\n    }\n    if (open) {\n      if (swipeInstance.current.velocity > minFlingVelocity || translateRatio > hysteresis) {\n        onClose();\n      } else {\n        // Reset the position, the swipe was aborted.\n        setPosition(0, {\n          mode: 'exit'\n        });\n      }\n      return;\n    }\n    if (swipeInstance.current.velocity < -minFlingVelocity || 1 - translateRatio > hysteresis) {\n      onOpen();\n    } else {\n      // Reset the position, the swipe was aborted.\n      setPosition(getMaxTranslate(horizontal, paperRef.current), {\n        mode: 'enter'\n      });\n    }\n  });\n  const startMaybeSwiping = function () {\n    let force = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;\n    if (!maybeSwiping) {\n      // on Safari Mobile, if you want to be able to have the 'click' event fired on child elements, nothing in the DOM can be changed.\n      // this is because Safari Mobile will not fire any mouse events (still fires touch though) if the DOM changes during mousemove.\n      // so do this change on first touchmove instead of touchstart\n      if (force || !(disableDiscovery && allowSwipeInChildren)) {\n        ReactDOM.flushSync(() => {\n          setMaybeSwiping(true);\n        });\n      }\n      const horizontalSwipe = isHorizontal(anchor);\n      if (!open && paperRef.current) {\n        // The ref may be null when a parent component updates while swiping.\n        setPosition(getMaxTranslate(horizontalSwipe, paperRef.current) + (disableDiscovery ? 15 : -DRAG_STARTED_SIGNAL), {\n          changeTransition: false\n        });\n      }\n      swipeInstance.current.velocity = 0;\n      swipeInstance.current.lastTime = null;\n      swipeInstance.current.lastTranslate = null;\n      swipeInstance.current.paperHit = false;\n      touchDetected.current = true;\n    }\n  };\n  const handleBodyTouchMove = useEventCallback(nativeEvent => {\n    // the ref may be null when a parent component updates while swiping\n    if (!paperRef.current || !touchDetected.current) {\n      return;\n    }\n\n    // We are not supposed to handle this touch move because the swipe was started in a scrollable container in the drawer\n    if (claimedSwipeInstance !== null && claimedSwipeInstance !== swipeInstance.current) {\n      return;\n    }\n    startMaybeSwiping(true);\n    const anchorRtl = getAnchor(theme, anchor);\n    const horizontalSwipe = isHorizontal(anchor);\n    const currentX = calculateCurrentX(anchorRtl, nativeEvent.touches, ownerDocument(nativeEvent.currentTarget));\n    const currentY = calculateCurrentY(anchorRtl, nativeEvent.touches, ownerWindow(nativeEvent.currentTarget));\n    if (open && paperRef.current.contains(nativeEvent.target) && claimedSwipeInstance === null) {\n      const domTreeShapes = getDomTreeShapes(nativeEvent.target, paperRef.current);\n      const hasNativeHandler = computeHasNativeHandler({\n        domTreeShapes,\n        start: horizontalSwipe ? swipeInstance.current.startX : swipeInstance.current.startY,\n        current: horizontalSwipe ? currentX : currentY,\n        anchor\n      });\n      if (hasNativeHandler) {\n        claimedSwipeInstance = true;\n        return;\n      }\n      claimedSwipeInstance = swipeInstance.current;\n    }\n\n    // We don't know yet.\n    if (swipeInstance.current.isSwiping == null) {\n      const dx = Math.abs(currentX - swipeInstance.current.startX);\n      const dy = Math.abs(currentY - swipeInstance.current.startY);\n      const definitelySwiping = horizontalSwipe ? dx > dy && dx > UNCERTAINTY_THRESHOLD : dy > dx && dy > UNCERTAINTY_THRESHOLD;\n      if (definitelySwiping && nativeEvent.cancelable) {\n        nativeEvent.preventDefault();\n      }\n      if (definitelySwiping === true || (horizontalSwipe ? dy > UNCERTAINTY_THRESHOLD : dx > UNCERTAINTY_THRESHOLD)) {\n        swipeInstance.current.isSwiping = definitelySwiping;\n        if (!definitelySwiping) {\n          handleBodyTouchEnd(nativeEvent);\n          return;\n        }\n\n        // Shift the starting point.\n        swipeInstance.current.startX = currentX;\n        swipeInstance.current.startY = currentY;\n\n        // Compensate for the part of the drawer displayed on touch start.\n        if (!disableDiscovery && !open) {\n          if (horizontalSwipe) {\n            swipeInstance.current.startX -= DRAG_STARTED_SIGNAL;\n          } else {\n            swipeInstance.current.startY -= DRAG_STARTED_SIGNAL;\n          }\n        }\n      }\n    }\n    if (!swipeInstance.current.isSwiping) {\n      return;\n    }\n    const maxTranslate = getMaxTranslate(horizontalSwipe, paperRef.current);\n    let startLocation = horizontalSwipe ? swipeInstance.current.startX : swipeInstance.current.startY;\n    if (open && !swipeInstance.current.paperHit) {\n      startLocation = Math.min(startLocation, maxTranslate);\n    }\n    const translate = getTranslate(horizontalSwipe ? currentX : currentY, startLocation, open, maxTranslate);\n    if (open) {\n      if (!swipeInstance.current.paperHit) {\n        const paperHit = horizontalSwipe ? currentX < maxTranslate : currentY < maxTranslate;\n        if (paperHit) {\n          swipeInstance.current.paperHit = true;\n          swipeInstance.current.startX = currentX;\n          swipeInstance.current.startY = currentY;\n        } else {\n          return;\n        }\n      } else if (translate === 0) {\n        swipeInstance.current.startX = currentX;\n        swipeInstance.current.startY = currentY;\n      }\n    }\n    if (swipeInstance.current.lastTranslate === null) {\n      swipeInstance.current.lastTranslate = translate;\n      swipeInstance.current.lastTime = performance.now() + 1;\n    }\n    const velocity = (translate - swipeInstance.current.lastTranslate) / (performance.now() - swipeInstance.current.lastTime) * 1e3;\n\n    // Low Pass filter.\n    swipeInstance.current.velocity = swipeInstance.current.velocity * 0.4 + velocity * 0.6;\n    swipeInstance.current.lastTranslate = translate;\n    swipeInstance.current.lastTime = performance.now();\n\n    // We are swiping, let's prevent the scroll event on iOS.\n    if (nativeEvent.cancelable) {\n      nativeEvent.preventDefault();\n    }\n    setPosition(translate);\n  });\n  const handleBodyTouchStart = useEventCallback(nativeEvent => {\n    // We are not supposed to handle this touch move.\n    // Example of use case: ignore the event if there is a Slider.\n    if (nativeEvent.defaultPrevented) {\n      return;\n    }\n\n    // We can only have one node at the time claiming ownership for handling the swipe.\n    if (nativeEvent.defaultMuiPrevented) {\n      return;\n    }\n\n    // At least one element clogs the drawer interaction zone.\n    if (open && (hideBackdrop || !backdropRef.current.contains(nativeEvent.target)) && !paperRef.current.contains(nativeEvent.target)) {\n      return;\n    }\n    const anchorRtl = getAnchor(theme, anchor);\n    const horizontalSwipe = isHorizontal(anchor);\n    const currentX = calculateCurrentX(anchorRtl, nativeEvent.touches, ownerDocument(nativeEvent.currentTarget));\n    const currentY = calculateCurrentY(anchorRtl, nativeEvent.touches, ownerWindow(nativeEvent.currentTarget));\n    if (!open) {\n      var _paperRef$current;\n      // logic for if swipe should be ignored:\n      // if disableSwipeToOpen\n      // if target != swipeArea, and target is not a child of paper ref\n      // if is a child of paper ref, and `allowSwipeInChildren` does not allow it\n      if (disableSwipeToOpen || !(nativeEvent.target === swipeAreaRef.current || (_paperRef$current = paperRef.current) != null && _paperRef$current.contains(nativeEvent.target) && (typeof allowSwipeInChildren === 'function' ? allowSwipeInChildren(nativeEvent, swipeAreaRef.current, paperRef.current) : allowSwipeInChildren))) {\n        return;\n      }\n      if (horizontalSwipe) {\n        if (currentX > swipeAreaWidth) {\n          return;\n        }\n      } else if (currentY > swipeAreaWidth) {\n        return;\n      }\n    }\n    nativeEvent.defaultMuiPrevented = true;\n    claimedSwipeInstance = null;\n    swipeInstance.current.startX = currentX;\n    swipeInstance.current.startY = currentY;\n    startMaybeSwiping();\n  });\n  React.useEffect(() => {\n    if (variant === 'temporary') {\n      const doc = ownerDocument(paperRef.current);\n      doc.addEventListener('touchstart', handleBodyTouchStart);\n      // A blocking listener prevents Firefox's navbar to auto-hide on scroll.\n      // It only needs to prevent scrolling on the drawer's content when open.\n      // When closed, the overlay prevents scrolling.\n      doc.addEventListener('touchmove', handleBodyTouchMove, {\n        passive: !open\n      });\n      doc.addEventListener('touchend', handleBodyTouchEnd);\n      return () => {\n        doc.removeEventListener('touchstart', handleBodyTouchStart);\n        doc.removeEventListener('touchmove', handleBodyTouchMove, {\n          passive: !open\n        });\n        doc.removeEventListener('touchend', handleBodyTouchEnd);\n      };\n    }\n    return undefined;\n  }, [variant, open, handleBodyTouchStart, handleBodyTouchMove, handleBodyTouchEnd]);\n  React.useEffect(() => () => {\n    // We need to release the lock.\n    if (claimedSwipeInstance === swipeInstance.current) {\n      claimedSwipeInstance = null;\n    }\n  }, []);\n  React.useEffect(() => {\n    if (!open) {\n      setMaybeSwiping(false);\n    }\n  }, [open]);\n  return /*#__PURE__*/_jsxs(React.Fragment, {\n    children: [/*#__PURE__*/_jsx(Drawer, _extends({\n      open: variant === 'temporary' && maybeSwiping ? true : open,\n      variant: variant,\n      ModalProps: _extends({\n        BackdropProps: _extends({}, BackdropProps, {\n          ref: backdropRef\n        })\n      }, variant === 'temporary' && {\n        keepMounted: true\n      }, ModalPropsProp),\n      hideBackdrop: hideBackdrop,\n      PaperProps: _extends({}, PaperProps, {\n        style: _extends({\n          pointerEvents: variant === 'temporary' && !open && !allowSwipeInChildren ? 'none' : ''\n        }, PaperProps.style),\n        ref: handleRef\n      }),\n      anchor: anchor,\n      transitionDuration: calculatedDurationRef.current || transitionDuration,\n      onClose: onClose,\n      ref: ref\n    }, other)), !disableSwipeToOpen && variant === 'temporary' && /*#__PURE__*/_jsx(NoSsr, {\n      children: /*#__PURE__*/_jsx(SwipeArea, _extends({\n        anchor: anchor,\n        ref: swipeAreaRef,\n        width: swipeAreaWidth\n      }, SwipeAreaProps))\n    })]\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? SwipeableDrawer.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * If set to true, the swipe event will open the drawer even if the user begins the swipe on one of the drawer's children.\n   * This can be useful in scenarios where the drawer is partially visible.\n   * You can customize it further with a callback that determines which children the user can drag over to open the drawer\n   * (for example, to ignore other elements that handle touch move events, like sliders).\n   *\n   * @param {TouchEvent} event The 'touchstart' event\n   * @param {HTMLDivElement} swipeArea The swipe area element\n   * @param {HTMLDivElement} paper The drawer's paper element\n   *\n   * @default false\n   */\n  allowSwipeInChildren: PropTypes.oneOfType([PropTypes.func, PropTypes.bool]),\n  /**\n   * @ignore\n   */\n  anchor: PropTypes.oneOf(['bottom', 'left', 'right', 'top']),\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Disable the backdrop transition.\n   * This can improve the FPS on low-end devices.\n   * @default false\n   */\n  disableBackdropTransition: PropTypes.bool,\n  /**\n   * If `true`, touching the screen near the edge of the drawer will not slide in the drawer a bit\n   * to promote accidental discovery of the swipe gesture.\n   * @default false\n   */\n  disableDiscovery: PropTypes.bool,\n  /**\n   * If `true`, swipe to open is disabled. This is useful in browsers where swiping triggers\n   * navigation actions. Swipe to open is disabled on iOS browsers by default.\n   * @default typeof navigator !== 'undefined' && /iPad|iPhone|iPod/.test(navigator.userAgent)\n   */\n  disableSwipeToOpen: PropTypes.bool,\n  /**\n   * @ignore\n   */\n  hideBackdrop: PropTypes.bool,\n  /**\n   * Affects how far the drawer must be opened/closed to change its state.\n   * Specified as percent (0-1) of the width of the drawer\n   * @default 0.52\n   */\n  hysteresis: PropTypes.number,\n  /**\n   * Defines, from which (average) velocity on, the swipe is\n   * defined as complete although hysteresis isn't reached.\n   * Good threshold is between 250 - 1000 px/s\n   * @default 450\n   */\n  minFlingVelocity: PropTypes.number,\n  /**\n   * @ignore\n   */\n  ModalProps: PropTypes /* @typescript-to-proptypes-ignore */.shape({\n    BackdropProps: PropTypes.shape({\n      component: elementTypeAcceptingRef\n    })\n  }),\n  /**\n   * Callback fired when the component requests to be closed.\n   *\n   * @param {React.SyntheticEvent<{}>} event The event source of the callback.\n   */\n  onClose: PropTypes.func.isRequired,\n  /**\n   * Callback fired when the component requests to be opened.\n   *\n   * @param {React.SyntheticEvent<{}>} event The event source of the callback.\n   */\n  onOpen: PropTypes.func.isRequired,\n  /**\n   * If `true`, the component is shown.\n   * @default false\n   */\n  open: PropTypes.bool,\n  /**\n   * @ignore\n   */\n  PaperProps: PropTypes /* @typescript-to-proptypes-ignore */.shape({\n    component: elementTypeAcceptingRef,\n    style: PropTypes.object\n  }),\n  /**\n   * The element is used to intercept the touch events on the edge.\n   */\n  SwipeAreaProps: PropTypes.object,\n  /**\n   * The width of the left most (or right most) area in `px` that\n   * the drawer can be swiped open from.\n   * @default 20\n   */\n  swipeAreaWidth: PropTypes.number,\n  /**\n   * The duration for the transition, in milliseconds.\n   * You may specify a single timeout for all transitions, or individually with an object.\n   * @default {\n   *   enter: theme.transitions.duration.enteringScreen,\n   *   exit: theme.transitions.duration.leavingScreen,\n   * }\n   */\n  transitionDuration: PropTypes.oneOfType([PropTypes.number, PropTypes.shape({\n    appear: PropTypes.number,\n    enter: PropTypes.number,\n    exit: PropTypes.number\n  })]),\n  /**\n   * @ignore\n   */\n  variant: PropTypes.oneOf(['permanent', 'persistent', 'temporary'])\n} : void 0;\nexport default SwipeableDrawer;", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "_excluded2", "React", "ReactDOM", "PropTypes", "elementTypeAcceptingRef", "NoSsr", "useDefaultProps", "Drawer", "getAnchor", "isHorizontal", "useForkRef", "ownerDocument", "ownerWindow", "useEventCallback", "useEnhancedEffect", "useTheme", "getTransitionProps", "SwipeArea", "jsx", "_jsx", "jsxs", "_jsxs", "UNCERTAINTY_THRESHOLD", "DRAG_STARTED_SIGNAL", "claimedSwipeInstance", "reset", "calculateCurrentX", "anchor", "touches", "doc", "body", "offsetWidth", "pageX", "calculateCurrentY", "containerWindow", "innerHeight", "clientY", "getMaxTranslate", "horizontalSwipe", "paperInstance", "clientWidth", "clientHeight", "getTranslate", "currentTranslate", "startLocation", "open", "maxTranslate", "Math", "min", "max", "getDomTreeShapes", "element", "rootNode", "domTreeShapes", "parentElement", "style", "getComputedStyle", "getPropertyValue", "scrollWidth", "scrollHeight", "push", "computeHasNativeHandler", "_ref", "start", "current", "axisProperties", "scrollPosition", "x", "y", "<PERSON><PERSON><PERSON><PERSON>", "clientLength", "some", "shape", "goingForward", "axis", "round", "areNotAtStart", "areNotAtEnd", "iOS", "navigator", "test", "userAgent", "SwipeableDrawer", "forwardRef", "inProps", "ref", "props", "name", "theme", "transitionDurationDefault", "enter", "transitions", "duration", "enteringScreen", "exit", "leavingScreen", "disableBackdropTransition", "disableDiscovery", "disableSwipeToOpen", "hideBackdrop", "hysteresis", "allowSwipeInChildren", "minFlingVelocity", "ModalProps", "BackdropProps", "onClose", "onOpen", "PaperProps", "SwipeAreaProps", "swipe<PERSON><PERSON><PERSON><PERSON><PERSON>", "transitionDuration", "variant", "ModalPropsProp", "other", "maybeSwiping", "setMaybeSwiping", "useState", "swipeInstance", "useRef", "isSwiping", "swipeAreaRef", "backdropRef", "paperRef", "handleRef", "touchDetected", "calculatedDurationRef", "setPosition", "useCallback", "translate", "options", "arguments", "length", "undefined", "mode", "changeTransition", "anchorRtl", "rtlTranslateMultiplier", "indexOf", "transform", "drawerStyle", "webkitTransform", "transition", "create", "easing", "timeout", "webkitTransition", "backdropStyle", "opacity", "handleBodyTouchEnd", "nativeEvent", "flushSync", "horizontal", "changedTouches", "currentTarget", "startX", "startY", "translateRatio", "abs", "velocity", "startMaybeSwiping", "force", "lastTime", "lastTranslate", "paperHit", "handleBodyTouchMove", "currentX", "currentY", "contains", "target", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dx", "dy", "definitelySwiping", "cancelable", "preventDefault", "performance", "now", "handleBodyTouchStart", "defaultPrevented", "defaultMuiPrevented", "_paperRef$current", "useEffect", "addEventListener", "passive", "removeEventListener", "Fragment", "children", "keepMounted", "pointerEvents", "width", "process", "env", "NODE_ENV", "propTypes", "oneOfType", "func", "bool", "oneOf", "node", "number", "component", "isRequired", "object", "appear"], "sources": ["/home/<USER>/PycharmProjects/ai-voice-mate-dev/ai_voice_mate/frontend/node_modules/@mui/material/SwipeableDrawer/SwipeableDrawer.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"BackdropProps\"],\n  _excluded2 = [\"anchor\", \"disableBackdropTransition\", \"disableDiscovery\", \"disableSwipeToOpen\", \"hideBackdrop\", \"hysteresis\", \"allowSwipeInChildren\", \"minFlingVelocity\", \"ModalProps\", \"onClose\", \"onOpen\", \"open\", \"PaperProps\", \"SwipeAreaProps\", \"swipeAreaWidth\", \"transitionDuration\", \"variant\"];\nimport * as React from 'react';\nimport * as ReactDOM from 'react-dom';\nimport PropTypes from 'prop-types';\nimport elementTypeAcceptingRef from '@mui/utils/elementTypeAcceptingRef';\nimport NoSsr from '../NoSsr';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport Drawer, { getAnchor, isHorizontal } from '../Drawer/Drawer';\nimport useForkRef from '../utils/useForkRef';\nimport ownerDocument from '../utils/ownerDocument';\nimport ownerWindow from '../utils/ownerWindow';\nimport useEventCallback from '../utils/useEventCallback';\nimport useEnhancedEffect from '../utils/useEnhancedEffect';\nimport useTheme from '../styles/useTheme';\nimport { getTransitionProps } from '../transitions/utils';\nimport SwipeArea from './SwipeArea';\n\n// This value is closed to what browsers are using internally to\n// trigger a native scroll.\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst UNCERTAINTY_THRESHOLD = 3; // px\n\n// This is the part of the drawer displayed on touch start.\nconst DRAG_STARTED_SIGNAL = 20; // px\n\n// We can only have one instance at the time claiming ownership for handling the swipe.\n// Otherwise, the UX would be confusing.\n// That's why we use a singleton here.\nlet claimedSwipeInstance = null;\n\n// Exported for test purposes.\nexport function reset() {\n  claimedSwipeInstance = null;\n}\nfunction calculateCurrentX(anchor, touches, doc) {\n  return anchor === 'right' ? doc.body.offsetWidth - touches[0].pageX : touches[0].pageX;\n}\nfunction calculateCurrentY(anchor, touches, containerWindow) {\n  return anchor === 'bottom' ? containerWindow.innerHeight - touches[0].clientY : touches[0].clientY;\n}\nfunction getMaxTranslate(horizontalSwipe, paperInstance) {\n  return horizontalSwipe ? paperInstance.clientWidth : paperInstance.clientHeight;\n}\nfunction getTranslate(currentTranslate, startLocation, open, maxTranslate) {\n  return Math.min(Math.max(open ? startLocation - currentTranslate : maxTranslate + startLocation - currentTranslate, 0), maxTranslate);\n}\n\n/**\n * @param {Element | null} element\n * @param {Element} rootNode\n */\nfunction getDomTreeShapes(element, rootNode) {\n  // Adapted from https://github.com/oliviertassinari/react-swipeable-views/blob/7666de1dba253b896911adf2790ce51467670856/packages/react-swipeable-views/src/SwipeableViews.js#L129\n  const domTreeShapes = [];\n  while (element && element !== rootNode.parentElement) {\n    const style = ownerWindow(rootNode).getComputedStyle(element);\n    if (\n    // Ignore the scroll children if the element is absolute positioned.\n    style.getPropertyValue('position') === 'absolute' ||\n    // Ignore the scroll children if the element has an overflowX hidden\n    style.getPropertyValue('overflow-x') === 'hidden') {\n      // noop\n    } else if (element.clientWidth > 0 && element.scrollWidth > element.clientWidth || element.clientHeight > 0 && element.scrollHeight > element.clientHeight) {\n      // Ignore the nodes that have no width.\n      // Keep elements with a scroll\n      domTreeShapes.push(element);\n    }\n    element = element.parentElement;\n  }\n  return domTreeShapes;\n}\n\n/**\n * @param {object} param0\n * @param {ReturnType<getDomTreeShapes>} param0.domTreeShapes\n */\nfunction computeHasNativeHandler({\n  domTreeShapes,\n  start,\n  current,\n  anchor\n}) {\n  // Adapted from https://github.com/oliviertassinari/react-swipeable-views/blob/7666de1dba253b896911adf2790ce51467670856/packages/react-swipeable-views/src/SwipeableViews.js#L175\n  const axisProperties = {\n    scrollPosition: {\n      x: 'scrollLeft',\n      y: 'scrollTop'\n    },\n    scrollLength: {\n      x: 'scrollWidth',\n      y: 'scrollHeight'\n    },\n    clientLength: {\n      x: 'clientWidth',\n      y: 'clientHeight'\n    }\n  };\n  return domTreeShapes.some(shape => {\n    // Determine if we are going backward or forward.\n    let goingForward = current >= start;\n    if (anchor === 'top' || anchor === 'left') {\n      goingForward = !goingForward;\n    }\n    const axis = anchor === 'left' || anchor === 'right' ? 'x' : 'y';\n    const scrollPosition = Math.round(shape[axisProperties.scrollPosition[axis]]);\n    const areNotAtStart = scrollPosition > 0;\n    const areNotAtEnd = scrollPosition + shape[axisProperties.clientLength[axis]] < shape[axisProperties.scrollLength[axis]];\n    if (goingForward && areNotAtEnd || !goingForward && areNotAtStart) {\n      return true;\n    }\n    return false;\n  });\n}\nconst iOS = typeof navigator !== 'undefined' && /iPad|iPhone|iPod/.test(navigator.userAgent);\nconst SwipeableDrawer = /*#__PURE__*/React.forwardRef(function SwipeableDrawer(inProps, ref) {\n  const props = useDefaultProps({\n    name: 'MuiSwipeableDrawer',\n    props: inProps\n  });\n  const theme = useTheme();\n  const transitionDurationDefault = {\n    enter: theme.transitions.duration.enteringScreen,\n    exit: theme.transitions.duration.leavingScreen\n  };\n  const {\n      anchor = 'left',\n      disableBackdropTransition = false,\n      disableDiscovery = false,\n      disableSwipeToOpen = iOS,\n      hideBackdrop,\n      hysteresis = 0.52,\n      allowSwipeInChildren = false,\n      minFlingVelocity = 450,\n      ModalProps: {\n        BackdropProps\n      } = {},\n      onClose,\n      onOpen,\n      open = false,\n      PaperProps = {},\n      SwipeAreaProps,\n      swipeAreaWidth = 20,\n      transitionDuration = transitionDurationDefault,\n      variant = 'temporary' // Mobile first.\n    } = props,\n    ModalPropsProp = _objectWithoutPropertiesLoose(props.ModalProps, _excluded),\n    other = _objectWithoutPropertiesLoose(props, _excluded2);\n  const [maybeSwiping, setMaybeSwiping] = React.useState(false);\n  const swipeInstance = React.useRef({\n    isSwiping: null\n  });\n  const swipeAreaRef = React.useRef();\n  const backdropRef = React.useRef();\n  const paperRef = React.useRef();\n  const handleRef = useForkRef(PaperProps.ref, paperRef);\n  const touchDetected = React.useRef(false);\n\n  // Ref for transition duration based on / to match swipe speed\n  const calculatedDurationRef = React.useRef();\n\n  // Use a ref so the open value used is always up to date inside useCallback.\n  useEnhancedEffect(() => {\n    calculatedDurationRef.current = null;\n  }, [open]);\n  const setPosition = React.useCallback((translate, options = {}) => {\n    const {\n      mode = null,\n      changeTransition = true\n    } = options;\n    const anchorRtl = getAnchor(theme, anchor);\n    const rtlTranslateMultiplier = ['right', 'bottom'].indexOf(anchorRtl) !== -1 ? 1 : -1;\n    const horizontalSwipe = isHorizontal(anchor);\n    const transform = horizontalSwipe ? `translate(${rtlTranslateMultiplier * translate}px, 0)` : `translate(0, ${rtlTranslateMultiplier * translate}px)`;\n    const drawerStyle = paperRef.current.style;\n    drawerStyle.webkitTransform = transform;\n    drawerStyle.transform = transform;\n    let transition = '';\n    if (mode) {\n      transition = theme.transitions.create('all', getTransitionProps({\n        easing: undefined,\n        style: undefined,\n        timeout: transitionDuration\n      }, {\n        mode\n      }));\n    }\n    if (changeTransition) {\n      drawerStyle.webkitTransition = transition;\n      drawerStyle.transition = transition;\n    }\n    if (!disableBackdropTransition && !hideBackdrop) {\n      const backdropStyle = backdropRef.current.style;\n      backdropStyle.opacity = 1 - translate / getMaxTranslate(horizontalSwipe, paperRef.current);\n      if (changeTransition) {\n        backdropStyle.webkitTransition = transition;\n        backdropStyle.transition = transition;\n      }\n    }\n  }, [anchor, disableBackdropTransition, hideBackdrop, theme, transitionDuration]);\n  const handleBodyTouchEnd = useEventCallback(nativeEvent => {\n    if (!touchDetected.current) {\n      return;\n    }\n    claimedSwipeInstance = null;\n    touchDetected.current = false;\n    ReactDOM.flushSync(() => {\n      setMaybeSwiping(false);\n    });\n\n    // The swipe wasn't started.\n    if (!swipeInstance.current.isSwiping) {\n      swipeInstance.current.isSwiping = null;\n      return;\n    }\n    swipeInstance.current.isSwiping = null;\n    const anchorRtl = getAnchor(theme, anchor);\n    const horizontal = isHorizontal(anchor);\n    let current;\n    if (horizontal) {\n      current = calculateCurrentX(anchorRtl, nativeEvent.changedTouches, ownerDocument(nativeEvent.currentTarget));\n    } else {\n      current = calculateCurrentY(anchorRtl, nativeEvent.changedTouches, ownerWindow(nativeEvent.currentTarget));\n    }\n    const startLocation = horizontal ? swipeInstance.current.startX : swipeInstance.current.startY;\n    const maxTranslate = getMaxTranslate(horizontal, paperRef.current);\n    const currentTranslate = getTranslate(current, startLocation, open, maxTranslate);\n    const translateRatio = currentTranslate / maxTranslate;\n    if (Math.abs(swipeInstance.current.velocity) > minFlingVelocity) {\n      // Calculate transition duration to match swipe speed\n      calculatedDurationRef.current = Math.abs((maxTranslate - currentTranslate) / swipeInstance.current.velocity) * 1000;\n    }\n    if (open) {\n      if (swipeInstance.current.velocity > minFlingVelocity || translateRatio > hysteresis) {\n        onClose();\n      } else {\n        // Reset the position, the swipe was aborted.\n        setPosition(0, {\n          mode: 'exit'\n        });\n      }\n      return;\n    }\n    if (swipeInstance.current.velocity < -minFlingVelocity || 1 - translateRatio > hysteresis) {\n      onOpen();\n    } else {\n      // Reset the position, the swipe was aborted.\n      setPosition(getMaxTranslate(horizontal, paperRef.current), {\n        mode: 'enter'\n      });\n    }\n  });\n  const startMaybeSwiping = (force = false) => {\n    if (!maybeSwiping) {\n      // on Safari Mobile, if you want to be able to have the 'click' event fired on child elements, nothing in the DOM can be changed.\n      // this is because Safari Mobile will not fire any mouse events (still fires touch though) if the DOM changes during mousemove.\n      // so do this change on first touchmove instead of touchstart\n      if (force || !(disableDiscovery && allowSwipeInChildren)) {\n        ReactDOM.flushSync(() => {\n          setMaybeSwiping(true);\n        });\n      }\n      const horizontalSwipe = isHorizontal(anchor);\n      if (!open && paperRef.current) {\n        // The ref may be null when a parent component updates while swiping.\n        setPosition(getMaxTranslate(horizontalSwipe, paperRef.current) + (disableDiscovery ? 15 : -DRAG_STARTED_SIGNAL), {\n          changeTransition: false\n        });\n      }\n      swipeInstance.current.velocity = 0;\n      swipeInstance.current.lastTime = null;\n      swipeInstance.current.lastTranslate = null;\n      swipeInstance.current.paperHit = false;\n      touchDetected.current = true;\n    }\n  };\n  const handleBodyTouchMove = useEventCallback(nativeEvent => {\n    // the ref may be null when a parent component updates while swiping\n    if (!paperRef.current || !touchDetected.current) {\n      return;\n    }\n\n    // We are not supposed to handle this touch move because the swipe was started in a scrollable container in the drawer\n    if (claimedSwipeInstance !== null && claimedSwipeInstance !== swipeInstance.current) {\n      return;\n    }\n    startMaybeSwiping(true);\n    const anchorRtl = getAnchor(theme, anchor);\n    const horizontalSwipe = isHorizontal(anchor);\n    const currentX = calculateCurrentX(anchorRtl, nativeEvent.touches, ownerDocument(nativeEvent.currentTarget));\n    const currentY = calculateCurrentY(anchorRtl, nativeEvent.touches, ownerWindow(nativeEvent.currentTarget));\n    if (open && paperRef.current.contains(nativeEvent.target) && claimedSwipeInstance === null) {\n      const domTreeShapes = getDomTreeShapes(nativeEvent.target, paperRef.current);\n      const hasNativeHandler = computeHasNativeHandler({\n        domTreeShapes,\n        start: horizontalSwipe ? swipeInstance.current.startX : swipeInstance.current.startY,\n        current: horizontalSwipe ? currentX : currentY,\n        anchor\n      });\n      if (hasNativeHandler) {\n        claimedSwipeInstance = true;\n        return;\n      }\n      claimedSwipeInstance = swipeInstance.current;\n    }\n\n    // We don't know yet.\n    if (swipeInstance.current.isSwiping == null) {\n      const dx = Math.abs(currentX - swipeInstance.current.startX);\n      const dy = Math.abs(currentY - swipeInstance.current.startY);\n      const definitelySwiping = horizontalSwipe ? dx > dy && dx > UNCERTAINTY_THRESHOLD : dy > dx && dy > UNCERTAINTY_THRESHOLD;\n      if (definitelySwiping && nativeEvent.cancelable) {\n        nativeEvent.preventDefault();\n      }\n      if (definitelySwiping === true || (horizontalSwipe ? dy > UNCERTAINTY_THRESHOLD : dx > UNCERTAINTY_THRESHOLD)) {\n        swipeInstance.current.isSwiping = definitelySwiping;\n        if (!definitelySwiping) {\n          handleBodyTouchEnd(nativeEvent);\n          return;\n        }\n\n        // Shift the starting point.\n        swipeInstance.current.startX = currentX;\n        swipeInstance.current.startY = currentY;\n\n        // Compensate for the part of the drawer displayed on touch start.\n        if (!disableDiscovery && !open) {\n          if (horizontalSwipe) {\n            swipeInstance.current.startX -= DRAG_STARTED_SIGNAL;\n          } else {\n            swipeInstance.current.startY -= DRAG_STARTED_SIGNAL;\n          }\n        }\n      }\n    }\n    if (!swipeInstance.current.isSwiping) {\n      return;\n    }\n    const maxTranslate = getMaxTranslate(horizontalSwipe, paperRef.current);\n    let startLocation = horizontalSwipe ? swipeInstance.current.startX : swipeInstance.current.startY;\n    if (open && !swipeInstance.current.paperHit) {\n      startLocation = Math.min(startLocation, maxTranslate);\n    }\n    const translate = getTranslate(horizontalSwipe ? currentX : currentY, startLocation, open, maxTranslate);\n    if (open) {\n      if (!swipeInstance.current.paperHit) {\n        const paperHit = horizontalSwipe ? currentX < maxTranslate : currentY < maxTranslate;\n        if (paperHit) {\n          swipeInstance.current.paperHit = true;\n          swipeInstance.current.startX = currentX;\n          swipeInstance.current.startY = currentY;\n        } else {\n          return;\n        }\n      } else if (translate === 0) {\n        swipeInstance.current.startX = currentX;\n        swipeInstance.current.startY = currentY;\n      }\n    }\n    if (swipeInstance.current.lastTranslate === null) {\n      swipeInstance.current.lastTranslate = translate;\n      swipeInstance.current.lastTime = performance.now() + 1;\n    }\n    const velocity = (translate - swipeInstance.current.lastTranslate) / (performance.now() - swipeInstance.current.lastTime) * 1e3;\n\n    // Low Pass filter.\n    swipeInstance.current.velocity = swipeInstance.current.velocity * 0.4 + velocity * 0.6;\n    swipeInstance.current.lastTranslate = translate;\n    swipeInstance.current.lastTime = performance.now();\n\n    // We are swiping, let's prevent the scroll event on iOS.\n    if (nativeEvent.cancelable) {\n      nativeEvent.preventDefault();\n    }\n    setPosition(translate);\n  });\n  const handleBodyTouchStart = useEventCallback(nativeEvent => {\n    // We are not supposed to handle this touch move.\n    // Example of use case: ignore the event if there is a Slider.\n    if (nativeEvent.defaultPrevented) {\n      return;\n    }\n\n    // We can only have one node at the time claiming ownership for handling the swipe.\n    if (nativeEvent.defaultMuiPrevented) {\n      return;\n    }\n\n    // At least one element clogs the drawer interaction zone.\n    if (open && (hideBackdrop || !backdropRef.current.contains(nativeEvent.target)) && !paperRef.current.contains(nativeEvent.target)) {\n      return;\n    }\n    const anchorRtl = getAnchor(theme, anchor);\n    const horizontalSwipe = isHorizontal(anchor);\n    const currentX = calculateCurrentX(anchorRtl, nativeEvent.touches, ownerDocument(nativeEvent.currentTarget));\n    const currentY = calculateCurrentY(anchorRtl, nativeEvent.touches, ownerWindow(nativeEvent.currentTarget));\n    if (!open) {\n      var _paperRef$current;\n      // logic for if swipe should be ignored:\n      // if disableSwipeToOpen\n      // if target != swipeArea, and target is not a child of paper ref\n      // if is a child of paper ref, and `allowSwipeInChildren` does not allow it\n      if (disableSwipeToOpen || !(nativeEvent.target === swipeAreaRef.current || (_paperRef$current = paperRef.current) != null && _paperRef$current.contains(nativeEvent.target) && (typeof allowSwipeInChildren === 'function' ? allowSwipeInChildren(nativeEvent, swipeAreaRef.current, paperRef.current) : allowSwipeInChildren))) {\n        return;\n      }\n      if (horizontalSwipe) {\n        if (currentX > swipeAreaWidth) {\n          return;\n        }\n      } else if (currentY > swipeAreaWidth) {\n        return;\n      }\n    }\n    nativeEvent.defaultMuiPrevented = true;\n    claimedSwipeInstance = null;\n    swipeInstance.current.startX = currentX;\n    swipeInstance.current.startY = currentY;\n    startMaybeSwiping();\n  });\n  React.useEffect(() => {\n    if (variant === 'temporary') {\n      const doc = ownerDocument(paperRef.current);\n      doc.addEventListener('touchstart', handleBodyTouchStart);\n      // A blocking listener prevents Firefox's navbar to auto-hide on scroll.\n      // It only needs to prevent scrolling on the drawer's content when open.\n      // When closed, the overlay prevents scrolling.\n      doc.addEventListener('touchmove', handleBodyTouchMove, {\n        passive: !open\n      });\n      doc.addEventListener('touchend', handleBodyTouchEnd);\n      return () => {\n        doc.removeEventListener('touchstart', handleBodyTouchStart);\n        doc.removeEventListener('touchmove', handleBodyTouchMove, {\n          passive: !open\n        });\n        doc.removeEventListener('touchend', handleBodyTouchEnd);\n      };\n    }\n    return undefined;\n  }, [variant, open, handleBodyTouchStart, handleBodyTouchMove, handleBodyTouchEnd]);\n  React.useEffect(() => () => {\n    // We need to release the lock.\n    if (claimedSwipeInstance === swipeInstance.current) {\n      claimedSwipeInstance = null;\n    }\n  }, []);\n  React.useEffect(() => {\n    if (!open) {\n      setMaybeSwiping(false);\n    }\n  }, [open]);\n  return /*#__PURE__*/_jsxs(React.Fragment, {\n    children: [/*#__PURE__*/_jsx(Drawer, _extends({\n      open: variant === 'temporary' && maybeSwiping ? true : open,\n      variant: variant,\n      ModalProps: _extends({\n        BackdropProps: _extends({}, BackdropProps, {\n          ref: backdropRef\n        })\n      }, variant === 'temporary' && {\n        keepMounted: true\n      }, ModalPropsProp),\n      hideBackdrop: hideBackdrop,\n      PaperProps: _extends({}, PaperProps, {\n        style: _extends({\n          pointerEvents: variant === 'temporary' && !open && !allowSwipeInChildren ? 'none' : ''\n        }, PaperProps.style),\n        ref: handleRef\n      }),\n      anchor: anchor,\n      transitionDuration: calculatedDurationRef.current || transitionDuration,\n      onClose: onClose,\n      ref: ref\n    }, other)), !disableSwipeToOpen && variant === 'temporary' && /*#__PURE__*/_jsx(NoSsr, {\n      children: /*#__PURE__*/_jsx(SwipeArea, _extends({\n        anchor: anchor,\n        ref: swipeAreaRef,\n        width: swipeAreaWidth\n      }, SwipeAreaProps))\n    })]\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? SwipeableDrawer.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * If set to true, the swipe event will open the drawer even if the user begins the swipe on one of the drawer's children.\n   * This can be useful in scenarios where the drawer is partially visible.\n   * You can customize it further with a callback that determines which children the user can drag over to open the drawer\n   * (for example, to ignore other elements that handle touch move events, like sliders).\n   *\n   * @param {TouchEvent} event The 'touchstart' event\n   * @param {HTMLDivElement} swipeArea The swipe area element\n   * @param {HTMLDivElement} paper The drawer's paper element\n   *\n   * @default false\n   */\n  allowSwipeInChildren: PropTypes.oneOfType([PropTypes.func, PropTypes.bool]),\n  /**\n   * @ignore\n   */\n  anchor: PropTypes.oneOf(['bottom', 'left', 'right', 'top']),\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Disable the backdrop transition.\n   * This can improve the FPS on low-end devices.\n   * @default false\n   */\n  disableBackdropTransition: PropTypes.bool,\n  /**\n   * If `true`, touching the screen near the edge of the drawer will not slide in the drawer a bit\n   * to promote accidental discovery of the swipe gesture.\n   * @default false\n   */\n  disableDiscovery: PropTypes.bool,\n  /**\n   * If `true`, swipe to open is disabled. This is useful in browsers where swiping triggers\n   * navigation actions. Swipe to open is disabled on iOS browsers by default.\n   * @default typeof navigator !== 'undefined' && /iPad|iPhone|iPod/.test(navigator.userAgent)\n   */\n  disableSwipeToOpen: PropTypes.bool,\n  /**\n   * @ignore\n   */\n  hideBackdrop: PropTypes.bool,\n  /**\n   * Affects how far the drawer must be opened/closed to change its state.\n   * Specified as percent (0-1) of the width of the drawer\n   * @default 0.52\n   */\n  hysteresis: PropTypes.number,\n  /**\n   * Defines, from which (average) velocity on, the swipe is\n   * defined as complete although hysteresis isn't reached.\n   * Good threshold is between 250 - 1000 px/s\n   * @default 450\n   */\n  minFlingVelocity: PropTypes.number,\n  /**\n   * @ignore\n   */\n  ModalProps: PropTypes /* @typescript-to-proptypes-ignore */.shape({\n    BackdropProps: PropTypes.shape({\n      component: elementTypeAcceptingRef\n    })\n  }),\n  /**\n   * Callback fired when the component requests to be closed.\n   *\n   * @param {React.SyntheticEvent<{}>} event The event source of the callback.\n   */\n  onClose: PropTypes.func.isRequired,\n  /**\n   * Callback fired when the component requests to be opened.\n   *\n   * @param {React.SyntheticEvent<{}>} event The event source of the callback.\n   */\n  onOpen: PropTypes.func.isRequired,\n  /**\n   * If `true`, the component is shown.\n   * @default false\n   */\n  open: PropTypes.bool,\n  /**\n   * @ignore\n   */\n  PaperProps: PropTypes /* @typescript-to-proptypes-ignore */.shape({\n    component: elementTypeAcceptingRef,\n    style: PropTypes.object\n  }),\n  /**\n   * The element is used to intercept the touch events on the edge.\n   */\n  SwipeAreaProps: PropTypes.object,\n  /**\n   * The width of the left most (or right most) area in `px` that\n   * the drawer can be swiped open from.\n   * @default 20\n   */\n  swipeAreaWidth: PropTypes.number,\n  /**\n   * The duration for the transition, in milliseconds.\n   * You may specify a single timeout for all transitions, or individually with an object.\n   * @default {\n   *   enter: theme.transitions.duration.enteringScreen,\n   *   exit: theme.transitions.duration.leavingScreen,\n   * }\n   */\n  transitionDuration: PropTypes.oneOfType([PropTypes.number, PropTypes.shape({\n    appear: PropTypes.number,\n    enter: PropTypes.number,\n    exit: PropTypes.number\n  })]),\n  /**\n   * @ignore\n   */\n  variant: PropTypes.oneOf(['permanent', 'persistent', 'temporary'])\n} : void 0;\nexport default SwipeableDrawer;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,eAAe,CAAC;EACjCC,UAAU,GAAG,CAAC,QAAQ,EAAE,2BAA2B,EAAE,kBAAkB,EAAE,oBAAoB,EAAE,cAAc,EAAE,YAAY,EAAE,sBAAsB,EAAE,kBAAkB,EAAE,YAAY,EAAE,SAAS,EAAE,QAAQ,EAAE,MAAM,EAAE,YAAY,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,oBAAoB,EAAE,SAAS,CAAC;AACxS,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAO,KAAKC,QAAQ,MAAM,WAAW;AACrC,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,uBAAuB,MAAM,oCAAoC;AACxE,OAAOC,KAAK,MAAM,UAAU;AAC5B,SAASC,eAAe,QAAQ,yBAAyB;AACzD,OAAOC,MAAM,IAAIC,SAAS,EAAEC,YAAY,QAAQ,kBAAkB;AAClE,OAAOC,UAAU,MAAM,qBAAqB;AAC5C,OAAOC,aAAa,MAAM,wBAAwB;AAClD,OAAOC,WAAW,MAAM,sBAAsB;AAC9C,OAAOC,gBAAgB,MAAM,2BAA2B;AACxD,OAAOC,iBAAiB,MAAM,4BAA4B;AAC1D,OAAOC,QAAQ,MAAM,oBAAoB;AACzC,SAASC,kBAAkB,QAAQ,sBAAsB;AACzD,OAAOC,SAAS,MAAM,aAAa;;AAEnC;AACA;AACA,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AACjD,MAAMC,qBAAqB,GAAG,CAAC,CAAC,CAAC;;AAEjC;AACA,MAAMC,mBAAmB,GAAG,EAAE,CAAC,CAAC;;AAEhC;AACA;AACA;AACA,IAAIC,oBAAoB,GAAG,IAAI;;AAE/B;AACA,OAAO,SAASC,KAAKA,CAAA,EAAG;EACtBD,oBAAoB,GAAG,IAAI;AAC7B;AACA,SAASE,iBAAiBA,CAACC,MAAM,EAAEC,OAAO,EAAEC,GAAG,EAAE;EAC/C,OAAOF,MAAM,KAAK,OAAO,GAAGE,GAAG,CAACC,IAAI,CAACC,WAAW,GAAGH,OAAO,CAAC,CAAC,CAAC,CAACI,KAAK,GAAGJ,OAAO,CAAC,CAAC,CAAC,CAACI,KAAK;AACxF;AACA,SAASC,iBAAiBA,CAACN,MAAM,EAAEC,OAAO,EAAEM,eAAe,EAAE;EAC3D,OAAOP,MAAM,KAAK,QAAQ,GAAGO,eAAe,CAACC,WAAW,GAAGP,OAAO,CAAC,CAAC,CAAC,CAACQ,OAAO,GAAGR,OAAO,CAAC,CAAC,CAAC,CAACQ,OAAO;AACpG;AACA,SAASC,eAAeA,CAACC,eAAe,EAAEC,aAAa,EAAE;EACvD,OAAOD,eAAe,GAAGC,aAAa,CAACC,WAAW,GAAGD,aAAa,CAACE,YAAY;AACjF;AACA,SAASC,YAAYA,CAACC,gBAAgB,EAAEC,aAAa,EAAEC,IAAI,EAAEC,YAAY,EAAE;EACzE,OAAOC,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,GAAG,CAACJ,IAAI,GAAGD,aAAa,GAAGD,gBAAgB,GAAGG,YAAY,GAAGF,aAAa,GAAGD,gBAAgB,EAAE,CAAC,CAAC,EAAEG,YAAY,CAAC;AACvI;;AAEA;AACA;AACA;AACA;AACA,SAASI,gBAAgBA,CAACC,OAAO,EAAEC,QAAQ,EAAE;EAC3C;EACA,MAAMC,aAAa,GAAG,EAAE;EACxB,OAAOF,OAAO,IAAIA,OAAO,KAAKC,QAAQ,CAACE,aAAa,EAAE;IACpD,MAAMC,KAAK,GAAG3C,WAAW,CAACwC,QAAQ,CAAC,CAACI,gBAAgB,CAACL,OAAO,CAAC;IAC7D;IACA;IACAI,KAAK,CAACE,gBAAgB,CAAC,UAAU,CAAC,KAAK,UAAU;IACjD;IACAF,KAAK,CAACE,gBAAgB,CAAC,YAAY,CAAC,KAAK,QAAQ,EAAE;MACjD;IAAA,CACD,MAAM,IAAIN,OAAO,CAACX,WAAW,GAAG,CAAC,IAAIW,OAAO,CAACO,WAAW,GAAGP,OAAO,CAACX,WAAW,IAAIW,OAAO,CAACV,YAAY,GAAG,CAAC,IAAIU,OAAO,CAACQ,YAAY,GAAGR,OAAO,CAACV,YAAY,EAAE;MAC1J;MACA;MACAY,aAAa,CAACO,IAAI,CAACT,OAAO,CAAC;IAC7B;IACAA,OAAO,GAAGA,OAAO,CAACG,aAAa;EACjC;EACA,OAAOD,aAAa;AACtB;;AAEA;AACA;AACA;AACA;AACA,SAASQ,uBAAuBA,CAAAC,IAAA,EAK7B;EAAA,IAL8B;IAC/BT,aAAa;IACbU,KAAK;IACLC,OAAO;IACPrC;EACF,CAAC,GAAAmC,IAAA;EACC;EACA,MAAMG,cAAc,GAAG;IACrBC,cAAc,EAAE;MACdC,CAAC,EAAE,YAAY;MACfC,CAAC,EAAE;IACL,CAAC;IACDC,YAAY,EAAE;MACZF,CAAC,EAAE,aAAa;MAChBC,CAAC,EAAE;IACL,CAAC;IACDE,YAAY,EAAE;MACZH,CAAC,EAAE,aAAa;MAChBC,CAAC,EAAE;IACL;EACF,CAAC;EACD,OAAOf,aAAa,CAACkB,IAAI,CAACC,KAAK,IAAI;IACjC;IACA,IAAIC,YAAY,GAAGT,OAAO,IAAID,KAAK;IACnC,IAAIpC,MAAM,KAAK,KAAK,IAAIA,MAAM,KAAK,MAAM,EAAE;MACzC8C,YAAY,GAAG,CAACA,YAAY;IAC9B;IACA,MAAMC,IAAI,GAAG/C,MAAM,KAAK,MAAM,IAAIA,MAAM,KAAK,OAAO,GAAG,GAAG,GAAG,GAAG;IAChE,MAAMuC,cAAc,GAAGnB,IAAI,CAAC4B,KAAK,CAACH,KAAK,CAACP,cAAc,CAACC,cAAc,CAACQ,IAAI,CAAC,CAAC,CAAC;IAC7E,MAAME,aAAa,GAAGV,cAAc,GAAG,CAAC;IACxC,MAAMW,WAAW,GAAGX,cAAc,GAAGM,KAAK,CAACP,cAAc,CAACK,YAAY,CAACI,IAAI,CAAC,CAAC,GAAGF,KAAK,CAACP,cAAc,CAACI,YAAY,CAACK,IAAI,CAAC,CAAC;IACxH,IAAID,YAAY,IAAII,WAAW,IAAI,CAACJ,YAAY,IAAIG,aAAa,EAAE;MACjE,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd,CAAC,CAAC;AACJ;AACA,MAAME,GAAG,GAAG,OAAOC,SAAS,KAAK,WAAW,IAAI,kBAAkB,CAACC,IAAI,CAACD,SAAS,CAACE,SAAS,CAAC;AAC5F,MAAMC,eAAe,GAAG,aAAajF,KAAK,CAACkF,UAAU,CAAC,SAASD,eAAeA,CAACE,OAAO,EAAEC,GAAG,EAAE;EAC3F,MAAMC,KAAK,GAAGhF,eAAe,CAAC;IAC5BiF,IAAI,EAAE,oBAAoB;IAC1BD,KAAK,EAAEF;EACT,CAAC,CAAC;EACF,MAAMI,KAAK,GAAGzE,QAAQ,CAAC,CAAC;EACxB,MAAM0E,yBAAyB,GAAG;IAChCC,KAAK,EAAEF,KAAK,CAACG,WAAW,CAACC,QAAQ,CAACC,cAAc;IAChDC,IAAI,EAAEN,KAAK,CAACG,WAAW,CAACC,QAAQ,CAACG;EACnC,CAAC;EACD,MAAM;MACFpE,MAAM,GAAG,MAAM;MACfqE,yBAAyB,GAAG,KAAK;MACjCC,gBAAgB,GAAG,KAAK;MACxBC,kBAAkB,GAAGpB,GAAG;MACxBqB,YAAY;MACZC,UAAU,GAAG,IAAI;MACjBC,oBAAoB,GAAG,KAAK;MAC5BC,gBAAgB,GAAG,GAAG;MACtBC,UAAU,EAAE;QACVC;MACF,CAAC,GAAG,CAAC,CAAC;MACNC,OAAO;MACPC,MAAM;MACN7D,IAAI,GAAG,KAAK;MACZ8D,UAAU,GAAG,CAAC,CAAC;MACfC,cAAc;MACdC,cAAc,GAAG,EAAE;MACnBC,kBAAkB,GAAGrB,yBAAyB;MAC9CsB,OAAO,GAAG,WAAW,CAAC;IACxB,CAAC,GAAGzB,KAAK;IACT0B,cAAc,GAAGlH,6BAA6B,CAACwF,KAAK,CAACiB,UAAU,EAAExG,SAAS,CAAC;IAC3EkH,KAAK,GAAGnH,6BAA6B,CAACwF,KAAK,EAAEtF,UAAU,CAAC;EAC1D,MAAM,CAACkH,YAAY,EAAEC,eAAe,CAAC,GAAGlH,KAAK,CAACmH,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAMC,aAAa,GAAGpH,KAAK,CAACqH,MAAM,CAAC;IACjCC,SAAS,EAAE;EACb,CAAC,CAAC;EACF,MAAMC,YAAY,GAAGvH,KAAK,CAACqH,MAAM,CAAC,CAAC;EACnC,MAAMG,WAAW,GAAGxH,KAAK,CAACqH,MAAM,CAAC,CAAC;EAClC,MAAMI,QAAQ,GAAGzH,KAAK,CAACqH,MAAM,CAAC,CAAC;EAC/B,MAAMK,SAAS,GAAGjH,UAAU,CAACiG,UAAU,CAACtB,GAAG,EAAEqC,QAAQ,CAAC;EACtD,MAAME,aAAa,GAAG3H,KAAK,CAACqH,MAAM,CAAC,KAAK,CAAC;;EAEzC;EACA,MAAMO,qBAAqB,GAAG5H,KAAK,CAACqH,MAAM,CAAC,CAAC;;EAE5C;EACAxG,iBAAiB,CAAC,MAAM;IACtB+G,qBAAqB,CAAC7D,OAAO,GAAG,IAAI;EACtC,CAAC,EAAE,CAACnB,IAAI,CAAC,CAAC;EACV,MAAMiF,WAAW,GAAG7H,KAAK,CAAC8H,WAAW,CAAC,UAACC,SAAS,EAAmB;IAAA,IAAjBC,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;IAC5D,MAAM;MACJG,IAAI,GAAG,IAAI;MACXC,gBAAgB,GAAG;IACrB,CAAC,GAAGL,OAAO;IACX,MAAMM,SAAS,GAAG/H,SAAS,CAACgF,KAAK,EAAE7D,MAAM,CAAC;IAC1C,MAAM6G,sBAAsB,GAAG,CAAC,OAAO,EAAE,QAAQ,CAAC,CAACC,OAAO,CAACF,SAAS,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IACrF,MAAMjG,eAAe,GAAG7B,YAAY,CAACkB,MAAM,CAAC;IAC5C,MAAM+G,SAAS,GAAGpG,eAAe,GAAG,aAAakG,sBAAsB,GAAGR,SAAS,QAAQ,GAAG,gBAAgBQ,sBAAsB,GAAGR,SAAS,KAAK;IACrJ,MAAMW,WAAW,GAAGjB,QAAQ,CAAC1D,OAAO,CAACT,KAAK;IAC1CoF,WAAW,CAACC,eAAe,GAAGF,SAAS;IACvCC,WAAW,CAACD,SAAS,GAAGA,SAAS;IACjC,IAAIG,UAAU,GAAG,EAAE;IACnB,IAAIR,IAAI,EAAE;MACRQ,UAAU,GAAGrD,KAAK,CAACG,WAAW,CAACmD,MAAM,CAAC,KAAK,EAAE9H,kBAAkB,CAAC;QAC9D+H,MAAM,EAAEX,SAAS;QACjB7E,KAAK,EAAE6E,SAAS;QAChBY,OAAO,EAAElC;MACX,CAAC,EAAE;QACDuB;MACF,CAAC,CAAC,CAAC;IACL;IACA,IAAIC,gBAAgB,EAAE;MACpBK,WAAW,CAACM,gBAAgB,GAAGJ,UAAU;MACzCF,WAAW,CAACE,UAAU,GAAGA,UAAU;IACrC;IACA,IAAI,CAAC7C,yBAAyB,IAAI,CAACG,YAAY,EAAE;MAC/C,MAAM+C,aAAa,GAAGzB,WAAW,CAACzD,OAAO,CAACT,KAAK;MAC/C2F,aAAa,CAACC,OAAO,GAAG,CAAC,GAAGnB,SAAS,GAAG3F,eAAe,CAACC,eAAe,EAAEoF,QAAQ,CAAC1D,OAAO,CAAC;MAC1F,IAAIsE,gBAAgB,EAAE;QACpBY,aAAa,CAACD,gBAAgB,GAAGJ,UAAU;QAC3CK,aAAa,CAACL,UAAU,GAAGA,UAAU;MACvC;IACF;EACF,CAAC,EAAE,CAAClH,MAAM,EAAEqE,yBAAyB,EAAEG,YAAY,EAAEX,KAAK,EAAEsB,kBAAkB,CAAC,CAAC;EAChF,MAAMsC,kBAAkB,GAAGvI,gBAAgB,CAACwI,WAAW,IAAI;IACzD,IAAI,CAACzB,aAAa,CAAC5D,OAAO,EAAE;MAC1B;IACF;IACAxC,oBAAoB,GAAG,IAAI;IAC3BoG,aAAa,CAAC5D,OAAO,GAAG,KAAK;IAC7B9D,QAAQ,CAACoJ,SAAS,CAAC,MAAM;MACvBnC,eAAe,CAAC,KAAK,CAAC;IACxB,CAAC,CAAC;;IAEF;IACA,IAAI,CAACE,aAAa,CAACrD,OAAO,CAACuD,SAAS,EAAE;MACpCF,aAAa,CAACrD,OAAO,CAACuD,SAAS,GAAG,IAAI;MACtC;IACF;IACAF,aAAa,CAACrD,OAAO,CAACuD,SAAS,GAAG,IAAI;IACtC,MAAMgB,SAAS,GAAG/H,SAAS,CAACgF,KAAK,EAAE7D,MAAM,CAAC;IAC1C,MAAM4H,UAAU,GAAG9I,YAAY,CAACkB,MAAM,CAAC;IACvC,IAAIqC,OAAO;IACX,IAAIuF,UAAU,EAAE;MACdvF,OAAO,GAAGtC,iBAAiB,CAAC6G,SAAS,EAAEc,WAAW,CAACG,cAAc,EAAE7I,aAAa,CAAC0I,WAAW,CAACI,aAAa,CAAC,CAAC;IAC9G,CAAC,MAAM;MACLzF,OAAO,GAAG/B,iBAAiB,CAACsG,SAAS,EAAEc,WAAW,CAACG,cAAc,EAAE5I,WAAW,CAACyI,WAAW,CAACI,aAAa,CAAC,CAAC;IAC5G;IACA,MAAM7G,aAAa,GAAG2G,UAAU,GAAGlC,aAAa,CAACrD,OAAO,CAAC0F,MAAM,GAAGrC,aAAa,CAACrD,OAAO,CAAC2F,MAAM;IAC9F,MAAM7G,YAAY,GAAGT,eAAe,CAACkH,UAAU,EAAE7B,QAAQ,CAAC1D,OAAO,CAAC;IAClE,MAAMrB,gBAAgB,GAAGD,YAAY,CAACsB,OAAO,EAAEpB,aAAa,EAAEC,IAAI,EAAEC,YAAY,CAAC;IACjF,MAAM8G,cAAc,GAAGjH,gBAAgB,GAAGG,YAAY;IACtD,IAAIC,IAAI,CAAC8G,GAAG,CAACxC,aAAa,CAACrD,OAAO,CAAC8F,QAAQ,CAAC,GAAGxD,gBAAgB,EAAE;MAC/D;MACAuB,qBAAqB,CAAC7D,OAAO,GAAGjB,IAAI,CAAC8G,GAAG,CAAC,CAAC/G,YAAY,GAAGH,gBAAgB,IAAI0E,aAAa,CAACrD,OAAO,CAAC8F,QAAQ,CAAC,GAAG,IAAI;IACrH;IACA,IAAIjH,IAAI,EAAE;MACR,IAAIwE,aAAa,CAACrD,OAAO,CAAC8F,QAAQ,GAAGxD,gBAAgB,IAAIsD,cAAc,GAAGxD,UAAU,EAAE;QACpFK,OAAO,CAAC,CAAC;MACX,CAAC,MAAM;QACL;QACAqB,WAAW,CAAC,CAAC,EAAE;UACbO,IAAI,EAAE;QACR,CAAC,CAAC;MACJ;MACA;IACF;IACA,IAAIhB,aAAa,CAACrD,OAAO,CAAC8F,QAAQ,GAAG,CAACxD,gBAAgB,IAAI,CAAC,GAAGsD,cAAc,GAAGxD,UAAU,EAAE;MACzFM,MAAM,CAAC,CAAC;IACV,CAAC,MAAM;MACL;MACAoB,WAAW,CAACzF,eAAe,CAACkH,UAAU,EAAE7B,QAAQ,CAAC1D,OAAO,CAAC,EAAE;QACzDqE,IAAI,EAAE;MACR,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;EACF,MAAM0B,iBAAiB,GAAG,SAAAA,CAAA,EAAmB;IAAA,IAAlBC,KAAK,GAAA9B,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;IACtC,IAAI,CAAChB,YAAY,EAAE;MACjB;MACA;MACA;MACA,IAAI8C,KAAK,IAAI,EAAE/D,gBAAgB,IAAII,oBAAoB,CAAC,EAAE;QACxDnG,QAAQ,CAACoJ,SAAS,CAAC,MAAM;UACvBnC,eAAe,CAAC,IAAI,CAAC;QACvB,CAAC,CAAC;MACJ;MACA,MAAM7E,eAAe,GAAG7B,YAAY,CAACkB,MAAM,CAAC;MAC5C,IAAI,CAACkB,IAAI,IAAI6E,QAAQ,CAAC1D,OAAO,EAAE;QAC7B;QACA8D,WAAW,CAACzF,eAAe,CAACC,eAAe,EAAEoF,QAAQ,CAAC1D,OAAO,CAAC,IAAIiC,gBAAgB,GAAG,EAAE,GAAG,CAAC1E,mBAAmB,CAAC,EAAE;UAC/G+G,gBAAgB,EAAE;QACpB,CAAC,CAAC;MACJ;MACAjB,aAAa,CAACrD,OAAO,CAAC8F,QAAQ,GAAG,CAAC;MAClCzC,aAAa,CAACrD,OAAO,CAACiG,QAAQ,GAAG,IAAI;MACrC5C,aAAa,CAACrD,OAAO,CAACkG,aAAa,GAAG,IAAI;MAC1C7C,aAAa,CAACrD,OAAO,CAACmG,QAAQ,GAAG,KAAK;MACtCvC,aAAa,CAAC5D,OAAO,GAAG,IAAI;IAC9B;EACF,CAAC;EACD,MAAMoG,mBAAmB,GAAGvJ,gBAAgB,CAACwI,WAAW,IAAI;IAC1D;IACA,IAAI,CAAC3B,QAAQ,CAAC1D,OAAO,IAAI,CAAC4D,aAAa,CAAC5D,OAAO,EAAE;MAC/C;IACF;;IAEA;IACA,IAAIxC,oBAAoB,KAAK,IAAI,IAAIA,oBAAoB,KAAK6F,aAAa,CAACrD,OAAO,EAAE;MACnF;IACF;IACA+F,iBAAiB,CAAC,IAAI,CAAC;IACvB,MAAMxB,SAAS,GAAG/H,SAAS,CAACgF,KAAK,EAAE7D,MAAM,CAAC;IAC1C,MAAMW,eAAe,GAAG7B,YAAY,CAACkB,MAAM,CAAC;IAC5C,MAAM0I,QAAQ,GAAG3I,iBAAiB,CAAC6G,SAAS,EAAEc,WAAW,CAACzH,OAAO,EAAEjB,aAAa,CAAC0I,WAAW,CAACI,aAAa,CAAC,CAAC;IAC5G,MAAMa,QAAQ,GAAGrI,iBAAiB,CAACsG,SAAS,EAAEc,WAAW,CAACzH,OAAO,EAAEhB,WAAW,CAACyI,WAAW,CAACI,aAAa,CAAC,CAAC;IAC1G,IAAI5G,IAAI,IAAI6E,QAAQ,CAAC1D,OAAO,CAACuG,QAAQ,CAAClB,WAAW,CAACmB,MAAM,CAAC,IAAIhJ,oBAAoB,KAAK,IAAI,EAAE;MAC1F,MAAM6B,aAAa,GAAGH,gBAAgB,CAACmG,WAAW,CAACmB,MAAM,EAAE9C,QAAQ,CAAC1D,OAAO,CAAC;MAC5E,MAAMyG,gBAAgB,GAAG5G,uBAAuB,CAAC;QAC/CR,aAAa;QACbU,KAAK,EAAEzB,eAAe,GAAG+E,aAAa,CAACrD,OAAO,CAAC0F,MAAM,GAAGrC,aAAa,CAACrD,OAAO,CAAC2F,MAAM;QACpF3F,OAAO,EAAE1B,eAAe,GAAG+H,QAAQ,GAAGC,QAAQ;QAC9C3I;MACF,CAAC,CAAC;MACF,IAAI8I,gBAAgB,EAAE;QACpBjJ,oBAAoB,GAAG,IAAI;QAC3B;MACF;MACAA,oBAAoB,GAAG6F,aAAa,CAACrD,OAAO;IAC9C;;IAEA;IACA,IAAIqD,aAAa,CAACrD,OAAO,CAACuD,SAAS,IAAI,IAAI,EAAE;MAC3C,MAAMmD,EAAE,GAAG3H,IAAI,CAAC8G,GAAG,CAACQ,QAAQ,GAAGhD,aAAa,CAACrD,OAAO,CAAC0F,MAAM,CAAC;MAC5D,MAAMiB,EAAE,GAAG5H,IAAI,CAAC8G,GAAG,CAACS,QAAQ,GAAGjD,aAAa,CAACrD,OAAO,CAAC2F,MAAM,CAAC;MAC5D,MAAMiB,iBAAiB,GAAGtI,eAAe,GAAGoI,EAAE,GAAGC,EAAE,IAAID,EAAE,GAAGpJ,qBAAqB,GAAGqJ,EAAE,GAAGD,EAAE,IAAIC,EAAE,GAAGrJ,qBAAqB;MACzH,IAAIsJ,iBAAiB,IAAIvB,WAAW,CAACwB,UAAU,EAAE;QAC/CxB,WAAW,CAACyB,cAAc,CAAC,CAAC;MAC9B;MACA,IAAIF,iBAAiB,KAAK,IAAI,KAAKtI,eAAe,GAAGqI,EAAE,GAAGrJ,qBAAqB,GAAGoJ,EAAE,GAAGpJ,qBAAqB,CAAC,EAAE;QAC7G+F,aAAa,CAACrD,OAAO,CAACuD,SAAS,GAAGqD,iBAAiB;QACnD,IAAI,CAACA,iBAAiB,EAAE;UACtBxB,kBAAkB,CAACC,WAAW,CAAC;UAC/B;QACF;;QAEA;QACAhC,aAAa,CAACrD,OAAO,CAAC0F,MAAM,GAAGW,QAAQ;QACvChD,aAAa,CAACrD,OAAO,CAAC2F,MAAM,GAAGW,QAAQ;;QAEvC;QACA,IAAI,CAACrE,gBAAgB,IAAI,CAACpD,IAAI,EAAE;UAC9B,IAAIP,eAAe,EAAE;YACnB+E,aAAa,CAACrD,OAAO,CAAC0F,MAAM,IAAInI,mBAAmB;UACrD,CAAC,MAAM;YACL8F,aAAa,CAACrD,OAAO,CAAC2F,MAAM,IAAIpI,mBAAmB;UACrD;QACF;MACF;IACF;IACA,IAAI,CAAC8F,aAAa,CAACrD,OAAO,CAACuD,SAAS,EAAE;MACpC;IACF;IACA,MAAMzE,YAAY,GAAGT,eAAe,CAACC,eAAe,EAAEoF,QAAQ,CAAC1D,OAAO,CAAC;IACvE,IAAIpB,aAAa,GAAGN,eAAe,GAAG+E,aAAa,CAACrD,OAAO,CAAC0F,MAAM,GAAGrC,aAAa,CAACrD,OAAO,CAAC2F,MAAM;IACjG,IAAI9G,IAAI,IAAI,CAACwE,aAAa,CAACrD,OAAO,CAACmG,QAAQ,EAAE;MAC3CvH,aAAa,GAAGG,IAAI,CAACC,GAAG,CAACJ,aAAa,EAAEE,YAAY,CAAC;IACvD;IACA,MAAMkF,SAAS,GAAGtF,YAAY,CAACJ,eAAe,GAAG+H,QAAQ,GAAGC,QAAQ,EAAE1H,aAAa,EAAEC,IAAI,EAAEC,YAAY,CAAC;IACxG,IAAID,IAAI,EAAE;MACR,IAAI,CAACwE,aAAa,CAACrD,OAAO,CAACmG,QAAQ,EAAE;QACnC,MAAMA,QAAQ,GAAG7H,eAAe,GAAG+H,QAAQ,GAAGvH,YAAY,GAAGwH,QAAQ,GAAGxH,YAAY;QACpF,IAAIqH,QAAQ,EAAE;UACZ9C,aAAa,CAACrD,OAAO,CAACmG,QAAQ,GAAG,IAAI;UACrC9C,aAAa,CAACrD,OAAO,CAAC0F,MAAM,GAAGW,QAAQ;UACvChD,aAAa,CAACrD,OAAO,CAAC2F,MAAM,GAAGW,QAAQ;QACzC,CAAC,MAAM;UACL;QACF;MACF,CAAC,MAAM,IAAItC,SAAS,KAAK,CAAC,EAAE;QAC1BX,aAAa,CAACrD,OAAO,CAAC0F,MAAM,GAAGW,QAAQ;QACvChD,aAAa,CAACrD,OAAO,CAAC2F,MAAM,GAAGW,QAAQ;MACzC;IACF;IACA,IAAIjD,aAAa,CAACrD,OAAO,CAACkG,aAAa,KAAK,IAAI,EAAE;MAChD7C,aAAa,CAACrD,OAAO,CAACkG,aAAa,GAAGlC,SAAS;MAC/CX,aAAa,CAACrD,OAAO,CAACiG,QAAQ,GAAGc,WAAW,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC;IACxD;IACA,MAAMlB,QAAQ,GAAG,CAAC9B,SAAS,GAAGX,aAAa,CAACrD,OAAO,CAACkG,aAAa,KAAKa,WAAW,CAACC,GAAG,CAAC,CAAC,GAAG3D,aAAa,CAACrD,OAAO,CAACiG,QAAQ,CAAC,GAAG,GAAG;;IAE/H;IACA5C,aAAa,CAACrD,OAAO,CAAC8F,QAAQ,GAAGzC,aAAa,CAACrD,OAAO,CAAC8F,QAAQ,GAAG,GAAG,GAAGA,QAAQ,GAAG,GAAG;IACtFzC,aAAa,CAACrD,OAAO,CAACkG,aAAa,GAAGlC,SAAS;IAC/CX,aAAa,CAACrD,OAAO,CAACiG,QAAQ,GAAGc,WAAW,CAACC,GAAG,CAAC,CAAC;;IAElD;IACA,IAAI3B,WAAW,CAACwB,UAAU,EAAE;MAC1BxB,WAAW,CAACyB,cAAc,CAAC,CAAC;IAC9B;IACAhD,WAAW,CAACE,SAAS,CAAC;EACxB,CAAC,CAAC;EACF,MAAMiD,oBAAoB,GAAGpK,gBAAgB,CAACwI,WAAW,IAAI;IAC3D;IACA;IACA,IAAIA,WAAW,CAAC6B,gBAAgB,EAAE;MAChC;IACF;;IAEA;IACA,IAAI7B,WAAW,CAAC8B,mBAAmB,EAAE;MACnC;IACF;;IAEA;IACA,IAAItI,IAAI,KAAKsD,YAAY,IAAI,CAACsB,WAAW,CAACzD,OAAO,CAACuG,QAAQ,CAAClB,WAAW,CAACmB,MAAM,CAAC,CAAC,IAAI,CAAC9C,QAAQ,CAAC1D,OAAO,CAACuG,QAAQ,CAAClB,WAAW,CAACmB,MAAM,CAAC,EAAE;MACjI;IACF;IACA,MAAMjC,SAAS,GAAG/H,SAAS,CAACgF,KAAK,EAAE7D,MAAM,CAAC;IAC1C,MAAMW,eAAe,GAAG7B,YAAY,CAACkB,MAAM,CAAC;IAC5C,MAAM0I,QAAQ,GAAG3I,iBAAiB,CAAC6G,SAAS,EAAEc,WAAW,CAACzH,OAAO,EAAEjB,aAAa,CAAC0I,WAAW,CAACI,aAAa,CAAC,CAAC;IAC5G,MAAMa,QAAQ,GAAGrI,iBAAiB,CAACsG,SAAS,EAAEc,WAAW,CAACzH,OAAO,EAAEhB,WAAW,CAACyI,WAAW,CAACI,aAAa,CAAC,CAAC;IAC1G,IAAI,CAAC5G,IAAI,EAAE;MACT,IAAIuI,iBAAiB;MACrB;MACA;MACA;MACA;MACA,IAAIlF,kBAAkB,IAAI,EAAEmD,WAAW,CAACmB,MAAM,KAAKhD,YAAY,CAACxD,OAAO,IAAI,CAACoH,iBAAiB,GAAG1D,QAAQ,CAAC1D,OAAO,KAAK,IAAI,IAAIoH,iBAAiB,CAACb,QAAQ,CAAClB,WAAW,CAACmB,MAAM,CAAC,KAAK,OAAOnE,oBAAoB,KAAK,UAAU,GAAGA,oBAAoB,CAACgD,WAAW,EAAE7B,YAAY,CAACxD,OAAO,EAAE0D,QAAQ,CAAC1D,OAAO,CAAC,GAAGqC,oBAAoB,CAAC,CAAC,EAAE;QAC/T;MACF;MACA,IAAI/D,eAAe,EAAE;QACnB,IAAI+H,QAAQ,GAAGxD,cAAc,EAAE;UAC7B;QACF;MACF,CAAC,MAAM,IAAIyD,QAAQ,GAAGzD,cAAc,EAAE;QACpC;MACF;IACF;IACAwC,WAAW,CAAC8B,mBAAmB,GAAG,IAAI;IACtC3J,oBAAoB,GAAG,IAAI;IAC3B6F,aAAa,CAACrD,OAAO,CAAC0F,MAAM,GAAGW,QAAQ;IACvChD,aAAa,CAACrD,OAAO,CAAC2F,MAAM,GAAGW,QAAQ;IACvCP,iBAAiB,CAAC,CAAC;EACrB,CAAC,CAAC;EACF9J,KAAK,CAACoL,SAAS,CAAC,MAAM;IACpB,IAAItE,OAAO,KAAK,WAAW,EAAE;MAC3B,MAAMlF,GAAG,GAAGlB,aAAa,CAAC+G,QAAQ,CAAC1D,OAAO,CAAC;MAC3CnC,GAAG,CAACyJ,gBAAgB,CAAC,YAAY,EAAEL,oBAAoB,CAAC;MACxD;MACA;MACA;MACApJ,GAAG,CAACyJ,gBAAgB,CAAC,WAAW,EAAElB,mBAAmB,EAAE;QACrDmB,OAAO,EAAE,CAAC1I;MACZ,CAAC,CAAC;MACFhB,GAAG,CAACyJ,gBAAgB,CAAC,UAAU,EAAElC,kBAAkB,CAAC;MACpD,OAAO,MAAM;QACXvH,GAAG,CAAC2J,mBAAmB,CAAC,YAAY,EAAEP,oBAAoB,CAAC;QAC3DpJ,GAAG,CAAC2J,mBAAmB,CAAC,WAAW,EAAEpB,mBAAmB,EAAE;UACxDmB,OAAO,EAAE,CAAC1I;QACZ,CAAC,CAAC;QACFhB,GAAG,CAAC2J,mBAAmB,CAAC,UAAU,EAAEpC,kBAAkB,CAAC;MACzD,CAAC;IACH;IACA,OAAOhB,SAAS;EAClB,CAAC,EAAE,CAACrB,OAAO,EAAElE,IAAI,EAAEoI,oBAAoB,EAAEb,mBAAmB,EAAEhB,kBAAkB,CAAC,CAAC;EAClFnJ,KAAK,CAACoL,SAAS,CAAC,MAAM,MAAM;IAC1B;IACA,IAAI7J,oBAAoB,KAAK6F,aAAa,CAACrD,OAAO,EAAE;MAClDxC,oBAAoB,GAAG,IAAI;IAC7B;EACF,CAAC,EAAE,EAAE,CAAC;EACNvB,KAAK,CAACoL,SAAS,CAAC,MAAM;IACpB,IAAI,CAACxI,IAAI,EAAE;MACTsE,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC,EAAE,CAACtE,IAAI,CAAC,CAAC;EACV,OAAO,aAAaxB,KAAK,CAACpB,KAAK,CAACwL,QAAQ,EAAE;IACxCC,QAAQ,EAAE,CAAC,aAAavK,IAAI,CAACZ,MAAM,EAAEV,QAAQ,CAAC;MAC5CgD,IAAI,EAAEkE,OAAO,KAAK,WAAW,IAAIG,YAAY,GAAG,IAAI,GAAGrE,IAAI;MAC3DkE,OAAO,EAAEA,OAAO;MAChBR,UAAU,EAAE1G,QAAQ,CAAC;QACnB2G,aAAa,EAAE3G,QAAQ,CAAC,CAAC,CAAC,EAAE2G,aAAa,EAAE;UACzCnB,GAAG,EAAEoC;QACP,CAAC;MACH,CAAC,EAAEV,OAAO,KAAK,WAAW,IAAI;QAC5B4E,WAAW,EAAE;MACf,CAAC,EAAE3E,cAAc,CAAC;MAClBb,YAAY,EAAEA,YAAY;MAC1BQ,UAAU,EAAE9G,QAAQ,CAAC,CAAC,CAAC,EAAE8G,UAAU,EAAE;QACnCpD,KAAK,EAAE1D,QAAQ,CAAC;UACd+L,aAAa,EAAE7E,OAAO,KAAK,WAAW,IAAI,CAAClE,IAAI,IAAI,CAACwD,oBAAoB,GAAG,MAAM,GAAG;QACtF,CAAC,EAAEM,UAAU,CAACpD,KAAK,CAAC;QACpB8B,GAAG,EAAEsC;MACP,CAAC,CAAC;MACFhG,MAAM,EAAEA,MAAM;MACdmF,kBAAkB,EAAEe,qBAAqB,CAAC7D,OAAO,IAAI8C,kBAAkB;MACvEL,OAAO,EAAEA,OAAO;MAChBpB,GAAG,EAAEA;IACP,CAAC,EAAE4B,KAAK,CAAC,CAAC,EAAE,CAACf,kBAAkB,IAAIa,OAAO,KAAK,WAAW,IAAI,aAAa5F,IAAI,CAACd,KAAK,EAAE;MACrFqL,QAAQ,EAAE,aAAavK,IAAI,CAACF,SAAS,EAAEpB,QAAQ,CAAC;QAC9C8B,MAAM,EAAEA,MAAM;QACd0D,GAAG,EAAEmC,YAAY;QACjBqE,KAAK,EAAEhF;MACT,CAAC,EAAED,cAAc,CAAC;IACpB,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC;AACFkF,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG9G,eAAe,CAAC+G,SAAS,CAAC,yBAAyB;EACzF;EACA;EACA;EACA;EACA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE5F,oBAAoB,EAAElG,SAAS,CAAC+L,SAAS,CAAC,CAAC/L,SAAS,CAACgM,IAAI,EAAEhM,SAAS,CAACiM,IAAI,CAAC,CAAC;EAC3E;AACF;AACA;EACEzK,MAAM,EAAExB,SAAS,CAACkM,KAAK,CAAC,CAAC,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;EAC3D;AACF;AACA;EACEX,QAAQ,EAAEvL,SAAS,CAACmM,IAAI;EACxB;AACF;AACA;AACA;AACA;EACEtG,yBAAyB,EAAE7F,SAAS,CAACiM,IAAI;EACzC;AACF;AACA;AACA;AACA;EACEnG,gBAAgB,EAAE9F,SAAS,CAACiM,IAAI;EAChC;AACF;AACA;AACA;AACA;EACElG,kBAAkB,EAAE/F,SAAS,CAACiM,IAAI;EAClC;AACF;AACA;EACEjG,YAAY,EAAEhG,SAAS,CAACiM,IAAI;EAC5B;AACF;AACA;AACA;AACA;EACEhG,UAAU,EAAEjG,SAAS,CAACoM,MAAM;EAC5B;AACF;AACA;AACA;AACA;AACA;EACEjG,gBAAgB,EAAEnG,SAAS,CAACoM,MAAM;EAClC;AACF;AACA;EACEhG,UAAU,EAAEpG,SAAS,CAAC,sCAAsCqE,KAAK,CAAC;IAChEgC,aAAa,EAAErG,SAAS,CAACqE,KAAK,CAAC;MAC7BgI,SAAS,EAAEpM;IACb,CAAC;EACH,CAAC,CAAC;EACF;AACF;AACA;AACA;AACA;EACEqG,OAAO,EAAEtG,SAAS,CAACgM,IAAI,CAACM,UAAU;EAClC;AACF;AACA;AACA;AACA;EACE/F,MAAM,EAAEvG,SAAS,CAACgM,IAAI,CAACM,UAAU;EACjC;AACF;AACA;AACA;EACE5J,IAAI,EAAE1C,SAAS,CAACiM,IAAI;EACpB;AACF;AACA;EACEzF,UAAU,EAAExG,SAAS,CAAC,sCAAsCqE,KAAK,CAAC;IAChEgI,SAAS,EAAEpM,uBAAuB;IAClCmD,KAAK,EAAEpD,SAAS,CAACuM;EACnB,CAAC,CAAC;EACF;AACF;AACA;EACE9F,cAAc,EAAEzG,SAAS,CAACuM,MAAM;EAChC;AACF;AACA;AACA;AACA;EACE7F,cAAc,EAAE1G,SAAS,CAACoM,MAAM;EAChC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACEzF,kBAAkB,EAAE3G,SAAS,CAAC+L,SAAS,CAAC,CAAC/L,SAAS,CAACoM,MAAM,EAAEpM,SAAS,CAACqE,KAAK,CAAC;IACzEmI,MAAM,EAAExM,SAAS,CAACoM,MAAM;IACxB7G,KAAK,EAAEvF,SAAS,CAACoM,MAAM;IACvBzG,IAAI,EAAE3F,SAAS,CAACoM;EAClB,CAAC,CAAC,CAAC,CAAC;EACJ;AACF;AACA;EACExF,OAAO,EAAE5G,SAAS,CAACkM,KAAK,CAAC,CAAC,WAAW,EAAE,YAAY,EAAE,WAAW,CAAC;AACnE,CAAC,GAAG,KAAK,CAAC;AACV,eAAenH,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}