{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"className\", \"id\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport Typography from '../Typography';\nimport styled from '../styles/styled';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport { getDialogTitleUtilityClass } from './dialogTitleClasses';\nimport DialogContext from '../Dialog/DialogContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root']\n  };\n  return composeClasses(slots, getDialogTitleUtilityClass, classes);\n};\nconst DialogTitleRoot = styled(Typography, {\n  name: 'MuiDialogTitle',\n  slot: 'Root',\n  overridesResolver: (props, styles) => styles.root\n})({\n  padding: '16px 24px',\n  flex: '0 0 auto'\n});\nconst DialogTitle = /*#__PURE__*/React.forwardRef(function DialogTitle(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiDialogTitle'\n  });\n  const {\n      className,\n      id: idProp\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = props;\n  const classes = useUtilityClasses(ownerState);\n  const {\n    titleId = idProp\n  } = React.useContext(DialogContext);\n  return /*#__PURE__*/_jsx(DialogTitleRoot, _extends({\n    component: \"h2\",\n    className: clsx(classes.root, className),\n    ownerState: ownerState,\n    ref: ref,\n    variant: \"h6\",\n    id: idProp != null ? idProp : titleId\n  }, other));\n});\nprocess.env.NODE_ENV !== \"production\" ? DialogTitle.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * @ignore\n   */\n  id: PropTypes.string,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default DialogTitle;", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "React", "PropTypes", "clsx", "composeClasses", "Typography", "styled", "useDefaultProps", "getDialogTitleUtilityClass", "DialogContext", "jsx", "_jsx", "useUtilityClasses", "ownerState", "classes", "slots", "root", "DialogTitleRoot", "name", "slot", "overridesResolver", "props", "styles", "padding", "flex", "DialogTitle", "forwardRef", "inProps", "ref", "className", "id", "idProp", "other", "titleId", "useContext", "component", "variant", "process", "env", "NODE_ENV", "propTypes", "children", "node", "object", "string", "sx", "oneOfType", "arrayOf", "func", "bool"], "sources": ["/home/<USER>/PycharmProjects/ai-voice-mate-dev/ai_voice_mate/frontend/node_modules/@mui/material/DialogTitle/DialogTitle.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"className\", \"id\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport Typography from '../Typography';\nimport styled from '../styles/styled';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport { getDialogTitleUtilityClass } from './dialogTitleClasses';\nimport DialogContext from '../Dialog/DialogContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root']\n  };\n  return composeClasses(slots, getDialogTitleUtilityClass, classes);\n};\nconst DialogTitleRoot = styled(Typography, {\n  name: 'MuiDialogTitle',\n  slot: 'Root',\n  overridesResolver: (props, styles) => styles.root\n})({\n  padding: '16px 24px',\n  flex: '0 0 auto'\n});\nconst DialogTitle = /*#__PURE__*/React.forwardRef(function DialogTitle(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiDialogTitle'\n  });\n  const {\n      className,\n      id: idProp\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = props;\n  const classes = useUtilityClasses(ownerState);\n  const {\n    titleId = idProp\n  } = React.useContext(DialogContext);\n  return /*#__PURE__*/_jsx(DialogTitleRoot, _extends({\n    component: \"h2\",\n    className: clsx(classes.root, className),\n    ownerState: ownerState,\n    ref: ref,\n    variant: \"h6\",\n    id: idProp != null ? idProp : titleId\n  }, other));\n});\nprocess.env.NODE_ENV !== \"production\" ? DialogTitle.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * @ignore\n   */\n  id: PropTypes.string,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default DialogTitle;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,WAAW,EAAE,IAAI,CAAC;AACrC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,cAAc,MAAM,2BAA2B;AACtD,OAAOC,UAAU,MAAM,eAAe;AACtC,OAAOC,MAAM,MAAM,kBAAkB;AACrC,SAASC,eAAe,QAAQ,yBAAyB;AACzD,SAASC,0BAA0B,QAAQ,sBAAsB;AACjE,OAAOC,aAAa,MAAM,yBAAyB;AACnD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC;EACF,CAAC,GAAGD,UAAU;EACd,MAAME,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM;EACf,CAAC;EACD,OAAOZ,cAAc,CAACW,KAAK,EAAEP,0BAA0B,EAAEM,OAAO,CAAC;AACnE,CAAC;AACD,MAAMG,eAAe,GAAGX,MAAM,CAACD,UAAU,EAAE;EACzCa,IAAI,EAAE,gBAAgB;EACtBC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAKA,MAAM,CAACN;AAC/C,CAAC,CAAC,CAAC;EACDO,OAAO,EAAE,WAAW;EACpBC,IAAI,EAAE;AACR,CAAC,CAAC;AACF,MAAMC,WAAW,GAAG,aAAaxB,KAAK,CAACyB,UAAU,CAAC,SAASD,WAAWA,CAACE,OAAO,EAAEC,GAAG,EAAE;EACnF,MAAMP,KAAK,GAAGd,eAAe,CAAC;IAC5Bc,KAAK,EAAEM,OAAO;IACdT,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;MACFW,SAAS;MACTC,EAAE,EAAEC;IACN,CAAC,GAAGV,KAAK;IACTW,KAAK,GAAGjC,6BAA6B,CAACsB,KAAK,EAAErB,SAAS,CAAC;EACzD,MAAMa,UAAU,GAAGQ,KAAK;EACxB,MAAMP,OAAO,GAAGF,iBAAiB,CAACC,UAAU,CAAC;EAC7C,MAAM;IACJoB,OAAO,GAAGF;EACZ,CAAC,GAAG9B,KAAK,CAACiC,UAAU,CAACzB,aAAa,CAAC;EACnC,OAAO,aAAaE,IAAI,CAACM,eAAe,EAAEnB,QAAQ,CAAC;IACjDqC,SAAS,EAAE,IAAI;IACfN,SAAS,EAAE1B,IAAI,CAACW,OAAO,CAACE,IAAI,EAAEa,SAAS,CAAC;IACxChB,UAAU,EAAEA,UAAU;IACtBe,GAAG,EAAEA,GAAG;IACRQ,OAAO,EAAE,IAAI;IACbN,EAAE,EAAEC,MAAM,IAAI,IAAI,GAAGA,MAAM,GAAGE;EAChC,CAAC,EAAED,KAAK,CAAC,CAAC;AACZ,CAAC,CAAC;AACFK,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGd,WAAW,CAACe,SAAS,CAAC,yBAAyB;EACrF;EACA;EACA;EACA;EACA;AACF;AACA;EACEC,QAAQ,EAAEvC,SAAS,CAACwC,IAAI;EACxB;AACF;AACA;EACE5B,OAAO,EAAEZ,SAAS,CAACyC,MAAM;EACzB;AACF;AACA;EACEd,SAAS,EAAE3B,SAAS,CAAC0C,MAAM;EAC3B;AACF;AACA;EACEd,EAAE,EAAE5B,SAAS,CAAC0C,MAAM;EACpB;AACF;AACA;EACEC,EAAE,EAAE3C,SAAS,CAAC4C,SAAS,CAAC,CAAC5C,SAAS,CAAC6C,OAAO,CAAC7C,SAAS,CAAC4C,SAAS,CAAC,CAAC5C,SAAS,CAAC8C,IAAI,EAAE9C,SAAS,CAACyC,MAAM,EAAEzC,SAAS,CAAC+C,IAAI,CAAC,CAAC,CAAC,EAAE/C,SAAS,CAAC8C,IAAI,EAAE9C,SAAS,CAACyC,MAAM,CAAC;AACxJ,CAAC,GAAG,KAAK,CAAC;AACV,eAAelB,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}