# websocket_server.py - Refactored for bi-directional audio streaming protocol

import asyncio
import json
import time
from flask import Flask, jsonify
from flask_socketio import Socket<PERSON>
from typing import Optional, Type, Dict, Any

import websockets
from websockets.exceptions import ConnectionClosedError
from tutor.executors.user import User
from tutor.modules.logger import custom_handler, logger
from tutor.modules.websocket import (
    EventType, ConnectedEvent, StartEvent, MediaEvent, StopEvent,
    DTMFEvent, MarkEvent, ClearEvent, EventFactory, StreamManager,
    AudioCodec
)


# Initialize logger
handler = custom_handler()
logger.info("Switching to %s", handler.baseFilename)
logger.addHandler(hdlr=handler)


# List to store active users and stream manager
active_users = []
stream_manager = StreamManager()
audio_codec = AudioCodec()

def find_user_by_session(session: str) -> Optional[User]:
    """Find a user by their session ID."""
    for user in active_users:
        if user.session == session:
            return user
    return None


def find_user_by_stream_id(stream_sid: str) -> Optional[User]:
    """Find a user by their stream ID."""
    for user in active_users:
        if hasattr(user, 'stream_id') and user.stream_id == stream_sid:
            return user
    return None


async def handle_protocol_event(websocket, event_data: Dict[str, Any]) -> None:
    """Handle incoming WebSocket events based on bi-directional streaming protocol"""
    try:
        event_type = event_data.get("event")
        
        if event_type == EventType.CONNECTED.value:
            await handle_connected_event(websocket, event_data)
        
        elif event_type == EventType.MEDIA.value:
            await handle_media_event(websocket, event_data)
        
        elif event_type == EventType.MARK.value:
            await handle_mark_event(websocket, event_data)
        
        elif event_type == EventType.CLEAR.value:
            await handle_clear_event(websocket, event_data)
        
        elif event_type == EventType.DTMF.value:
            await handle_dtmf_event(websocket, event_data)
            
        else:
            logger.warning(f"Unknown protocol event type: {event_type}")
            
    except Exception as e:
        logger.error(f"Error handling protocol event: {e}")





async def handle_connected_event(websocket, event_data: Dict[str, Any]) -> None:
    """Handle connected event - respond with connected acknowledgment"""
    try:
        # Send connected response
        connected_event = ConnectedEvent()
        await websocket.send(connected_event.to_json())
        logger.info("Sent connected event response")
    except Exception as e:
        logger.error(f"Error handling connected event: {e}")


async def handle_media_event(websocket, event_data: Dict[str, Any]) -> None:
    """Handle incoming media event with audio data"""
    try:
        stream_sid = event_data.get("streamSid")
        media = event_data.get("media", {})
        
        if not stream_sid:
            logger.warning("Media event missing streamSid")
            return
            
        # Find user by stream ID
        user = find_user_by_stream_id(stream_sid)
        if not user:
            logger.warning(f"No user found for streamSid: {stream_sid}")
            return
            
        # Decode base64 audio payload
        payload_b64 = media.get("payload", "")
        if payload_b64:
            try:
                audio_data = audio_codec.decode_audio_from_base64(payload_b64)
                user.audio_processor.add_protocol_audio(audio_data)
                
                # Update chunk and timestamp tracking
                chunk = int(media.get("chunk", 0))
                timestamp = int(media.get("timestamp", 0))
                user.update_media_stats(chunk, timestamp)
                
            except Exception as e:
                logger.error(f"Error processing media payload: {e}")
                
    except Exception as e:
        logger.error(f"Error handling media event: {e}")


async def handle_mark_event(websocket, event_data: Dict[str, Any]) -> None:
    """Handle mark event for audio synchronization"""
    try:
        stream_sid = event_data.get("streamSid")
        mark = event_data.get("mark", {})
        mark_name = mark.get("name", "")
        
        user = find_user_by_stream_id(stream_sid)
        if user:
            # Handle mark completion (audio playback finished)
            user.handle_mark_completion(mark_name)
            logger.info(f"Processed mark event: {mark_name} for stream {stream_sid}")
        else:
            logger.warning(f"No user found for mark event with streamSid: {stream_sid}")
            
    except Exception as e:
        logger.error(f"Error handling mark event: {e}")


async def handle_clear_event(websocket, event_data: Dict[str, Any]) -> None:
    """Handle clear event to interrupt audio buffer"""
    try:
        stream_sid = event_data.get("streamSid")
        
        user = find_user_by_stream_id(stream_sid)
        if user:
            # Clear audio buffer and send pending marks
            user.clear_audio_buffer()
            logger.info(f"Processed clear event for stream {stream_sid}")
        else:
            logger.warning(f"No user found for clear event with streamSid: {stream_sid}")
            
    except Exception as e:
        logger.error(f"Error handling clear event: {e}")


async def handle_dtmf_event(websocket, event_data: Dict[str, Any]) -> None:
    """Handle DTMF (touch-tone) event"""
    try:
        stream_sid = event_data.get("streamSid")
        dtmf = event_data.get("dtmf", {})
        digit = dtmf.get("digit", "")
        
        user = find_user_by_stream_id(stream_sid)
        if user:
            # Process DTMF input
            user.handle_dtmf_input(digit)
            logger.info(f"Processed DTMF event: {digit} for stream {stream_sid}")
        else:
            logger.warning(f"No user found for DTMF event with streamSid: {stream_sid}")
            
    except Exception as e:
        logger.error(f"Error handling DTMF event: {e}")





async def start_protocol_stream(websocket, user) -> None:
    """Start a new protocol-compliant stream"""
    try:
        # Create new stream
        stream_sid = stream_manager.create_stream(
            from_number=user.mobile,
            to_number="SYSTEM",
            custom_parameters={
                "name": user.name,
                "session": user.session,
                "userId": user.user_id
            }
        )
        
        # Associate stream with user
        user.set_stream_id(stream_sid)
        
        # Get stream metadata and send start event
        metadata = stream_manager.get_stream_metadata(stream_sid)
        if metadata:
            sequence_num = stream_manager.get_next_sequence_number(stream_sid)
            start_event = StartEvent(sequence_num, metadata)
            await websocket.send(start_event.to_json())
            
            # Start AI call with protocol support
            user.start_ai_call_with_protocol()
            
            logger.info(f"Started protocol stream {stream_sid} for user {user.name}")
        else:
            logger.error(f"Failed to get metadata for stream {stream_sid}")
            
    except Exception as e:
        logger.error(f"Error starting protocol stream: {e}")


async def end_protocol_stream(websocket, user, reason: str) -> None:
    """End a protocol-compliant stream"""
    try:
        stream_sid = getattr(user, 'stream_id', None)
        if not stream_sid:
            logger.warning(f"No stream ID found for user {user.name}")
            return
            
        # Get stream metadata for stop event
        metadata = stream_manager.get_stream_metadata(stream_sid)
        if metadata:
            sequence_num = stream_manager.get_next_sequence_number(stream_sid)
            stop_event = StopEvent(
                sequence_num, stream_sid, 
                metadata.accountSid, metadata.callSid, reason
            )
            await websocket.send(stop_event.to_json())
            
        # End stream in manager
        stream_manager.end_stream(stream_sid, reason)
        
        # End AI call
        user.end_ai_call()
        logger.info(f"user name: {user.name} WebSocket connection closed")
        
        # Remove user from active users
        try:
            active_users.remove(user)
        except ValueError as e:
            logger.error(f"Error removing user: {e}")
            
        # Cleanup stream
        stream_manager.cleanup_stream(stream_sid)
        
    except Exception as e:
        logger.error(f"Error ending protocol stream: {e}")


async def on_request(websocket, path):
    """Handle incoming WebSocket requests with protocol support."""
    try:
        async for message in websocket:
            try:
                data = json.loads(message)

                # Handle protocol-compliant events only
                if "event" in data:
                    if data.get("event") not in ["media", "connected"]:
                        logger.debug(f"Protocol event: {data}")
                    await handle_protocol_event(websocket, data)
                else:
                    # Unknown message format
                    logger.warning(f"Unknown message format received: {data}")
                    await websocket.send(json.dumps({"type": "error", "message": "Unknown message format. Please use protocol-compliant events."}))

            except json.JSONDecodeError as e:
                logger.error(f"JSON decode error: {e}")
                await websocket.send(json.dumps({"type": "error", "message": "Invalid JSON format"}))
            except KeyError as e:
                logger.error(f"Missing key in data: {e}")
                await websocket.send(json.dumps({"type": "error", "message": "Missing key in data"}))
            except Exception as e:
                logger.error(f"Unexpected error: {e}")
                await websocket.send(json.dumps({"type": "error", "message": "Unexpected error occurred"}))
    except ConnectionClosedError as e:
        logger.warning(f"Connection closed: {e}")
    except Exception as e:
        logger.error(f"Error in WebSocket connection: {e}")


def websocket_server() -> None:
    # Start WebSocket server
    start_server = websockets.serve(on_request, "0.0.0.0", 5010)

    # Run the server
    print("Starting WebSocket server with asyncio event loop")
    loop = asyncio.get_event_loop()
    loop.run_until_complete(start_server)
    loop.run_forever()